FROM mcr.microsoft.com/dotnet/core/sdk:2.1-stretch AS build
WORKDIR /src

ARG GH_USERNAME
ARG GH_TOKEN

ENV GH_USERNAME=${GH_USERNAME}
ENV GH_TOKEN=${GH_TOKEN}

COPY NuGet.Config ./
COPY Motivai.Catalogs.sln ./
COPY ./Motivai.Catalogs.Domain/ Motivai.Catalogs.Domain/
COPY ./Motivai.Catalogs.Repository/ Motivai.Catalogs.Repository/
COPY ./Motivai.Catalogs.App/ Motivai.Catalogs.App/
COPY ./Motivai.Catalogs.Api/ Motivai.Catalogs.Api/
COPY ./Motivai.Catalogs.Test/ Motivai.Catalogs.Test/

RUN dotnet restore Motivai.Catalogs.sln
RUN dotnet test Motivai.Catalogs.Test/Motivai.Catalogs.Test.csproj

WORKDIR /src/Motivai.Catalogs.Api
RUN dotnet build Motivai.Catalogs.Api.csproj -c Release -o /app/build

FROM build AS publish
RUN dotnet publish Motivai.Catalogs.Api.csproj -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/core/aspnet:2.1-stretch-slim AS base
WORKDIR /opt/ApiCatalogs
EXPOSE 80
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Motivai.Catalogs.Api.dll"]