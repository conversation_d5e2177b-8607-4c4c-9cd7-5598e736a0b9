using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.Catalogs.Test.Tests.Helpers.Products
{
    public static class ProductScenarioBuilder
    {
        public static ProductItem BuildOnlineItem(ProductType productType = ProductType.Produto)
        {
            return new ProductItem()
            {
                ProductType = productType,
                ProcessType = ProcessType.Online,
                PartnerId = Constants.PARTNER_ID,
                ProductId = Constants.PRODUCT_ID,
                SkuId = Constants.SKU_ID,
                ElasticsearchId = Constants.ELASTICSEARCH_ID,
                SkuCode = Constants.SKU_CODE,
                Quantity = 1
            };
        }

        public static ProductItem BuildOnlineProduct()
        {
            return BuildOnlineItem();
        }

        public static ProductItem BuildOnlineVirtualVoucher()
        {
            return BuildOnlineItem(ProductType.ValeVirtual);
        }
    }
}