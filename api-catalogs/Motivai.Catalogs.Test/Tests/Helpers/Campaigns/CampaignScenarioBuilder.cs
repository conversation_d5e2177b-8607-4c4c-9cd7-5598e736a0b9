using Motivai.Catalogo.Domain.IRepository;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums;

using Moq;

namespace Motivai.Catalogs.Test.Tests.Helpers.Campaigns
{
    public static class CampaignScenarioBuilder
    {
        public static CampaignFees CreateCampaignFees(double pointsConversionFactor, double? clientSpecificPartnerFee = 0)
        {
            return CampaignFactorModelBuilder.Create()
                .WithPointsConversionFactor(pointsConversionFactor)
                .WithClientPartnerFee(Constants.PARTNER_ID, clientSpecificPartnerFee)
                .Build();
        }

        public static void MockGetSettingsWithShippingCalculation(Mock<ICampaignRepository> mockedRepository)
        {
            MockGetSettingsWith(
                mockedRepository,
                new ParametrizationsModel()
                {
                    EnableShippingCalculation = true,
                    EnableEmbeddedShippingCost = false
                }
            );
        }

        public static void MockGetSettingsWith(Mock<ICampaignRepository> mockedRepository,
            ParametrizationsModel parametrizations)
        {
            mockedRepository.Setup(m => m.GetSettings(Constants.CAMPAIGN_ID))
                .ReturnsAsync(new CampaignSettingsModel()
                {
                    Type = CampaignType.Rewards,
                    Active = true,
                    CoinType = CampaignCoinType.Pontos,
                    Parametrizations = parametrizations
                });
        }
    }
}