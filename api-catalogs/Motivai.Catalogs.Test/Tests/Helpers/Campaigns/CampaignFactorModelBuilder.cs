using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.SharedKernel.Domain.Entities.References.Campaign;

namespace Motivai.Catalogs.Test.Tests.Helpers.Campaigns
{
    public class CampaignFactorModelBuilder
    {
        private decimal pointsConversionFactor;
        private decimal productConversionFactor;
        private List<PartnerFee> PartnersFees;

        private CampaignFactorModelBuilder()
        {
        }

        public static CampaignFactorModelBuilder Create()
        {
            return new CampaignFactorModelBuilder();
        }

        public CampaignFactorModelBuilder WithPointsConversionFactor(double pointsConversionFactor)
        {
            this.pointsConversionFactor = (decimal)pointsConversionFactor;
            return this;
        }

        public CampaignFactorModelBuilder WithClientPartnersFee(double productConversionFactor)
        {
            this.productConversionFactor = (decimal)productConversionFactor;
            return this;
        }

        public CampaignFactorModelBuilder WithClientPartnerFee(Guid partnerId, double? administrativeTax)
        {
            if (PartnersFees == null)
                PartnersFees = new List<PartnerFee>();
            PartnersFees.Add(new PartnerFee()
            {
                PartnerId = partnerId,
                Fee = administrativeTax.HasValue ? (decimal?)administrativeTax.Value : null
            });
            return this;
        }

        public CampaignFees Build()
        {
            return new CampaignFees()
            {
                PointsConversionFactor = pointsConversionFactor,
                ClientPartnersFee = productConversionFactor,
                ClientPartnersSpecificFees = PartnersFees
            };
        }

        public Task<CampaignFees> BuildForAsync()
        {
            return Task.FromResult(Build());
        }
    }
}