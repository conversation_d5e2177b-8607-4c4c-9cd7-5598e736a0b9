using System;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IRepository.GeneralSettings;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.IRepository.ShoppingCarts;
using Motivai.Catalogs.Domain.Models.GeneralSettings;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Prices;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Services.Products.Stock;
using Motivai.Catalogs.Domain.Services.Products.Stocks;
using Motivai.Catalogs.Domain.Services.Shippings;
using Motivai.Catalogs.Test.Tests.Helpers;
using Motivai.Catalogs.Test.Tests.Helpers.Campaigns;
using Motivai.Catalogs.Test.Tests.Helpers.Products;
using Motivai.Catalogs.Test.Utils;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;

using Moq;

using Xunit;

namespace Motivai.Catalogs.Test.Tests.Domain.Services.Products
{
    public class CatalogProductServiceTests : BaseTest
    {
        private readonly Mock<IPlatformGeneralSettingsRepository> _platformSettingsRepositoryMock;
        private readonly Mock<ICampaignRepository> _campaignRepositoryMock;

        private readonly CatalogProductService _productService;

        public CatalogProductServiceTests()
        {
            _platformSettingsRepositoryMock = new Mock<IPlatformGeneralSettingsRepository>();

            _campaignRepositoryMock = new Mock<ICampaignRepository>();
            var companyRepository = new Mock<ICompanyRepository>().Object;
            var shoppignCartRepository = new Mock<IShoppingCartRepository>().Object;
            var stockService = new Mock<PartnerOnlineStockService>(null, null, null, null).Object;
            var onlineStockService = new Mock<PartnerOnlineStockService>(null, null, null, null).Object;

            var productIntegrationRepository = new Mock<IProductIntegrationRepository>().Object;


            var catalogShippingService = new Mock<CatalogShippingService>(
                companyRepository, _campaignRepositoryMock.Object,
                new Mock<IOrderIntegrationRepository>().Object, new Mock<ICorreiosRepository>().Object
            ).Object;

            _productService = new CatalogProductService(
                _platformSettingsRepositoryMock.Object,
                catalogShippingService,
                new Mock<DiscountCalculator>(_campaignRepositoryMock.Object).Object,
                new Mock<CampaignStockService>(_campaignRepositoryMock.Object, companyRepository,
                    shoppignCartRepository, stockService).Object,
                new Mock<ProductPointsPurchaseCalculator>(null, null).Object,
                _campaignRepositoryMock.Object,
                companyRepository,
                onlineStockService, productIntegrationRepository
            );
        }

        [Theory]
        [InlineData(1.0, null, 100, 0, 100)]
        [InlineData(0.5, null, 200, 0, 200)]
        [InlineData(1.0, 0.2, 100, 20, 120)]
        [InlineData(1.0, 0.08, 100, 8, 108)]
        public void ShouldCalcuateSkuPriceUsingFactorAndTax(double pointsConversionFactor,
            double? clientSpecificPartnerFee,
            decimal expectedPriceCost, decimal expectedClientCost, decimal expectedSalePrice)
        {
            CampaignScenarioBuilder.MockGetSettingsWithShippingCalculation(_campaignRepositoryMock);

            CampaignFees campaignFactors =
                CampaignScenarioBuilder.CreateCampaignFees(pointsConversionFactor, clientSpecificPartnerFee);
            Price price = new Price() { SaleValue = 100m, PromotionalValue = 0m, ApplyConversionFactor = true };

            var prices = SkuPrice.Of(price);
            var item = ProductScenarioBuilder.BuildOnlineProduct();

            var calculatedPrices = _productService
                .CalculateItemPrice(Constants.CAMPAIGN_ID, item, prices, campaignFactors).Result;
            Assert.Equal(expectedSalePrice, calculatedPrices.Price);
            Assert.Equal(100m, calculatedPrices.DetailedPrice.CostPrice.GetCurrencyOrZero());
            Assert.Equal(expectedPriceCost, calculatedPrices.DetailedPrice.CostPrice.GetPointsOrZero());
            Assert.Equal(expectedClientCost, calculatedPrices.DetailedPrice.ClientPartnerFee.GetPointsOrZero());
            Assert.Equal(expectedSalePrice, calculatedPrices.DetailedPrice.SalePrice.GetPointsOrZero());
        }

        [Theory]
        [InlineData("Produto", 1.0, 0.0, 100, 0, 100)]
        [InlineData("Produto", 0.5, 0.0, 100, 0, 200)]
        [InlineData("Produto", 1.0, 1.5, 100, 0, 100)]
        [InlineData("Produto", 0.5, 1.5, 100, 0, 200)]
        [InlineData("ValeFisico", 1.0, 0.0, 100, 0, 100)]
        [InlineData("ValeFisico", 1.0, 1.5, 100, 0, 100)]
        [InlineData("ValeVirtual", 1.0, 0.0, 100, 0, 100)]
        [InlineData("ValeVirtual", 1.0, 1.5, 100, 1.5, 101.5)]
        [InlineData("ValeVirtual", 0.5, 2.5, 100, 5, 205)]
        public void ShouldCalculateSkuPriceWithPlatformCost(string productType, double conversionFactor,
            decimal feeAmount, decimal productCostCurrency,
            decimal expectedFeePoints, decimal expectedPricePoints)
        {
            CampaignScenarioBuilder.MockGetSettingsWith(_campaignRepositoryMock,
                new ParametrizationsModel() { EnableShippingCalculation = true, EnableEmbeddedShippingCost = false }
            );

            _platformSettingsRepositoryMock.Setup(r => r.GetActiveRiskAssessmentSettings())
                .ReturnsAsync(new RiskAssessmentSettings() { AnalysisFeeAmount = feeAmount });

            var campaignFactors = CampaignScenarioBuilder.CreateCampaignFees(conversionFactor);
            var item = ProductScenarioBuilder.BuildOnlineItem(EnumHelper<ProductType>.Parse(productType));

            Price price = new Price()
            {
                SaleValue = productCostCurrency, PromotionalValue = 0m, ApplyConversionFactor = true
            };
            var prices = SkuPrice.Of(price);

            var calculatedPrices = _productService
                .CalculateItemPrice(Constants.CAMPAIGN_ID, item, prices, campaignFactors).Result;
            Assert.Equal(expectedPricePoints, calculatedPrices.Price);
            Assert.Equal(expectedFeePoints, calculatedPrices.DetailedPrice.RiskAssessmentFee.GetPointsOrZero());
        }

        [Theory]
        [InlineData("Produto", 1.0, 0.0, 100, 0, 100)]
        [InlineData("Produto", 0.5, 0.0, 100, 0, 200)]
        [InlineData("Produto", 1.0, 1.5, 100, 0, 100)]
        [InlineData("Produto", 0.5, 1.5, 100, 0, 200)]
        [InlineData("ValeFisico", 1.0, 0.0, 100, 0, 100)]
        [InlineData("ValeFisico", 1.0, 1.5, 100, 0, 100)]
        [InlineData("ValeVirtual", 1.0, 0.0, 100, 0, 100)]
        [InlineData("ValeVirtual", 1.0, 1.5, 100, 1.5, 101.5)]
        [InlineData("ValeVirtual", 0.5, 2.5, 100, 5, 205)]
        public void ShouldCalculateSkuPriceWithCustomPromotionalPriceAndPlatformCost(string productType,
            double conversionFactor,
            decimal feeAmount, decimal productCostCurrency,
            decimal expectedFeePoints, decimal expectedPricePoints)
        {
            CampaignScenarioBuilder.MockGetSettingsWith(_campaignRepositoryMock,
                new ParametrizationsModel() { EnableShippingCalculation = true, EnableEmbeddedShippingCost = false }
            );

            _platformSettingsRepositoryMock.Setup(r => r.GetActiveRiskAssessmentSettings())
                .ReturnsAsync(new RiskAssessmentSettings() { AnalysisFeeAmount = feeAmount });

            var campaignFactors = CampaignScenarioBuilder.CreateCampaignFees(conversionFactor);
            var item = ProductScenarioBuilder.BuildOnlineItem(EnumHelper<ProductType>.Parse(productType));

            Price price = new Price()
            {
                SaleValue = 1000,
                PromotionalValue = 0m,
                ApplyConversionFactor = true,
                EnableCustomPromotionalPrice = true,
                CustomPromotionalPrice = new CustomPromotionalPrice()
                {
                    PriceFor = productCostCurrency,
                    PriceFrom = 0,
                    StartDate = DateTime.Today.AddDays(-1),
                    EndDate = DateTime.Today.AddDays(1)
                }
            };
            var prices = SkuPrice.Of(price);

            var calculatedPrices = _productService
                .CalculateItemPrice(Constants.CAMPAIGN_ID, item, prices, campaignFactors).Result;
            Assert.Equal(expectedPricePoints, calculatedPrices.Price);
            Assert.Equal(expectedFeePoints, calculatedPrices.DetailedPrice.RiskAssessmentFee.GetPointsOrZero());
        }

        [Theory]
        [InlineData("Produto", 1.0, 0.0, 100, 0, 100)]
        [InlineData("Produto", 0.5, 0.0, 100, 0, 200)]
        [InlineData("Produto", 1.0, 1.5, 100, 0, 100)]
        [InlineData("Produto", 0.5, 1.5, 100, 0, 200)]
        [InlineData("ValeFisico", 1.0, 0.0, 100, 0, 100)]
        [InlineData("ValeFisico", 1.0, 1.5, 100, 0, 100)]
        [InlineData("ValeVirtual", 1.0, 0.0, 100, 0, 100)]
        [InlineData("ValeVirtual", 1.0, 1.5, 100, 1.5, 101.5)]
        [InlineData("ValeVirtual", 0.5, 2.5, 100, 5, 205)]
        public void ShouldNotCalculateSkuPriceWithCustomPromotionalPriceAndPlatformCost(string productType,
            double conversionFactor,
            decimal feeAmount, decimal productCostCurrency,
            decimal expectedFeePoints, decimal expectedPricePoints)
        {
            CampaignScenarioBuilder.MockGetSettingsWith(_campaignRepositoryMock,
                new ParametrizationsModel() { EnableShippingCalculation = true, EnableEmbeddedShippingCost = false }
            );

            _platformSettingsRepositoryMock.Setup(r => r.GetActiveRiskAssessmentSettings())
                .ReturnsAsync(new RiskAssessmentSettings() { AnalysisFeeAmount = feeAmount });

            var campaignFactors = CampaignScenarioBuilder.CreateCampaignFees(conversionFactor);
            var item = ProductScenarioBuilder.BuildOnlineItem(EnumHelper<ProductType>.Parse(productType));

            Price price = new Price()
            {
                SaleValue = productCostCurrency,
                PromotionalValue = 0m,
                ApplyConversionFactor = true,
                EnableCustomPromotionalPrice = true,
                CustomPromotionalPrice = new CustomPromotionalPrice()
                {
                    PriceFor = 1000,
                    PriceFrom = 0,
                    StartDate = DateTime.Today.AddDays(-10),
                    EndDate = DateTime.Today.AddDays(-5)
                }
            };
            var prices = SkuPrice.Of(price);

            var calculatedPrices = _productService
                .CalculateItemPrice(Constants.CAMPAIGN_ID, item, prices, campaignFactors).Result;
            Assert.Equal(expectedPricePoints, calculatedPrices.Price);
            Assert.Equal(expectedFeePoints, calculatedPrices.DetailedPrice.RiskAssessmentFee.GetPointsOrZero());
        }
    }
}