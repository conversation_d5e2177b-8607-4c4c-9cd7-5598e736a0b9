using System;

using Motivai.Catalogs.Domain.Services.Prices;
using Motivai.Catalogs.Test.Tests.Helpers.Campaigns;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;

using Xunit;

namespace Motivai.Catalogs.Test.Tests.Domain.Services.Prices
{
    public class PriceCalculatorTest
    {
        private static readonly Guid CAMPAIGN_ID = Guid.Parse("387d5974-4103-4b2a-983c-c6a9ac47d4f2");
        private static readonly Guid PARTNER_ID = Guid.Parse("3e2ab787-2109-43dd-857f-36ef9cf68f73");

        private CampaignFactorModel CreateMockedCampaignFactorModel(Guid campaignId, decimal productConversionFactor)
        {
            return new CampaignFactorModel()
            {
                CampaignId = campaignId,
                ProductConversionFactor = productConversionFactor
            };
        }

        [Fact]
        public void ShouldCalculatePriceWithFactorEqualsToOne()
        {
            const bool USE_FACTOR = true;
            const decimal productCurrencyValue = 100;

            var campaignFees = CampaignFactorModelBuilder.Create()
                .WithPointsConversionFactor(0.02)
                .WithClientPartnersFee(0.1)
                .WithClientPartnerFee(PARTNER_ID, 0)
                .Build();

            var priceCalculator = SalePriceCalculator.WithFees(campaignFees);
            var item = ItemCalculation.OfSku(PARTNER_ID, Guid.Empty, ProcessType.Online);

            var calculatedPrice = priceCalculator.CalculatePricePoints(item, productCurrencyValue, USE_FACTOR);
            Assert.Equal((decimal)5500.0, calculatedPrice.GetSalePrice().GetPoints());
        }

        [Theory]
        [InlineData(1.0, 0.0, 0, 260.5, 260.5)]
        [InlineData(1.0, 1.0, null, 521.0, 521.0)]
        [InlineData(1.0, 0.75, 0, 455.88, 455.88)]
        [InlineData(0.40, 0.5, 0, 390.75, 976.87)]
        [InlineData(0.05, 0.21, 0.1, 346.72, 6934.51)]
        [InlineData(0.75, 0.35, 0.15, 404.43, 539.24)]
        public void ShouldCalculatePriceUsingFactor(double pointsConversionFactor, double clientPartnersFee,
            double? clientSpecificPartnerFee, decimal expectedPriceCurrency, decimal expectedPricePoints)
        {
            const bool USE_FACTOR = true;
            const decimal PRODUCT_CURRENCY_VALUE = (decimal)260.50;

            var campaignFactors = CampaignFactorModelBuilder.Create()
                .WithPointsConversionFactor(pointsConversionFactor)
                .WithClientPartnersFee(clientPartnersFee)
                .WithClientPartnerFee(PARTNER_ID, clientSpecificPartnerFee)
                .Build();

            var priceCalculator = SalePriceCalculator.WithFees(campaignFactors);

            var item = ItemCalculation.OfSku(PARTNER_ID, Guid.Empty, ProcessType.Online);

            var calculatedPrice = priceCalculator.CalculatePricePoints(item, PRODUCT_CURRENCY_VALUE, USE_FACTOR);

            Assert.Equal(expectedPriceCurrency, calculatedPrice.GetSalePrice().GetCurrency());
            Assert.Equal(expectedPricePoints, calculatedPrice.GetSalePrice().GetPoints());
        }
    }
}