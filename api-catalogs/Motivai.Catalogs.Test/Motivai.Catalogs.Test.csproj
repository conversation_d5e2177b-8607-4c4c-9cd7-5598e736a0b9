<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netcoreapp2.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appsettings.json" CopyToOutputDirectory="Always" />
    <Content Include="appsettings.Test.json" CopyToOutputDirectory="Always" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.0.0" />
    <PackageReference Include="xunit" Version="2.2.0" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.2.0" />

    <PackageReference Include="Moq" Version="4.7.99" />
  </ItemGroup>

    <ItemGroup>
    <ProjectReference Include="..\Motivai.Catalogs.App\Motivai.Catalogs.App.csproj" />
    <ProjectReference Include="..\Motivai.Catalogs.Domain\Motivai.Catalogs.Domain.csproj" />
    <ProjectReference Include="..\Motivai.Catalogs.Repository\Motivai.Catalogs.Repository.csproj" />
  </ItemGroup>

</Project>
