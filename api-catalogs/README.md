# Motivai Catalogs API

API de Catálogos da plataforma Motivai - Sistema de gerenciamento de catálogos de produtos, campanhas e serviços para programas de incentivo e fidelidade.

## 📋 Resumo

A **Motivai Catalogs API** é uma API REST desenvolvida em .NET Core que gerencia catálogos de produtos, campanhas promocionais, carrinho de compras, pedidos e serviços extras para a plataforma Motivai. O sistema oferece funcionalidades completas para e-commerce B2B, incluindo gestão de estoque, cálculo de preços, integração com gateways de pagamento e processamento de pedidos.

## 🚀 Tecnologias Utilizadas

### Framework e Runtime
- **.NET Core 2.1** - Framework principal da aplicação
- **ASP.NET Core 2.1** - Framework web para APIs REST
- **C#** - Linguagem de programação

### Banco de Dados e Cache
- **MongoDB** - Banco de dados NoSQL principal
- **Redis** - Cache distribuído e sessões

### Infraestrutura e DevOps
- **Docker** - Containerização da aplicação
- **Kubernetes** - Orquestração de containers
- **Helm** - Gerenciamento de deployments no Kubernetes
- **AWS ECR** - Registry de imagens Docker

### Logging e Monitoramento
- **NLog** - Sistema de logging estruturado
- **Health Checks** - Monitoramento de saúde da aplicação

### Ferramentas de Desenvolvimento
- **xUnit** - Framework de testes unitários
- **dotnet watch** - Hot reload para desenvolvimento

## 🏗️ Arquitetura

O projeto segue os princípios da **Arquitetura Limpa (Clean Architecture)** e **Domain-Driven Design (DDD)**, organizando o código em camadas bem definidas:

### Estrutura de Camadas

```
├── Motivai.Catalogs.Api/          # 🌐 Camada de Apresentação
│   ├── Controllers/               # Controllers da API REST
│   ├── Program.cs                 # Ponto de entrada da aplicação
│   └── Startup.cs                 # Configuração de serviços e middleware
│
├── Motivai.Catalogs.App/          # 📱 Camada de Aplicação
│   ├── CampaignsCatalog/          # Casos de uso de campanhas
│   ├── Products/                  # Casos de uso de produtos
│   ├── Orders/                    # Casos de uso de pedidos
│   ├── Marketing/                 # Casos de uso de marketing
│   ├── ExtraServices/             # Casos de uso de serviços extras
│   └── ShoppingCart/              # Casos de uso de carrinho
│
├── Motivai.Catalogs.Domain/       # 🏛️ Camada de Domínio
│   ├── Entities/                  # Entidades de domínio
│   ├── Models/                    # Modelos de domínio
│   ├── Services/                  # Serviços de domínio
│   ├── IApp/                      # Interfaces da camada de aplicação
│   └── IRepository/               # Interfaces de repositórios
│
├── Motivai.Catalogs.Repository/   # 💾 Camada de Infraestrutura
│   ├── Integrations/              # Integrações externas
│   ├── PaymentGateway/            # Integração com gateways de pagamento
│   └── [Various]Repository.cs     # Implementações de repositórios
│
└── Motivai.Catalogs.Test/         # 🧪 Testes
    ├── Tests/                     # Testes unitários e integração
    └── Utils/                     # Utilitários para testes
```

### Padrões Arquiteturais Implementados

- **Repository Pattern** - Abstração da camada de dados
- **Dependency Injection** - Inversão de controle e injeção de dependências
- **CQRS (Command Query Responsibility Segregation)** - Separação de comandos e consultas
- **Domain Services** - Lógica de negócio complexa encapsulada em serviços
- **Factory Pattern** - Criação de objetos complexos
- **Strategy Pattern** - Algoritmos intercambiáveis (ex: cálculo de preços)

## 🎯 Principais Funcionalidades

### 📦 Gestão de Catálogos
- Catálogos de produtos por campanha
- Categorização hierárquica de produtos
- Gestão de SKUs e variações
- Controle de estoque online/offline
- Preços dinâmicos e promoções

### 🛒 E-commerce
- Carrinho de compras multi-parceiro
- Cálculo automático de frete
- Gestão de cupons de desconto
- Múltiplos métodos de pagamento
- Processamento de pedidos

### 🎪 Campanhas e Marketing
- Campanhas promocionais personalizadas
- MediaBox para conteúdo promocional
- SpecialShop para lojas temáticas
- Scripts customizados por campanha
- Segmentação por região

### 💳 Serviços Financeiros
- Compra de pontos/créditos
- Cartões pré-pagos
- Transferências bancárias
- Recarga de celular
- Pagamento de contas

### 🔧 Serviços Extras
- Integração com correios
- Notificações por email/SMS
- Relatórios e analytics
- Gestão de reembolsos
- Suporte a call center

## 🌐 Principais Endpoints

### Catálogos
- `GET /api/campaigns/{id}/catalog` - Obter catálogo da campanha
- `GET /api/products/{id}` - Detalhes do produto
- `GET /api/categories` - Listar categorias

### Carrinho e Pedidos
- `POST /api/shopping-cart/add` - Adicionar item ao carrinho
- `GET /api/shopping-cart` - Obter carrinho atual
- `POST /api/orders` - Finalizar pedido
- `GET /api/orders/{id}` - Acompanhar pedido

### Serviços
- `POST /api/points-purchase` - Comprar pontos
- `POST /api/extra-services/mobile-recharge` - Recarga de celular
- `POST /api/extra-services/bill-payment` - Pagamento de contas

## 🔧 Configuração e Execução

### Pré-requisitos
- .NET Core 2.1 SDK
- MongoDB
- Redis
- Docker (opcional)

### Desenvolvimento Local

1. **Clone o repositório**
```bash
git clone <repository-url>
cd api-catalogs
```

2. **Configure as variáveis de ambiente**
```bash
# Edite o arquivo appsettings.Local.json com suas configurações
cp Motivai.Catalogs.Api/appsettings.json Motivai.Catalogs.Api/appsettings.Local.json
```

3. **Execute a aplicação**
```bash
# Usando o script de desenvolvimento
./run_dev.sh

# Ou manualmente
cd Motivai.Catalogs.Api
export ASPNETCORE_ENVIRONMENT=Local
export ASPNETCORE_URLS="http://*:5020"
dotnet watch run
```

4. **Acesse a aplicação**
- API: http://localhost:5020
- Health Check: http://localhost:5020/healthy/ping

### Docker

```bash
# Build da imagem
./build_image.sh

# Ou manualmente
docker build -t motivai/api-catalogs .
docker run -p 5020:80 motivai/api-catalogs
```

### Testes

```bash
# Executar todos os testes
dotnet test Motivai.Catalogs.Test/

# Executar testes com coverage
dotnet test --collect:"XPlat Code Coverage"
```

## 🚀 Deploy

### Kubernetes com Helm

```bash
# Deploy em produção
./deploy_prod.sh

# Ou manualmente
helm upgrade --install api-catalogs ./helm \
  --values ./helm/values-prod.yaml \
  --namespace motivai-prod
```

### Variáveis de Ambiente Necessárias

```bash
# APIs Externas
AWS_X_API_KEY=<chave-api-aws>
ApiGeneralSettings=<url-api-configuracoes>
ApiUsuario=<url-api-usuarios>
ApiCampanha=<url-api-campanhas>
ApiProduto=<url-api-produtos>

# Banco de Dados
MONGODB_URI=<connection-string-mongodb>
MONGODB_DATABASE=<nome-database>
REDIS_CONNECTION_STRING=<connection-string-redis>

# Outros serviços
ApiCorreios=<url-api-correios>
ApiEmail=<url-api-email>
ApiPagamento=<url-api-pagamento>
```

## 📊 Monitoramento

### Health Checks
- **Endpoint**: `/healthy/ping`
- **Método**: GET
- **Resposta**: Status da aplicação e dependências

### Logs
- **Framework**: NLog
- **Formato**: JSON estruturado
- **Níveis**: Debug, Info, Warning, Error, Fatal
- **Destinos**: Console, Arquivo, Elasticsearch (opcional)

### Métricas
- CPU e memória via Kubernetes
- Requests por segundo
- Tempo de resposta
- Taxa de erro

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

### Padrões de Código
- Seguir convenções do C#/.NET
- Testes unitários obrigatórios para novas funcionalidades
- Documentação de APIs com XML comments
- Code review obrigatório

## 📄 Licença

Este projeto é propriedade da Motivai e está sob licença proprietária.

## 📞 Suporte

Para dúvidas e suporte técnico, entre em contato com a equipe de desenvolvimento da Motivai.

---

**Motivai Catalogs API** - Transformando programas de incentivo em experiências excepcionais! 🎯
