# Motivai Catalogs API

API de Catálogos da plataforma Motivai - Sistema de gerenciamento de catálogos de produtos, campanhas e serviços para programas de incentivo e fidelidade.

## 📋 Resumo

A **Motivai Catalogs API** é uma API REST desenvolvida em .NET Core que gerencia catálogos de produtos, campanhas promocionais, carrinho de compras, pedidos e serviços extras para a plataforma Motivai. O sistema oferece funcionalidades completas para e-commerce B2B, incluindo gestão de estoque, cálculo de preços, integração com gateways de pagamento e processamento de pedidos.

## 🚀 Tecnologias Utilizadas

### Framework e Runtime
- **.NET Core 2.1** - Framework principal da aplicação
- **ASP.NET Core 2.1** - Framework web para APIs REST
- **C#** - Linguagem de programação

### Banco de Dados e Cache
- **MongoDB** - Banco de dados NoSQL principal
- **Redis** - Cache distribuído e sessões

### Infraestrutura e DevOps
- **Docker** - Containerização da aplicação
- **Kubernetes** - Orquestração de containers
- **Helm** - Gerenciamento de deployments no Kubernetes
- **AWS ECR** - Registry de imagens Docker

### Logging e Monitoramento
- **NLog** - Sistema de logging estruturado
- **Health Checks** - Monitoramento de saúde da aplicação

### Ferramentas de Desenvolvimento
- **xUnit** - Framework de testes unitários
- **dotnet watch** - Hot reload para desenvolvimento

## 🏗️ Arquitetura

O projeto segue os princípios da **Arquitetura Limpa (Clean Architecture)** e **Domain-Driven Design (DDD)**, organizando o código em camadas bem definidas:

### Estrutura de Camadas

```
├── Motivai.Catalogs.Api/          # 🌐 Camada de Apresentação
│   ├── Controllers/               # Controllers da API REST
│   ├── Program.cs                 # Ponto de entrada da aplicação
│   └── Startup.cs                 # Configuração de serviços e middleware
│
├── Motivai.Catalogs.App/          # 📱 Camada de Aplicação
│   ├── CampaignsCatalog/          # Casos de uso de campanhas
│   ├── Products/                  # Casos de uso de produtos
│   ├── Orders/                    # Casos de uso de pedidos
│   ├── Marketing/                 # Casos de uso de marketing
│   ├── ExtraServices/             # Casos de uso de serviços extras
│   └── ShoppingCart/              # Casos de uso de carrinho
│
├── Motivai.Catalogs.Domain/       # 🏛️ Camada de Domínio
│   ├── Entities/                  # Entidades de domínio
│   ├── Models/                    # Modelos de domínio
│   ├── Services/                  # Serviços de domínio
│   ├── IApp/                      # Interfaces da camada de aplicação
│   └── IRepository/               # Interfaces de repositórios
│
├── Motivai.Catalogs.Repository/   # 💾 Camada de Infraestrutura
│   ├── Integrations/              # Integrações externas
│   ├── PaymentGateway/            # Integração com gateways de pagamento
│   └── [Various]Repository.cs     # Implementações de repositórios
│
└── Motivai.Catalogs.Test/         # 🧪 Testes
    ├── Tests/                     # Testes unitários e integração
    └── Utils/                     # Utilitários para testes
```

### Padrões Arquiteturais Implementados

- **Repository Pattern** - Abstração da camada de dados
- **Dependency Injection** - Inversão de controle e injeção de dependências
- **CQRS (Command Query Responsibility Segregation)** - Separação de comandos e consultas
- **Domain Services** - Lógica de negócio complexa encapsulada em serviços
- **Factory Pattern** - Criação de objetos complexos
- **Strategy Pattern** - Algoritmos intercambiáveis (ex: cálculo de preços)

## 🎯 Principais Funcionalidades

### 📦 Gestão de Catálogos
- Catálogos de produtos por campanha
- Categorização hierárquica de produtos
- Gestão de SKUs e variações
- Controle de estoque online/offline
- Preços dinâmicos e promoções

### 🛒 E-commerce
- Carrinho de compras multi-parceiro
- Cálculo automático de frete
- Gestão de cupons de desconto
- Múltiplos métodos de pagamento
- Processamento de pedidos

### 🎪 Campanhas e Marketing
- Campanhas promocionais personalizadas
- MediaBox para conteúdo promocional
- SpecialShop para lojas temáticas
- Scripts customizados por campanha
- Segmentação por região

### 💳 Serviços Financeiros
- Compra de pontos/créditos
- Cartões pré-pagos
- Transferências bancárias
- Recarga de celular
- Pagamento de contas

### 🔧 Serviços Extras
- Integração com correios
- Notificações por email/SMS
- Relatórios e analytics
- Gestão de reembolsos
- Suporte a call center

## 🌐 Principais Endpoints

### Catálogos
- `GET /api/campaigns/{id}/catalog` - Obter catálogo da campanha
- `GET /api/products/{id}` - Detalhes do produto
- `GET /api/categories` - Listar categorias

### Carrinho e Pedidos
- `POST /api/shopping-cart/add` - Adicionar item ao carrinho
- `GET /api/shopping-cart` - Obter carrinho atual
- `POST /api/orders` - Finalizar pedido
- `GET /api/orders/{id}` - Acompanhar pedido

### Serviços
- `POST /api/points-purchase` - Comprar pontos
- `POST /api/extra-services/mobile-recharge` - Recarga de celular
- `POST /api/extra-services/bill-payment` - Pagamento de contas

## 🔧 Como Rodar o Projeto

### Pré-requisitos

Antes de executar o projeto, certifique-se de ter as seguintes ferramentas instaladas:

#### Obrigatórios
- **.NET Core 2.1 SDK** - [Download aqui](https://dotnet.microsoft.com/download/dotnet/2.1)
- **MongoDB** - Banco de dados principal
- **Redis** - Cache e sessões
- **Git** - Controle de versão

#### Opcionais
- **Docker** - Para execução em containers
- **Visual Studio** ou **VS Code** - IDEs recomendadas

### Instalação das Dependências

#### 1. Instalar .NET Core 2.1 SDK

**Windows:**
```bash
# Baixe e instale do site oficial
# https://dotnet.microsoft.com/download/dotnet/2.1
```

**macOS:**
```bash
# Via Homebrew
brew install --cask dotnet-sdk

# Ou baixe do site oficial
```

**Linux (Ubuntu/Debian):**
```bash
# Adicionar repositório Microsoft
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb

# Instalar SDK
sudo apt-get update
sudo apt-get install -y dotnet-sdk-2.1
```

#### 2. Instalar MongoDB

**Windows:**
```bash
# Baixe e instale do site oficial
# https://www.mongodb.com/try/download/community
```

**macOS:**
```bash
# Via Homebrew
brew tap mongodb/brew
brew install mongodb-community
brew services start mongodb-community
```

**Linux (Ubuntu/Debian):**
```bash
# Importar chave pública
wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo apt-key add -

# Adicionar repositório
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list

# Instalar MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Iniciar serviço
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### 3. Instalar Redis

**Windows:**
```bash
# Via Chocolatey
choco install redis-64

# Ou baixe do GitHub: https://github.com/microsoftarchive/redis/releases
```

**macOS:**
```bash
# Via Homebrew
brew install redis
brew services start redis
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install redis-server

# Iniciar serviço
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### Configuração do Projeto

#### 1. Clone o Repositório
```bash
git clone <repository-url>
cd api-catalogs
```

#### 2. Verificar Instalação do .NET
```bash
dotnet --version
# Deve retornar uma versão 2.1.x
```

#### 3. Restaurar Dependências
```bash
# Restaurar pacotes NuGet para toda a solução
dotnet restore Motivai.Catalogs.sln

# Ou restaurar individualmente
dotnet restore Motivai.Catalogs.Api/Motivai.Catalogs.Api.csproj
dotnet restore Motivai.Catalogs.App/Motivai.Catalogs.App.csproj
dotnet restore Motivai.Catalogs.Domain/Motivai.Catalogs.Domain.csproj
dotnet restore Motivai.Catalogs.Repository/Motivai.Catalogs.Repository.csproj
dotnet restore Motivai.Catalogs.Test/Motivai.Catalogs.Test.csproj
```

#### 4. Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de configuração
cp Motivai.Catalogs.Api/appsettings.json Motivai.Catalogs.Api/appsettings.Local.json

# Editar configurações locais
nano Motivai.Catalogs.Api/appsettings.Local.json
```

**Exemplo de configuração mínima (appsettings.Local.json):**
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  },
  "MONGODB_URI": "mongodb://localhost:27017",
  "MONGODB_DATABASE": "motivai_catalogs_local",
  "REDIS_CONNECTION_STRING": "localhost:6379",
  "ApiGeneralSettings": "http://localhost:5000",
  "ApiUsuario": "http://localhost:5001",
  "ApiCampanha": "http://localhost:5002"
}
```

### Executando o Projeto

#### Opção 1: Script de Desenvolvimento (Recomendado)
```bash
# Dar permissão de execução (Linux/macOS)
chmod +x run_dev.sh

# Executar
./run_dev.sh
```

#### Opção 2: Comando Manual
```bash
cd Motivai.Catalogs.Api
export ASPNETCORE_ENVIRONMENT=Local
export ASPNETCORE_URLS="http://*:5020"
dotnet watch run
```

#### Opção 3: Execução Simples
```bash
cd Motivai.Catalogs.Api
dotnet run
```

### Verificação da Execução

#### 1. Verificar se a aplicação está rodando
```bash
# Health Check
curl http://localhost:5020/healthy/ping

# Ou acesse no navegador
# http://localhost:5020/healthy/ping
```

#### 2. Endpoints de Teste
- **API Base**: http://localhost:5020
- **Health Check**: http://localhost:5020/healthy/ping
- **Swagger** (se configurado): http://localhost:5020/swagger

#### 3. Logs da Aplicação
Os logs aparecerão no terminal onde a aplicação está executando. Para logs mais detalhados, verifique a pasta `logs/` no projeto.

### Executando com Docker

#### Pré-requisitos Docker
- **Docker** - [Instalar Docker](https://docs.docker.com/get-docker/)
- **Docker Compose** (opcional) - Para orquestração local

#### Opção 1: Docker Simples

**1. Build da Imagem**
```bash
# Usando script (recomendado)
chmod +x build_image.sh
./build_image.sh

# Ou manualmente
docker build -t motivai/api-catalogs \
  --build-arg GH_USERNAME=<seu-usuario-github> \
  --build-arg GH_TOKEN=<seu-token-github> .
```

**2. Executar Container**
```bash
# Executar com variáveis de ambiente
docker run -d \
  --name api-catalogs \
  -p 5020:80 \
  -e ASPNETCORE_ENVIRONMENT=Local \
  -e MONGODB_URI=mongodb://host.docker.internal:27017 \
  -e REDIS_CONNECTION_STRING=host.docker.internal:6379 \
  motivai/api-catalogs

# Verificar logs
docker logs api-catalogs

# Parar container
docker stop api-catalogs
docker rm api-catalogs
```

#### Opção 2: Docker Compose (Ambiente Completo)

**1. Criar docker-compose.yml**
```yaml
version: '3.8'
services:
  api-catalogs:
    build: .
    ports:
      - "5020:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Local
      - MONGODB_URI=mongodb://mongodb:27017
      - REDIS_CONNECTION_STRING=redis:6379
    depends_on:
      - mongodb
      - redis

  mongodb:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  mongodb_data:
```

**2. Executar Stack Completa**
```bash
# Subir todos os serviços
docker-compose up -d

# Verificar status
docker-compose ps

# Ver logs
docker-compose logs api-catalogs

# Parar todos os serviços
docker-compose down
```

### Troubleshooting

#### Problemas Comuns

**1. Erro: "dotnet command not found"**
```bash
# Verificar se .NET está no PATH
echo $PATH
which dotnet

# Reinstalar .NET Core 2.1 SDK
```

**2. Erro de Conexão com MongoDB**
```bash
# Verificar se MongoDB está rodando
sudo systemctl status mongod  # Linux
brew services list | grep mongodb  # macOS

# Testar conexão
mongo --eval "db.adminCommand('ismaster')"
```

**3. Erro de Conexão com Redis**
```bash
# Verificar se Redis está rodando
redis-cli ping  # Deve retornar "PONG"

# Verificar porta
netstat -tlnp | grep 6379
```

**4. Erro: "Unable to bind to http://localhost:5020"**
```bash
# Verificar se a porta está em uso
netstat -tlnp | grep 5020
lsof -i :5020  # macOS/Linux

# Usar porta diferente
export ASPNETCORE_URLS="http://*:5021"
```

**5. Erro de Dependências NuGet**
```bash
# Limpar cache do NuGet
dotnet nuget locals all --clear

# Restaurar novamente
dotnet restore --force
```

**6. Erro de Compilação**
```bash
# Limpar build anterior
dotnet clean

# Rebuild completo
dotnet build --configuration Debug
```

#### Verificação de Saúde do Sistema

**1. Script de Verificação**
```bash
#!/bin/bash
echo "=== Verificação do Ambiente ==="

echo "1. Verificando .NET Core..."
dotnet --version

echo "2. Verificando MongoDB..."
mongo --eval "print('MongoDB OK')" --quiet

echo "3. Verificando Redis..."
redis-cli ping

echo "4. Verificando portas..."
netstat -tlnp | grep -E "(5020|27017|6379)"

echo "5. Verificando dependências do projeto..."
cd Motivai.Catalogs.Api
dotnet restore --verbosity quiet
echo "Dependências OK"

echo "=== Verificação Concluída ==="
```

**2. Executar Verificação**
```bash
chmod +x check_environment.sh
./check_environment.sh
```

### Desenvolvimento Avançado

#### Hot Reload
```bash
# Usar dotnet watch para reload automático
cd Motivai.Catalogs.Api
dotnet watch run
```

#### Debug no VS Code
**1. Configurar launch.json**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": ".NET Core Launch (web)",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Motivai.Catalogs.Api/bin/Debug/netcoreapp2.1/Motivai.Catalogs.Api.dll",
      "args": [],
      "cwd": "${workspaceFolder}/Motivai.Catalogs.Api",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Local",
        "ASPNETCORE_URLS": "http://localhost:5020"
      }
    }
  ]
}
```

#### Perfil de Performance
```bash
# Executar com profiling
dotnet run --configuration Release

# Monitorar performance
dotnet counters monitor --process-id <PID>
```

## 🧪 Executando Testes

```bash
# Executar todos os testes
dotnet test Motivai.Catalogs.Test/

# Executar testes com coverage
dotnet test --collect:"XPlat Code Coverage"
```

## 🚀 Deploy

### Kubernetes com Helm

```bash
# Deploy em produção
./deploy_prod.sh

# Ou manualmente
helm upgrade --install api-catalogs ./helm \
  --values ./helm/values-prod.yaml \
  --namespace motivai-prod
```

### Variáveis de Ambiente Necessárias

```bash
# APIs Externas
AWS_X_API_KEY=<chave-api-aws>
ApiGeneralSettings=<url-api-configuracoes>
ApiUsuario=<url-api-usuarios>
ApiCampanha=<url-api-campanhas>
ApiProduto=<url-api-produtos>

# Banco de Dados
MONGODB_URI=<connection-string-mongodb>
MONGODB_DATABASE=<nome-database>
REDIS_CONNECTION_STRING=<connection-string-redis>

# Outros serviços
ApiCorreios=<url-api-correios>
ApiEmail=<url-api-email>
ApiPagamento=<url-api-pagamento>
```

## 📊 Monitoramento

### Health Checks
- **Endpoint**: `/healthy/ping`
- **Método**: GET
- **Resposta**: Status da aplicação e dependências

### Logs
- **Framework**: NLog
- **Formato**: JSON estruturado
- **Níveis**: Debug, Info, Warning, Error, Fatal
- **Destinos**: Console, Arquivo, Elasticsearch (opcional)

### Métricas
- CPU e memória via Kubernetes
- Requests por segundo
- Tempo de resposta
- Taxa de erro

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

### Padrões de Código
- Seguir convenções do C#/.NET
- Testes unitários obrigatórios para novas funcionalidades
- Documentação de APIs com XML comments
- Code review obrigatório

## 📄 Licença

Este projeto é propriedade da Motivai e está sob licença proprietária.

## 📞 Suporte

Para dúvidas e suporte técnico, entre em contato com a equipe de desenvolvimento da Motivai.

---

**Motivai Catalogs API** - Transformando programas de incentivo em experiências excepcionais! 🎯
