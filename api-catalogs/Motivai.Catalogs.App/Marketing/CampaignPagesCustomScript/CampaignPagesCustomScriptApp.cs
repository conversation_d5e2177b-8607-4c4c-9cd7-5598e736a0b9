using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.IRepository.Marketing;
using Motivai.Catalogs.Domain.Models.Marketing;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.App.Marketing.CampaignPagesCustomScript
{
    public class CampaignPagesCustomScriptApp : ICampaignPagesCustomScriptApp
    {
        private readonly ICampaignsPagesCustomScriptsRepository campaignsPagesCustomScriptsRepository;
        public CampaignPagesCustomScriptApp(ICampaignsPagesCustomScriptsRepository _campaignsPagesCustomScriptsRepository)
        {
            this.campaignsPagesCustomScriptsRepository = _campaignsPagesCustomScriptsRepository;
        }
        public async Task<List<CampaignPagesCustomScriptModel>> GetCampaignsScript(string campaignId)
        {
            if (string.IsNullOrEmpty(campaignId))
            {
                throw MotivaiException.ofValidation("Campanha inválida.");
            }
            return await this.campaignsPagesCustomScriptsRepository.GetCampaignScripts(campaignId);
        }
    }
}