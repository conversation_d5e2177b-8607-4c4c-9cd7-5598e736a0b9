using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.IApp.Regions;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.Marketing
{
    public class MarketingSpecialshopApp : IMarketingSpecialshopApp
    {
        #region Lojas Especiais

        private readonly ICampaignRepository _campaignRepository;
        private readonly IPlatformGeneralSpecialShopRepository _platformGeneralSpecialShopRepository;
        private readonly IProductCatalogRepository _productCatalogRepository;
        private readonly CatalogProductService _catalogProductService;
        private readonly IRegionApp _regionApp;

        public MarketingSpecialshopApp(ICampaignRepository campaignRepository,
            IPlatformGeneralSpecialShopRepository platformGeneralSpecialShopRepository,
            IProductCatalogRepository productCatalogRepository,
            CatalogProductService catalogProductService, IRegionApp regionApp)
        {
            _campaignRepository = campaignRepository;
            _platformGeneralSpecialShopRepository = platformGeneralSpecialShopRepository;
            _productCatalogRepository = productCatalogRepository;
            _catalogProductService = catalogProductService;
            _regionApp = regionApp;
        }

        public async Task<List<SpecialShopModel>> GetActiveSpecialShopsByCampaign(Guid campaignId, Guid participantId,
            CampaignParametrizationOrigin? origin)
        {
            var specialShops = new List<SpecialShopModel>();

            if (!origin.HasValue || origin == CampaignParametrizationOrigin.CAMPAIGN)
            {
                var campaignSpecialShops = await GetCampaignSpecialShops(campaignId, participantId);
                if (!campaignSpecialShops.IsNullOrEmpty())
                {
                    specialShops.AddRange(campaignSpecialShops);
                }
            }

            if (!origin.HasValue || origin == CampaignParametrizationOrigin.GENERAL_PLATFORM)
            {
                var campaignParametrizations = await _campaignRepository.GetSettings(campaignId);
                if (!campaignParametrizations.Parametrizations.SkipPlatformSpecialShops)
                {
                    var generalPlatformSpecialShops = await GetPlatformSpecialShops();
                    if (!generalPlatformSpecialShops.IsNullOrEmpty())
                    {
                        specialShops.AddRange(generalPlatformSpecialShops);
                    }
                }
            }

            return specialShops;
        }

        public async Task<SpecialShopModel> GetActiveSpecialShopById(Guid campaignId, Guid specialShopId, Guid userId,
            Guid participantId, CampaignParametrizationOrigin origin,
            bool? shouldConsultSkuAvailaibilityAtPartner = null)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (participantId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");
            if (specialShopId == Guid.Empty)
                throw MotivaiException.ofValidation("Loja especial inválida.");

            SpecialShopModel specialShop =
                await GetSpecialShopByOriginAndId(campaignId, specialShopId, participantId, origin);
            if (specialShop == null)
                throw MotivaiException.ofValidation("Loja especial não encontrada.");

            await LoadSpecialShopProducts(campaignId, userId, participantId, specialShop,
                shouldConsultSkuAvailaibilityAtPartner);


            return specialShop;
        }

        private async Task<List<SpecialShopModel>> GetCampaignSpecialShops(Guid campaignId, Guid participantId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            var specialShops = await _campaignRepository.GetActiveSpecialShopsByCampaign(campaignId);
            if (specialShops.IsNullOrEmpty())
                return null;

            List<Guid> participantAudiences = null;
            if (specialShops.Any(m => m.HasTargetAudiences()))
            {
                participantAudiences =
                    await _campaignRepository.GetTargetAudiencesByParticipant(campaignId, participantId);
            }

            if (participantAudiences.IsNullOrEmpty())
                return specialShops.Where(m => !m.HasTargetAudiences()).ToList();
            return specialShops
                .Where(m => !m.HasTargetAudiences() || participantAudiences.Any(i => m.TargetAudiences.Contains(i)))
                .ToList();
        }

        private Task<SpecialShopModel> GetSpecialShopByOriginAndId(Guid campaignId, Guid specialShopId,
            Guid participantId, CampaignParametrizationOrigin origin)
        {
            if (origin == CampaignParametrizationOrigin.GENERAL_PLATFORM)
            {
                return GetGeneralPlatformSpecialShopById(specialShopId);
            }

            return GetCampaignSpecialShopById(campaignId, specialShopId, participantId);
        }

        private async Task<SpecialShopModel> GetCampaignSpecialShopById(Guid campaignId, Guid specialShopId,
            Guid participantId)
        {
            var specialShop = await _campaignRepository.GetActiveSpecialShopById(campaignId, specialShopId);

            if (specialShop != null && specialShop.HasTargetAudiences())
            {
                List<Guid> participantAudiences =
                    await _campaignRepository.GetTargetAudiencesByParticipant(campaignId, participantId);
                if (participantAudiences == null || participantAudiences.Count == 0)
                    return null;
                if (!participantAudiences.Any(i => specialShop.TargetAudiences.Contains(i)))
                {
                    return null;
                }
            }

            return specialShop;
        }

        private async Task<SpecialShopModel> GetGeneralPlatformSpecialShopById(Guid specialShopId)
        {
            var generalPlatformSpecialShop = await _platformGeneralSpecialShopRepository
                .GetSpecialShopActiveById(specialShopId);

            return SpecialShopModel.BuildFrom(generalPlatformSpecialShop,
                CampaignParametrizationOrigin.GENERAL_PLATFORM);
        }

        private async Task LoadSpecialShopProducts(Guid campaignId, Guid userId, Guid participantId,
            SpecialShopModel specialShop, bool? shouldConsultSkuAvailaibilityAtPartner = null)
        {
            if (specialShop.Skus.IsNullOrEmpty())
            {
                return;
            }

            // RegionFilter regionFilter = await _regionApp.GetMatchedRegionsForUser(userId, campaignId);
            specialShop.Products = await _productCatalogRepository.GetProductsBySkusIds(campaignId, userId,
                participantId, specialShop.Skus);


            if (specialShop.Products != null)
            {
                await _catalogProductService.CalculateOutdatedPrices(campaignId, userId, specialShop.Products, null,
                    shouldConsultSkuAvailaibilityAtPartner);
            }
        }

        public async Task<SpecialShopModel> GetRandomActiveSpecialShopByCampaign(Guid campaignId, Guid participantId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var specialShop = await _campaignRepository.GetRandomActiveSpecialShopByCampaign(campaignId);
            if (specialShop == null) return null;

            if (specialShop.HasTargetAudiences())
            {
                List<Guid> participantAudiences =
                    await _campaignRepository.GetTargetAudiencesByParticipant(campaignId, participantId);

                if (participantAudiences == null || participantAudiences.Count == 0)
                    return null;

                if (!participantAudiences.Any(i => specialShop.TargetAudiences.Contains(i)))
                {
                    return null;
                }
            }

            return specialShop;
        }

        private async Task<List<SpecialShopModel>> GetPlatformSpecialShops()
        {
            try
            {
                var generalSpecialShops = await this._platformGeneralSpecialShopRepository.GetSpecialShopActives();
                if (generalSpecialShops.IsNullOrEmpty())
                    return null;
                return generalSpecialShops
                    .Select(s => SpecialShopModel.BuildFrom(s, CampaignParametrizationOrigin.GENERAL_PLATFORM))
                    .ToList();
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Catalogs - SpecialShops",
                    "Erro durante o carregamento das lojas especiais gerais da plataforma");
                return null;
            }
        }

        #endregion
    }
}