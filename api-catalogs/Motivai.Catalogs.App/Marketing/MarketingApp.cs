using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.IApp.Regions;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Newtonsoft.Json.Linq;

namespace Motivai.Catalogs.App.Marketing
{
	public class MarketingApp : IMarketingApp
    {
        private readonly ICampaignRepository _campaignRepository;
        private readonly IProductCatalogRepository _productCatalogRepository;
        private readonly CatalogProductService _catalogProductService;
        private readonly IRegionApp _regionApp;

        public MarketingApp(ICampaignRepository campanhaRepository, IProductCatalogRepository productCatalogRepository,
            CatalogProductService catalogProductService, IRegionApp regionApp)
        {
            this._campaignRepository = campanhaRepository;
            this._productCatalogRepository = productCatalogRepository;
            this._catalogProductService = catalogProductService;
            this._regionApp = regionApp;
        }

        public async Task<CampaignCustomization> GetCampaignCustomization(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var customization = new CampaignCustomization();
            customization.CoinName = await GetCoinName(campaignId);
            customization.Theme = await GetTheme(campaignId);
            customization.Menu = await GetMenu(campaignId);
            return customization;
        }

        private async Task<CoinName> GetCoinName(Guid campaignId)
        {
            try
            {
                return await _campaignRepository.GetCoinName(campaignId);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Campaign - Coin Name", "Erro ao carregar a moeda da campanha.");
            }
            return CoinName.Of("", "");
        }

        private async Task<Guid> GetTheme(Guid campaignId)
        {
            try
            {
                return await _campaignRepository.GetTheme(campaignId);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Campaign - Tema", "Erro ao carregar o tema da campanha.");
            }
            return Guid.Empty;
        }

        private async Task<JArray> GetMenu(Guid campaignId)
        {
            try
            {
                return await _campaignRepository.GetMenu(campaignId);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Campaign - Menu", "Erro ao carregar o menu da campanha.");
            }
            return null;
        }

        public async Task<JArray> GetCampaignMenu(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            return await _campaignRepository.GetMenu(campaignId);
        }

        public async Task<Guid> GetThemeFolderByCampaign(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            return await _campaignRepository.GetTheme(campaignId);
        }

        public async Task<MediaBox> GetCampaignCommunicationById(Guid campaignId, Guid communicationId, Guid participantId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var communication = await _campaignRepository.GetCommunicationById(campaignId, communicationId);
            if (communication == null || !communication.Active) return null;

            List<Guid> participantAudiences = null;
            if (communication.HasTargetAudiences())
            {
                participantAudiences = await _campaignRepository.GetTargetAudiencesByParticipant(campaignId, participantId);
                if (!participantAudiences.Any(i => communication.TargetAudiences.Contains(i)))
                {
                    return null;
                }
            }

            return new MediaBox()
            {
                Id = communication.Id.ToString(),
                Identifier = communication.Identifier,
                Name = communication.Description,
                Location = communication.CommunicationLocation,
                Message = communication.CommunicationMessage,
                ImageUrl = communication.ImageUrl,
                MediumImageUrl = communication.MediumImageUrl,
                SmallImageUrl = communication.SmallImageUrl,
                TargetAudiences = communication.TargetAudiences,
                OpenInNewTab = communication.OpenInNewTab,
                Communication = true,


            };
        }
    }
}