using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Newtonsoft.Json;

namespace Motivai.Catalogs.App.Marketing
{
	public class MarketingMediaboxApp : IMarketingMediaboxApp
	{
		private readonly ICampaignRepository _campaignRepository;
		private readonly IPlatformGeneralMediaBoxRepository _platformGeneralMediaBoxRepository;
		private readonly IProductCatalogRepository _productCatalogRepository;
		private readonly CatalogProductService _catalogProductService;

		public MarketingMediaboxApp(ICampaignRepository campaignRepository,
			IPlatformGeneralMediaBoxRepository PlatformGeneralMediaBoxRepository,
			IProductCatalogRepository productCatalogRepository,
			CatalogProductService _catalogProductService)
		{
			this._campaignRepository = campaignRepository;
			this._platformGeneralMediaBoxRepository = PlatformGeneralMediaBoxRepository;
			_productCatalogRepository = productCatalogRepository;
			this._catalogProductService = _catalogProductService;
		}

		public Task<List<MediaBox>> GetCatalogMediaboxesForHome(Guid campaignId, Guid userId, Guid participantId)
		{
			return GetActiveMediaBoxesForUser(campaignId, userId, participantId, MediaboxVisibleOn.CATALOG, Guid.Empty.ToString());
		}

		public Task<List<MediaBox>> GetActiveMediaBoxes(Guid campaignId, Guid userId, Guid participantId,
			bool catalog, bool site, string departmentId, string sitePage)
		{
			if (catalog)
			{
				return GetActiveMediaBoxesForUser(campaignId, userId, participantId, MediaboxVisibleOn.CATALOG, departmentId);
			}
			else if (site)
			{
				return GetActiveMediaBoxesForUser(campaignId, userId, participantId, MediaboxVisibleOn.CAMPAIGN_SITE, sitePage);
			}
			// Sem implementação pro App ainda
			return null;
		}

		public async Task<List<MediaBox>> GetCatalogMediaboxesForDepartment(Guid campaignId, Guid userId, Guid participantId, string departmentId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			var mediaboxes = await GetActiveMediaBoxesForUser(campaignId, userId, participantId, MediaboxVisibleOn.CATALOG, departmentId);

			// Se tiver algum mediabox de produto então efetua a pesquisa deste
			if (mediaboxes.Any(m => m.HasProduct()))
			{
				var partnersSkus = mediaboxes.Where(m => m.HasProduct())
					.Select(m => m.Product)
					.ToList();
				var products = await _productCatalogRepository.GetProductsByPartnersAndSkuCodes(campaignId, userId, participantId, partnersSkus, null);
				if (products != null && products.Count > 0)
				{
					await _catalogProductService.CalculateOutdatedPrices(campaignId, userId, products);

					products.ForEach(prod =>
					{
						mediaboxes.Where(m => m.HasProduct() && m.Product.PartnerId == prod.PartnerId && m.Product.SkuCode == prod.SkuCode)
							.ToList()
							.ForEach(mb =>
							{
								mb.FeaturedProduct = prod;
								mb.Product = null;
							});
					});
				}
			}

			return mediaboxes;
		}

		// adicionando bool skipPlatformMediaboxes

		private async Task<List<MediaBox>> GetActiveMediaBoxesForUser(Guid campaignId, Guid userId, Guid participantId,
				MediaboxVisibleOn visibleOn, string pageOrDepartment)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");

			var mediaboxes = new List<MediaBox>();

			var campaignMediaboxes = await GetCampaignMediaboxes(campaignId, userId, visibleOn, pageOrDepartment);
			if (!campaignMediaboxes.IsNullOrEmpty())
			{
				mediaboxes.AddRange(campaignMediaboxes);
			}

			var campaignParametrizations = await _campaignRepository.GetSettings(campaignId);

			if (!campaignParametrizations.Parametrizations.SkipPlatformMediaboxes) {
				var platformMediaboxes = await GetPlatformActiveMediaBoxes(visibleOn, pageOrDepartment);
				if (!platformMediaboxes.IsNullOrEmpty())
				{
					mediaboxes.AddRange(platformMediaboxes);
				}
			}

			List<Guid> participantAudiences = null;
			if (mediaboxes.Any(m => m.HasTargetAudiences()))
			{
				participantAudiences = await _campaignRepository.GetTargetAudiencesByParticipant(campaignId, participantId);
			}
			if (participantAudiences.IsNullOrEmpty())
			{
				return mediaboxes.Where(m => !m.HasTargetAudiences()).ToList();
			}

			return mediaboxes
				.Where(m => !m.HasTargetAudiences() || participantAudiences.Any(i => m.TargetAudiences.Contains(i)))
				.ToList();
		}

		private async Task<List<MediaBox>> GetCampaignMediaboxes(Guid campaignId, Guid userId,
			MediaboxVisibleOn visibleOn, string pageOrDepartment)
		{
			switch (visibleOn)
			{
				case MediaboxVisibleOn.CATALOG:
					return await _campaignRepository.GetActiveMediaBoxesForDepartment(campaignId, userId, pageOrDepartment);
				case MediaboxVisibleOn.CAMPAIGN_SITE:
					return await _campaignRepository.GetActiveMediaBoxesForSitePage(campaignId, userId, pageOrDepartment);
				default:
					return null;
			}
		}

		private async Task<List<MediaBox>> GetPlatformActiveMediaBoxes(MediaboxVisibleOn visibleOn, string page)
		{
			var generalMediaBoxes = await this._platformGeneralMediaBoxRepository.GetMediaBoxActives(visibleOn, page);
			if (generalMediaBoxes == null)
				return Enumerable.Empty<MediaBox>().ToList();
			return generalMediaBoxes.Select(MediaBox.BuildFrom).ToList();
		}
	}
}