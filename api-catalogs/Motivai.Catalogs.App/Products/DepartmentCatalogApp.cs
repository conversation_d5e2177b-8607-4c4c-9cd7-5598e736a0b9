﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.Catalogo.App
{
    public class DepartmentCatalogApp : IDepartmentCatalogApp
    {
        private readonly CatalogProductService _catalogProductService;
        private readonly ICampaignRepository _campaignRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly IProductCatalogRepository _productCatalogRepository;

        public DepartmentCatalogApp(CatalogProductService catalogProductService, ICampaignRepository campaignRepository,
            ICategoryRepository categoryRepository, IProductCatalogRepository productCatalogRepository)
        {
            this._catalogProductService = catalogProductService;
            this._campaignRepository = campaignRepository;
            this._categoryRepository = categoryRepository;
            this._productCatalogRepository = productCatalogRepository;
        }

        public async Task<DepartmentModel> GetDepartmentById(Guid campaignId, Guid departmentId, Guid participantId)
        {
            if (departmentId == Guid.Empty)
                return null;
            var departmentsIds = await _categoryRepository.GetDepartmentsWithProducts(campaignId, participantId);

            // Verifica se o departamento está no menu da campanha
            if (departmentsIds == null || !departmentsIds.Contains(departmentId))
                return null;

            // Carrega os dados do departamento
            var department = await _categoryRepository.GetCategory(departmentId);

            if (department == null)
                return null;
            return new DepartmentModel()
            {
                Id = department.Id,
                Name = department.Name
            };
        }

        public async Task<CategoryModel> GetCategoryById(Guid campaignId, Guid categoryId, Guid participantId)
        {
            if (categoryId == Guid.Empty)
                return null;
            var departmentsIds = await _categoryRepository.GetCategoriesWithProducts(campaignId, participantId);

            // Verifica se a categoria
            if (departmentsIds == null || !departmentsIds.Contains(categoryId))
                return null;

            // Carrega os dados da categoria
            var category = await _categoryRepository.GetCategory(categoryId);

            if (category == null)
                return null;

            if (!category.DepartmentId.HasValue)
                throw MotivaiException.of(ErrorType.Configuration, "Categoria não está vinculado a nenhum departamento.");

            // Carrega os dados do departamento
            var department = await _categoryRepository.GetCategory(category.DepartmentId.Value);
            if (department == null)
                throw MotivaiException.ofValidation("Categoria com departamento inválido.");

            ProductCategoryModel subcategory = null;
            if (category.IsSubcategory())
            {
                subcategory = category;
                category = await _categoryRepository.GetCategory(category.ParentId.Value);
                if (category == null)
                    throw MotivaiException.ofValidation("Subcategoria com categoria inválida.");
            }

            return new CategoryModel()
            {
                Id = category.Id,
                Name = category.Name,
                SubcategoryId = subcategory != null ? subcategory.Id : (Guid?)null,
                SubcategoryName = subcategory != null ? subcategory.Name : null,
                Department = new DepartmentModel()
                {
                    Id = department.Id,
                    Name = department.Name
                }
            };
        }

        public async Task<List<ProductShowcaseModel>> GetDepartmentFeaturedProductsByCampaign(Guid campaignId, Guid departmentId,
                Guid userId, Guid participantId, decimal? availableBalance = null)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            var campaignSettings = await _campaignRepository.GetSettings(campaignId);

            var departmentProducts = new List<ProductShowcaseModel>();
            // Quantidade de produtos para exibir na home
            var take = 16;

            // Carrega os produtos destacados do Marketing
            var featuredSkusIds = await _campaignRepository.GetDepartmentFeaturedProducts(campaignId, departmentId);

            // Se tiver produtos configurados para home então é carregado
            if (featuredSkusIds != null && featuredSkusIds.Count > 0)
            {
                var featuredProducts = await _productCatalogRepository.GetProductsByPartnersAndSkuCodes(campaignId, userId, participantId, featuredSkusIds, null);

                if (featuredProducts != null && featuredProducts.Count > 0)
                {
                    departmentProducts.AddRange(featuredProducts);
                    // Se tiver menos de 16 produtos destacados então carrega outros para preencher
                    take = featuredProducts.Count < 16 ? take - featuredProducts.Count : 0;
                }
            }

            // Carrega o restante dos produtos caso não tenha completado 16
            if (take > 0)
            {
                decimal? fromCurrency = default(decimal?);
                decimal? toCurrency = default(decimal?);
                var balancePoints = availableBalance ?? 0;
                if (balancePoints > 0)
                {
                    var campaignFees = await _campaignRepository.GetFees(campaignId);
                    if (campaignFees.ClientPartnersFee > 0)
                    {
                        balancePoints = balancePoints * ((decimal)1.0 + campaignFees.ClientPartnersFee);
                    }
                    var balanceCurrency = AmountConversor.RevertPointsFactorApplied(campaignFees.PointsConversionFactor, balancePoints);
                    fromCurrency = balanceCurrency * (decimal)0.9;
                    toCurrency = balanceCurrency * (decimal)1.1;
                }
                var products = await _productCatalogRepository.GetProducts(campaignId, userId, participantId, departmentId,
                    null, 0, take, fromCurrency, toCurrency);
                // Carrega novamente sem filtrar por pontos
                if (products.IsNullOrEmpty() && balancePoints > 0)
                {
                    products = await _productCatalogRepository.GetProducts(campaignId, userId, participantId, departmentId, null, 0, take);
                }
                if (products != null)
                {
                    // Adiciona apenas os produtos que já não foram destacados
                    departmentProducts.AddRange(products.Where(prod => !departmentProducts.Any(hp => hp.Id == prod.Id)));
                }
            }

            await _catalogProductService.CalculateOutdatedPrices(campaignId, userId, departmentProducts, availableBalance);
            return departmentProducts;
        }

        public async Task<List<ProductShowcaseModel>> GetProductsOffersByDepartment(Guid campaignId, Guid departmentId, Guid userId, Guid participantId, decimal? availableBalance = null)
        {
            if (campaignId == null)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var searchResult = await _productCatalogRepository.SearchProducts(campaignId, userId, participantId, departmentId, null);
            if (searchResult == null || !searchResult.HasResult())
                return null;
            await _catalogProductService.CalculateOutdatedPrices(campaignId, userId, searchResult.Result, availableBalance);
            return searchResult.Result;
        }
    }
}