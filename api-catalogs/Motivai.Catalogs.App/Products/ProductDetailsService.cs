using System.Threading.Tasks;
using System;

using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Dtos;
using Newtonsoft.Json;

namespace Motivai.Catalogs.App.Products
{
    public class ProductDetailsService
    {
        private readonly ICompanyRepository companyRepository;
        private readonly CatalogProductService catalogProductService;

        public ProductDetailsService(ICompanyRepository companyRepository, CatalogProductService catalogProductService)
        {
            this.companyRepository = companyRepository;
            this.catalogProductService = catalogProductService;
        }

        public async Task<ProductDetailsModel> GetProductDetails(Guid campaignId, Guid userId, ElasticsearchProduct elasticProduct,
            Sku skuParam = null, bool evenInactive = false, decimal? availableBalance = null, bool checkAvailabilityOfAnotherSku = true, decimal? priceDefinedByParticipant = null)
        {
            if (elasticProduct == null)
                return null;
            if (elasticProduct.Skus == null || elasticProduct.Skus.Count == 0)
                throw MotivaiException.ofValidation("Produto não possui nenhum SKU cadastrado.");

            if (skuParam == null)
            {
                skuParam = elasticProduct.GetMasterActiveOrFirstActive();
                // Se aceita produto indisponível então pega o master ou primeiro
                if (skuParam == null && evenInactive)
                {
                    skuParam = elasticProduct.GetMasterSkuOrFirstSku();
                }
            }

            var partnerSettings = await this.companyRepository.GetPartnerSettings(Guid.Parse(elasticProduct.PartnerId));

            var productDetails = new ProductDetailsModel(elasticProduct, skuParam, partnerSettings);

            if (productDetails.IsDynamicPriceDefinedByParticipant()) {
                    productDetails.SetDynamicPriceToCalculate(priceDefinedByParticipant);
            }
            await this.ApplyDisponibilityAndPrices(campaignId, userId, productDetails, availableBalance);

            if (!productDetails.Available && checkAvailabilityOfAnotherSku)
            {
                var sku = this.CheckAvailabilityOfAnotherSku(productDetails, elasticProduct);
                if (sku != null)
                {
                    productDetails = new ProductDetailsModel(elasticProduct, sku, partnerSettings);
                    await this.ApplyDisponibilityAndPrices(campaignId, userId, productDetails, availableBalance);
                }
            }
            return productDetails;
        }

        private async Task ApplyDisponibilityAndPrices(Guid campaignId, Guid userId, ProductDetailsModel product,
            decimal? availableBalance = null)
        {
            try
            {
                await this.catalogProductService.ApplyDisponibilityAndPrices(campaignId, userId, product, availableBalance);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, $"Erro durante na verificação de disponibilidade e preço do produto {product.ElasticsearchId} - {product.SkuCode}", true);
                product.Available = false;
            }
        }

        private Sku CheckAvailabilityOfAnotherSku(ProductDetailsModel product, ElasticsearchProduct elasticProd)
        {
            Sku skuAvailable = null;
            foreach (var sku in elasticProd.Skus)
            {
                if (sku.Id == product.Sku.Id || !elasticProd.IsActive(sku)) continue;

                var available = elasticProd.IsActive(sku);

                if (available)
                {
                    skuAvailable = sku;
                    break;
                }
            }
            return skuAvailable;
        }
    }
}