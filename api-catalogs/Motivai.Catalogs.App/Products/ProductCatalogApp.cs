﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogo.Domain.Models;
using Motivai.Catalogs.App.Products;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Services.ShoppingCarts;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;

using Newtonsoft.Json;

namespace Motivai.Catalogo.App
{
    public class ProductCatalogApp : IProductCatalogApp
    {
        private readonly CatalogProductService catalogProductService;
        private readonly ShoppingCartManager shoppingCartManager;
        private readonly ICampaignRepository campaignRepository;
        private readonly IProductCatalogRepository productsCatalogRepository;
        private readonly ProductDetailsService productDetailsService;
        private readonly ICompanyRepository companyRepository;

        public ProductCatalogApp(CatalogProductService catalogProductService, ShoppingCartManager shoppingCartManager,
            ICampaignRepository campaignRepository, IProductCatalogRepository productsCatalogRepository,
            ProductDetailsService productDetailsService, ICompanyRepository companyRepository)
        {
            this.catalogProductService = catalogProductService;
            this.shoppingCartManager = shoppingCartManager;
            this.campaignRepository = campaignRepository;
            this.productsCatalogRepository = productsCatalogRepository;
            this.productDetailsService = productDetailsService;
            this.companyRepository = companyRepository;
        }

        private async Task ValidateCampaignStatus(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (!await campaignRepository.IsActive(campaignId))
            {
                throw MotivaiException.ofValidation("Campanha não está ativa.");
            }
        }

        public async Task<SearchModel> SearchProducts(Guid campaignId, Guid userId, Guid participantId, Guid? departmentId, string query,
            string[] partners = null, string[] departments = null, string[] categories = null, string[] subcategories = null, string[] manufacturers = null,
            string[] colors = null, string[] voltagens = null, string sortBy = null, bool? asc = null, string nestedPath = null, decimal? fromPoint = null, decimal? toPoints = null,
            decimal? availableBalance = null, int from = 0, int size = 8, string[] productType = null)
        {
            await ValidateCampaignStatus(campaignId);

            List<ProductShowcaseModel> featuredProducts = null;

            // carrega os produtos destacados do departamento
            if (departmentId.HasValue && departmentId.Value != Guid.Empty && (categories == null || categories.Length == 0))
            {
                // Carrega os produtos destacados do Marketing
                var featuredSkusIds = await campaignRepository.GetDepartmentFeaturedProducts(campaignId, departmentId.Value);
                if (!featuredSkusIds.IsNullOrEmpty() && featuredSkusIds.Count > from)
                {
                    featuredProducts = await productsCatalogRepository.GetProductsBySkusIds(campaignId, userId, participantId,
                        featuredSkusIds.Select(a => a.SkuId).Skip(from).Take(8).ToList());
                    if (!featuredProducts.IsNullOrEmpty())
                    {
                        // se a qtde de produtos destacados for menor que a paginação então carrega mais
                        size = featuredProducts.Count > size ? 0 : (size - featuredProducts.Count);
                    }
                }
            }

            decimal? fromSaleValue = default, toSaleValue = default;
            if (fromPoint.HasValue || toPoints.HasValue)
            {
                var campaignFees = await campaignRepository.GetFees(campaignId);
                if (fromPoint.HasValue)
                    fromSaleValue = AmountConversor.RevertPointsFactorApplied(campaignFees.PointsConversionFactor, fromPoint.Value);
                if (toPoints.HasValue)
                    toSaleValue = AmountConversor.RevertPointsFactorApplied(campaignFees.PointsConversionFactor, toPoints.Value);
            }

            var searchResult = await productsCatalogRepository.SearchProducts(campaignId, userId, participantId,
                departmentId, query, partners, departments, categories, subcategories, manufacturers, colors, voltagens,
                sortBy, asc, nestedPath, fromSaleValue, toSaleValue, null, from, size, productType);
            if (searchResult == null)
                return null;

            if (!featuredProducts.IsNullOrEmpty())
            {
                // coloca os destacados em primeiro
                if (searchResult.HasResult())
                {
                    featuredProducts.RemoveAll(p => searchResult.Result.Any(r => r.Id == p.Id));
                    featuredProducts.AddRange(searchResult.Result);
                }
                searchResult.Result = featuredProducts;
            }

            if (searchResult.HasResult())
            {
                await catalogProductService.CalculateOutdatedPrices(campaignId, userId, searchResult.Result, availableBalance);
            }
            return searchResult;
        }

        public async Task<List<ProductShowcaseModel>> SearchProductsByTermOrSku(Guid campaignId, Guid? factoryId, Guid userId,
            Guid? participantId, string skuCode, string term, decimal? availableBalance = null, int from = 0, int size = 10)
        {
            if (string.IsNullOrEmpty(skuCode) && string.IsNullOrEmpty(term))
                throw MotivaiException.ofValidation("Informe o código SKU ou termo para pesquisar o produto.");
            await ValidateCampaignStatus(campaignId);

            var searchResult = await productsCatalogRepository.SearchProductsByTermOrSkuCode(campaignId, userId,
                participantId ?? userId, skuCode, term, factoryId, null, from, size);

            if (searchResult != null && searchResult.Count > 0)
            {
                await catalogProductService.CalculateOutdatedPrices(campaignId, userId, searchResult, availableBalance);
            }
            return searchResult;
        }

        ///<summary>
        /// Quando o SKU principal do produto estiver indisponivel e searchSimiliarSkuAndEan for true, então verifica cada SKU desse produto
        /// se algum deles está ativo, se encontrar um SKU disponível então retorno ele para o cara visualizar.
        ///
        /// Se todos os SKUs deste produto estiverem indisponíveis, então pesquisa outro produto no elastic que tenha o mesmo código SKU e o mesmo EAN.
        /// No caso da Cnova, o SKU e o EAN é o mesmo entre as bandeiras. Alguns produtos tem SKUs diferentes mas EAN iguais, daí usar a mesma pesquisa
        /// do marketplace não daria certo (tem vários brinquedos de dinossauros do filme jurassic park com código SKU diferente mas com EANs iguais).
        /// E utilizar apenas o código SKU pode trazer produto de outro parceiro que não tenha relação com o primeiro que estamos consultando.
        ///</summary>
        public async Task<ProductDetailsModel> GetProductByElasticId(Guid campaignId, Guid userId, Guid participantId, string elasticId, string skuCode,
            bool? evenInactive = false, bool? searchSimiliarSkuAndEan = false, decimal? availableBalance = null)
        {
            await ValidateCampaignStatus(campaignId);

            var elasticProd = await productsCatalogRepository.GetProductByElasticId(campaignId, userId, participantId, elasticId, evenInactive);
            if (elasticProd == null || elasticProd.Skus == null)
                return null;
            if (!string.IsNullOrEmpty(skuCode))
            {
                elasticProd.Skus.RemoveAll(s => s.Code != skuCode);
                if (elasticProd.Skus.Count == 0) return null;
            }

            var product = await this.productDetailsService.GetProductDetails(campaignId, userId, elasticProd, null, evenInactive ?? false, availableBalance);

            // Se estiver indisponível e permite carregar outro com SKU e EAN
            if (!product.Available && searchSimiliarSkuAndEan.HasValue && searchSimiliarSkuAndEan.Value)
            {
                return await FindSimilarOrSame(campaignId, userId, participantId, skuCode, elasticProd, product, availableBalance);
            }
            return product;
        }

        public async Task<ProductDetailsModel> AddSkuToParticipantCart(Guid campaignId, Guid userId, Guid participantId, string elasticsearchId,
            string skuCode, int quantity = 1, decimal? priceDefinedByParticipant = null)
        {
            await ValidateCampaignStatus(campaignId);

            var elasticProduct = await productsCatalogRepository.GetProductForCartItemByElasticIdAndSkuCode(campaignId, userId, participantId, elasticsearchId, skuCode);
            if (elasticProduct == null)
            {
                throw MotivaiException.ofValidation($"SKU {skuCode} não encontrado.");
            }
            var sku = elasticProduct.GetSkuByCode(skuCode) ?? throw MotivaiException.ofValidation("SKU não encontrado.");
            ProductDetailsModel product = null;
            try
            {
                product = await this.productDetailsService.GetProductDetails(campaignId, userId, elasticProduct, sku, false, null, false, priceDefinedByParticipant);
                await shoppingCartManager.AddSkuToParticipantCart(campaignId, userId, product, quantity);
            }
            catch (Exception ex)
            {
                if (ex is MotivaiException gpex && (gpex.ErrorType == ErrorType.Validation))
                    throw ex;
                if (product != null)
                    product.Available = false;
            }
            return product;
        }

        private async Task<ProductDetailsModel> FindSimilarOrSame(Guid campaignId, Guid userId, Guid participantId, string skuCode,
            ElasticsearchProduct elasticProd, ProductDetailsModel product, decimal? availableBalance = null)
        {
            // Pesquisa pelo código SKU (mas não remove os outros SKUs dos produtos encontrados)
            var foundProducts = await productsCatalogRepository.GetAllProductsBySkuCode(campaignId, userId, participantId, skuCode ?? product.SkuCode, false);

            // se não encontrar outros então retorna o mesmo indisponível
            // Se o produto encontrado for o mesmo que calculou inicialmente então o retorna
            if (foundProducts == null || foundProducts.Count == 1 && Guid.Parse(foundProducts.FirstOrDefault().Id) == product.Id)
                return product;

            // Orderna os produtos por parceiro para evitar que a cada chamada retorne um produto que depende da ordem de retorno do Elasticsearch
            foundProducts = foundProducts.OrderBy(p => p.Partner).ToList();

            foreach (var similarProduct in foundProducts)
            {
                // Pula o produto inicial encontrado
                if (Guid.Parse(similarProduct.Id) == product.Id || similarProduct.Skus == null) continue;

                // se não tiver o mesmo EAN então ignora (pois dois parceiros diferentes podem ter o mesmo SKU)
                if (!elasticProd.Skus.Any(wantedSku => similarProduct.Skus.Any(s => s.Ean == wantedSku.Ean)))
                    continue;

                var otherProduct = await this.productDetailsService.GetProductDetails(campaignId, userId, similarProduct, null, false, availableBalance);
                if (otherProduct.Available)
                    return otherProduct;
            }
            return product;
        }

        public async Task<List<ProductDetailsModel>> FindProductsBySkuCode(Guid campaignId, Guid userId, Guid participantId, string skuCode)
        {
            await ValidateCampaignStatus(campaignId);

            var foundProducts = await productsCatalogRepository.GetAllProductsBySkuCode(campaignId, userId, participantId, skuCode);
            if (foundProducts == null) return null;
            var products = new List<ProductDetailsModel>(foundProducts.Count);
            await foundProducts.ForEachAsync(async p =>
            {
                products.Add(await this.productDetailsService.GetProductDetails(campaignId, userId, p));
            });
            return products;
        }

        public async Task<List<MarketplaceItem>> GetMarketplacesByEan(Guid campaignId, Guid userId, Guid participantId, string ean, decimal? availableBalance = null)
        {
            if (string.IsNullOrEmpty(ean))
                throw MotivaiException.ofValidation("Deve ser informado o EAN.");
            await ValidateCampaignStatus(campaignId);

            var products = await productsCatalogRepository.GetProductsByEan(campaignId, userId, participantId, ean);
            if (products.IsNullOrEmpty())
                return null;
            await catalogProductService.CalculateOutdatedPrices(campaignId, userId, products, availableBalance);
            return products.Select(MarketplaceItem.From).ToList();
        }

        public async Task<List<ProductShowcaseModel>> GetSimilarProducts(Guid campaignId, Guid userId, Guid participantId, Guid categoryLowestLevel, decimal? availableBalance = null)
        {
            await ValidateCampaignStatus(campaignId);

            var products = await productsCatalogRepository.GetProductsByCategory(campaignId, userId, participantId, categoryLowestLevel, 0, 8);
            if (products.IsNullOrEmpty())
                return null;
            await catalogProductService.CalculateOutdatedPrices(campaignId, userId, products, availableBalance);
            return products;
        }

        public async Task<ProductAttributes> GetSkusAttributes(Guid campaignId, Guid userId, Guid participantId, string elasticId,
            string model = null, string voltage = null, string color = null, string size = null)
        {
            await ValidateCampaignStatus(campaignId);
            return await productsCatalogRepository.GetSkusAttributes(campaignId, userId, participantId, elasticId, model, voltage, color, size);
        }

        public async Task<bool> RegisterAvailableNotification(Guid campaignId, Guid productId, Guid participantId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (productId == Guid.Empty)
                throw MotivaiException.ofValidation("Produto inválido.");
            if (participantId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");
            return await productsCatalogRepository.RegisterAvailableNotification(productId, participantId);
        }

        public async Task<AvailabilityModel> CheckAvailability(Guid campaignId, Guid userId, Guid participantId,
            string elasticId, string skuCode, int quantity = 1, decimal? priceDefinedByParticipant = null)
        {
            await ValidateCampaignStatus(campaignId);
            try
            {
                var product = await productsCatalogRepository.GetProductForCalculationByElasticId(campaignId, userId, participantId, elasticId, skuCode);
                if (product == null)
                    throw MotivaiException.ofValidation("Produto não encontrado.");

                var partnerSettings = await this.companyRepository.GetPartnerSettings(product.PartnerId);
                product.SetPartnerSettings(new PartnerSettingsModel(partnerSettings));

                if (product.IsDynamicPriceDefinedByParticipant()) {
                    // LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - ElasticId {} - SKU {} - Utilizando preco customizado: {}",
                    //         campaignId, userId, elasticId, skuCode, priceDefinedByParticipant);
                    product.SetDynamicPriceToCalculate(priceDefinedByParticipant);
                }

                await catalogProductService.ApplyDisponibilityAndPrices(campaignId, userId, product);

                return AvailabilityModel.Of(product.Available, product.Prices, product.Quantity);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Product - Availability", "Erro ao consultar disponibilidade");
                throw ex.WrapIfNotValidationException("CATALOG_PRODUCT_NOT_FOUND",
                        "Ocorreu um erro ao efetuar consulta da disponibilidade do produto.");

            }
        }
    }
}
