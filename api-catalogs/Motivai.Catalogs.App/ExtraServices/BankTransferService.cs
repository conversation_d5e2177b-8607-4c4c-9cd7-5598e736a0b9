using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.Prices;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.ExtraServices
{
	public class BankTransferService : IBankTransferService
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly IPricesRepository pricesRepository;
		private readonly IBankTransferRepository cashbackRepository;

		public BankTransferService(ICampaignRepository campaignRepository, IPricesRepository pricesRepository,
			IBankTransferRepository cashbackRepository)
		{
			this.campaignRepository = campaignRepository;
			this.pricesRepository = pricesRepository;
			this.cashbackRepository = cashbackRepository;
		}

		public Task<dynamic> GetBankTransferConfiguration(Guid campaignId)
		{
			return campaignRepository.GetBankTransferConfiguration(campaignId);
		}

		public async Task<dynamic> CalculateBankTransferOrderFees(Guid campaignId, dynamic order)
		{
			if (order == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
			order.campaignId = campaignId;
			return await pricesRepository.CalculateBankTransferOrderFees(order);
		}

		public async Task<dynamic> CreateBankTransferOrder(Guid campaignId, dynamic order)
		{
			if (order == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
			order.campaignId = campaignId;

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			if (campaignSettings.Parametrizations.LimitRedemptionPeriod)
			{
				if (!DateTime.UtcNow.Between(campaignSettings.Parametrizations.RedemptionPeriodStart, campaignSettings.Parametrizations.RedemptionPeriodEnd))
					throw MotivaiException.ofValidation("Não é permitido efetuar cash back fora do período de resgate");
			}

			var nowInSaoPaulo = DateTime.UtcNow.ToTimeZoneSaoPaulo();
			if (nowInSaoPaulo.Hour < 8 || nowInSaoPaulo.Hour >= 20 || nowInSaoPaulo.DayOfWeek == DayOfWeek.Saturday || nowInSaoPaulo.DayOfWeek == DayOfWeek.Sunday)
			{
				LoggerFactory.GetLogger().Warn("Cmp {} - Usr {} - Tentativa de pedido de transferência bancária",
					campaignId, order.userId);
				throw MotivaiException.ofValidation("Funcionalidade indisponível no momento.");
			}

			return await cashbackRepository.CreateBankTransferOrder(order);
		}

		public async Task<dynamic> GetBankTransferOrderById(Guid campaignId, Guid orderId)
		{
			if (orderId == Guid.Empty)
				throw MotivaiException.ofValidation("Pedido de cartão inválido.");
			return await cashbackRepository.GetBankTransferOrderById(orderId);
		}
	}
}
