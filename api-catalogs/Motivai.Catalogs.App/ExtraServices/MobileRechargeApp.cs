using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.IApp.Accounts;
using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.Notifications;
using Motivai.Catalogs.Domain.Models.ExtraServices;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Partners;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Calculators;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.ExtraServices
{
	public class MobileRechargeApp : IMobileRechargeApp
	{
		private readonly Guid CAMPAIGN_WELLNESS_ID = Guid.Parse("7257b3d7-0d71-4833-8cbf-e9b72a421d19");
		private readonly Guid CAMPAIGN_WELLNESS_FULLY_ID = Guid.Parse("672080ce-1eb1-4342-99cb-af245eec8931");

		private readonly ICampaignRepository campaignRepository;
		private readonly ICatalogExtraServiceRepository catalogExtraServiceRepository;
		private readonly IAccountWalletDebiter accountWallet;
		private readonly INotificationRepository notificationRepository;

		public MobileRechargeApp(ICampaignRepository campaignRepository, ICatalogExtraServiceRepository catalogExtraServiceRepository,
				IAccountWalletDebiter accountWallet, INotificationRepository notificationRepository)
		{
			this.campaignRepository = campaignRepository;
			this.catalogExtraServiceRepository = catalogExtraServiceRepository;
			this.accountWallet = accountWallet;
			this.notificationRepository = notificationRepository;
		}

		private async Task<MobilePartnerParametrization> GetActiveCampaignMobilePartnerIfEnabled(Guid campaignId)
		{
			// verifica se tem período de resgate
			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			if (campaignSettings.Parametrizations.LimitRedemptionPeriod)
			{
				if (!DateTime.UtcNow.Between(campaignSettings.Parametrizations.RedemptionPeriodStart, campaignSettings.Parametrizations.RedemptionPeriodEnd))
					throw MotivaiException.ofValidation("CAMPAIGN_OUT_OF_REDEEM_PERIOD",
						"Não é permitido efetuar pagamento de contas fora do período de resgate");
			}

			var nowInSaoPaulo = DateTime.UtcNow.ToTimeZoneSaoPaulo();
			if (campaignId != CAMPAIGN_WELLNESS_ID && campaignId != CAMPAIGN_WELLNESS_FULLY_ID)
			{
				if (nowInSaoPaulo.Hour < 8 || nowInSaoPaulo.Hour >= 20 || nowInSaoPaulo.DayOfWeek == DayOfWeek.Saturday || nowInSaoPaulo.DayOfWeek == DayOfWeek.Sunday)
				{
					throw MotivaiException.ofValidation("Funcionalidade indisponível no momento.");
				}
			}

			var mobilePartner = await campaignRepository.GetActiveMobilePartner(campaignId);
			if (mobilePartner == null)
			{
				throw MotivaiException.of("MOBILE_RECHARGE_PARTNER_INVALID", "Nenhum parceiro de recarga configurado.");
			}
			return mobilePartner;
		}

		public async Task<List<PartnerOperator>> GetAvailableProvidersForDdd(Guid campaignId, string ddd)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida.");
			if (string.IsNullOrEmpty(ddd))
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_DDD_INVALID", "DDD inválido.");

			var mobilePartner = await GetActiveCampaignMobilePartnerIfEnabled(campaignId);
			try
			{
				return await catalogExtraServiceRepository.GetOperatorsForMobilePartner(campaignId, mobilePartner.Partner, ddd);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "MobileRecharge - Carregar Operadoras",
					$"Erro ao carregar operadoras para DDD {ddd}.", true);
				if (ex is MotivaiException)
					throw ex;
				throw MotivaiException.of("MOBILE_RECHARGE_DDD_OPERATORS_QUERY_ERROR",
					"Não foi possível consultar as operadoras para recarga, por favor, tente novamente.", ex);
			}
		}

		///<summary>
		/// Calcula os totais para cada opção de recarga.
		///</summary>
		private async Task CalculateTotals(Guid campaignId, MobilePartnerParametrization mobilePartner, PartnerOperationOption option)
		{
			var campaignFee = await campaignRepository.GetFees(campaignId);

			if (mobilePartner.ConversionFactor.HasValue)
			{
				option.PointsFactor = mobilePartner.ConversionFactor.Value;
			}
			else
			{
				option.PointsFactor = campaignFee.PointsConversionFactor;
			}

			var conversor = AmountConversor.WithPointsConversionFactor(option.PointsFactor);

			option.PartnerCost = conversor.ApplyPointsFactorAndCreateAmount(option.RechargeCost);

			var partialTotalPoints = option.PartnerCost.GetPoints();

			// Aplica a taxa do Parceiro da integração
			option.PartnerFee = mobilePartner.PartnerFee;
			option.PartnerFeeAmount = conversor.ApplyPointsFactorAndCreateAmount(
				AmountValueHelper.RoundAmountValue(PercentageCalculator.CalculatePartUsingPercentage(option.RechargeCost, mobilePartner.PartnerFee))
			);
			partialTotalPoints += option.PartnerFeeAmount.GetPoints();

			// Aplica a taxa da Motivai
			option.GpFee = mobilePartner.GpFee;
			decimal gpFee = PercentageCalculator.CalculatePartUsingPercentage(partialTotalPoints, mobilePartner.GpFee);
			option.GpFeeAmount = conversor.RevertPointsFactorAppliedAndCreateAmount(gpFee);
			partialTotalPoints = partialTotalPoints + gpFee;

			// Aplica a taxa geral de parceiros do cliente
			option.ClientPartnerFee = campaignFee.ClientPartnersFee * 100;
			decimal clientPartnerFee = PercentageCalculator.CalculatePartUsingDecimal(partialTotalPoints, campaignFee.ClientPartnersFee);
			option.ClientPartnerFeeAmount = conversor.RevertPointsFactorAppliedAndCreateAmount(clientPartnerFee);
			partialTotalPoints = partialTotalPoints + clientPartnerFee;

			option.ParticipantCost = conversor.RevertPointsFactorAppliedAndCreateAmount(partialTotalPoints);
		}

		private async Task<PartnerOperationOption> CreateOption(Guid campaignId, MobilePartnerParametrization mobilePartner, PartnerOperationCost partnerOptionCost)
		{
			var option = new PartnerOperationOption()
			{
				Description = partnerOptionCost.CreditName,
				RechargeCost = partnerOptionCost.Cost,
				BonusValue = partnerOptionCost.BonusValue,
				Validate = partnerOptionCost.Validate
			};
			await CalculateTotals(campaignId, mobilePartner, option);
			return option;
		}

		public async Task<List<PartnerOperationOption>> GetOperatorOptionsForDdd(Guid campaignId, string ddd, string operatorId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida.");
			if (string.IsNullOrEmpty(ddd))
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_DDD_INVALID", "DDD inválido.");
			if (string.IsNullOrEmpty(operatorId))
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_OPERATOR_INVALID", "Operadora inválida.");

			var mobilePartner = await GetActiveCampaignMobilePartnerIfEnabled(campaignId);
			List<PartnerOperationCost> partnerOptionsCost = null;
			try
			{
				partnerOptionsCost = await catalogExtraServiceRepository.GetPartnerOptionsCostsForDdd(campaignId, mobilePartner.Partner, ddd, operatorId);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "MobileRecharge - Carregar Opções",
					$"Erro ao carregar opções de recarga para DDD {ddd} e operadora {operatorId}.", true);
				if (ex is MotivaiException)
					throw ex;
				throw MotivaiException.of("MOBILE_RECHARGE_OPERATOR_OPTIONS_QUERY_ERROR",
					"Não foi possível consultar opções de recarga, por favor, tente novamente.", ex);
			}
			if (partnerOptionsCost == null || partnerOptionsCost.Count == 0) return null;

			var options = new List<PartnerOperationOption>();

			await partnerOptionsCost.ForEachAsync(async partnerOption =>
			{
				if (partnerOption == null || partnerOption.Cost <= 0) return;
				options.Add(await CreateOption(campaignId, mobilePartner, partnerOption));
			});

			return options;
		}

		public async Task<ExtraServiceOperationResult> IssueRechargeMobileTicket(Guid campaignId, MobileRechargeIssue rechargeIssue)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida.");
			if (rechargeIssue == null)
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_INVALID", "Preencha todos os dados para continuar.");

			rechargeIssue.Validate();

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Celular {2} - Valor {3} - Preparando recarga",
				campaignId, rechargeIssue.UserId, rechargeIssue.CellphoneNumber, rechargeIssue.RechargeCost);

			var mobilePartner = await GetActiveCampaignMobilePartnerIfEnabled(campaignId);
			var option = new PartnerOperationOption()
			{
				RechargeCost = rechargeIssue.RechargeCost
			};
			await CalculateTotals(campaignId, mobilePartner, option);
			if (rechargeIssue.ParticipantCost.GetPointsOrZero() != option.ParticipantCost.GetPointsOrZero())
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_TOTAL_CHANGED", "Houve alteração do valor calculado, por favor, tente novamente.");
			rechargeIssue.CopyFeesFrom(option);

			var participantBalance = await accountWallet.GetBalance(campaignId, rechargeIssue.UserId);
			if (participantBalance < rechargeIssue.ParticipantCost.GetPointsOrZero())
            {
                LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Saldo insuficiente para a recarga - Saldo: {2}",
                    campaignId, rechargeIssue.UserId, participantBalance);
				throw MotivaiException.ofValidation("USER_INSUFFICIENT_BALANCE", "Saldo insuficiente para efetuar a recarga.");
            }

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Celular {2} - Valor {3} - Iniciando recarga",
				campaignId, rechargeIssue.UserId, rechargeIssue.CellphoneNumber, rechargeIssue.RechargeCost);
			ExtraServiceOperationResult result;
			try
			{
				result = await catalogExtraServiceRepository.StartMobileRecharge(campaignId, rechargeIssue.UserId, mobilePartner.Partner, rechargeIssue);
				if (result == null)
				{
					throw MotivaiException.ofValidation("MOBILE_RECHARGE_CREATION_ERROR",
						"Não foi possível efetuar a recarga, por favor, tente novamente.");
				}
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Usr {1} - Celular {2} - Valor {3} - Erro ao iniciar recarga: {4}",
					campaignId, rechargeIssue.UserId, rechargeIssue.CellphoneNumber, rechargeIssue.RechargeCost, ex.Message);
				await ExceptionLogger.LogException(ex, "MobileRecharge - Iniciar Recarga",
					"Ocorreu um erro ao iniciar a recarga de celular.", true);
				if (ex is MotivaiException)
					throw ex;
				throw MotivaiException.of("MOBILE_RECHARGE_CREATION_ERROR",
					"Não foi possível iniciar a recarga, por favor, tente novamente.", ex);
			}

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Celular {2} - Valor {3} - Recarga iniciada {4}",
				campaignId, rechargeIssue.UserId, rechargeIssue.CellphoneNumber, rechargeIssue.RechargeCost, result.CatalogExtraServiceId);
			return result;
		}

		public async Task<ExtraServiceConfirmationResult> ConfirmRechargeMobile(Guid campaignId, MobileRechargeTicket ticket)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida.");
			if (ticket == null)
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_INVALID", "Preencha todos os dados para continuar.");

			var mobilePartner = await GetActiveCampaignMobilePartnerIfEnabled(campaignId);

			ticket.Validate();

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - ID {2} - Celular {3} - Valor {4} - Confirmando recarga {5}",
				campaignId, ticket.UserId, ticket.CatalogExtraServiceId, ticket.CellphoneNumber,
				ticket.RechargeValue, ticket.Confirm);

			if (ticket.Confirm)
			{
				var debitTransactionId = await accountWallet.Debit(campaignId, ticket.UserId, ticket.ParticipantId,
					TransactionOrigin.MobileRecharge, ticket.CatalogExtraServiceId, $"Recarga de Celular - {ticket.GetCellphone()}", ticket.ParticipantCost);
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - ID {2} - Debitado {3}",
					campaignId, ticket.UserId, ticket.CatalogExtraServiceId, debitTransactionId);
			}

			ExtraServiceConfirmationResult result;
			try
			{
				result = await catalogExtraServiceRepository.ConfirmMobileRecharge(campaignId, ticket.UserId, mobilePartner.Partner, ticket);
				if (result == null)
					throw MotivaiException.ofValidation("MOBILE_RECHARGE_CONFIRMATION_ERROR",
						"Não foi possível confirmar a recarga, por favor, tente novamente.");
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Usr {1} - ID {2} - Erro ao confirmar pagto: {3}",
					campaignId, ticket.UserId, ticket.CatalogExtraServiceId, ex.Message);
				await ExceptionLogger.LogException(ex, "MobileRecharge - Confirmar Recarga", "Ocorreu um erro ao confirmar a recarga de celular.", true);
				if (ex is MotivaiException)
					throw ex;
				throw MotivaiException.of("MOBILE_RECHARGE_CONFIRMATION_ERROR", "Não foi possível confirmar a recarga, por favor, tente novamente.", ex);
			}

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - ID {2} - Pagto confirmado: {3}",
				campaignId, ticket.UserId, ticket.CatalogExtraServiceId, result.IsConfirmed);
			if (result.IsConfirmed)
			{
				await SendNotification(campaignId, ticket);
			}
			return result;
		}

		private async Task SendNotification(Guid campaignId, MobileRechargeTicket ticket)
		{
			try
			{
				var emailSent = await notificationRepository.NotifyMobileRecharge(campaignId, ticket);
				if (!emailSent)
					throw MotivaiException.ofValidation("MOBILE_RECHARGE_NOTIFICATION_NOT_SENT", "Notificação de pedido de recarga de celular não foi enviada.");
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "MobileRecharge - Notificação", "Não foi possível enviar notificação de pedido de recarga de celular.", true);
			}
		}

        public async Task<dynamic> GetRechargeOrderById(Guid campaignId, Guid orderId) {
            if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (orderId == Guid.Empty)
                throw MotivaiException.ofValidation("Pedido de recarga inválido.");
            var rechargeOrder = await catalogExtraServiceRepository.GetRechargeOrderById(campaignId, orderId);
            if (rechargeOrder == null)
                throw MotivaiException.ofValidation("Pedido não encontrado.");
            return rechargeOrder;
        }
    }
}
