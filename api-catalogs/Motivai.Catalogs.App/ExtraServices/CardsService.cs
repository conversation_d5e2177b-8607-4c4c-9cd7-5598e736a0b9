using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.Prices;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Motivai.Catalogs.App.ExtraServices
{
    public class CardsService : ICardsService
    {
        private readonly IUserParticipantRepository participantRepository;
        private readonly ICampaignRepository campaignRepository;
        private readonly IPricesRepository pricesRepository;
        private readonly ICardsRepository cardsRepository;

        public CardsService(ICampaignRepository campaignRepository, IUserParticipantRepository participantRepository, IPricesRepository pricesRepository,
                ICardsRepository cardsRepository)
        {
            this.campaignRepository = campaignRepository;
            this.participantRepository = participantRepository;
            this.pricesRepository = pricesRepository;
            this.cardsRepository = cardsRepository;
        }

        public async Task<dynamic> GetCardParametrizations(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Campanha inválida.");
            }
            var parametrizations = await this.campaignRepository.GetCardParametrizations(campaignId);
            if (parametrizations == null)
            {
                throw MotivaiException.of(ErrorType.Configuration, "Parametrizações do cartão não encontradas");
            }
            return parametrizations;
        }

        public Task<dynamic> GetCardsConfiguration(Guid campaignId, string cardType)
        {
            return campaignRepository.GetCardsPageConfiguration(campaignId, cardType);
        }

        public Task<List<dynamic>> GetParticipantActiveCards(Guid campaignId, Guid userId, PrepaidCardType? cardType)
        {
            return cardsRepository.GetParticipantActiveCards(campaignId, userId, cardType);
        }

        public Task<dynamic> CalculateCardOrderFees(Guid campaignId, dynamic order)
        {
            if (order == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            order.campaignId = campaignId;
            return pricesRepository.CalculateCardOrderFees(order);
        }

        public async Task<dynamic> CreateCardOrder(Guid campaignId, dynamic order)
        {
            if (order == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");
            order.campaignId = campaignId;

            var campaignSettings = await this.campaignRepository.GetSettings(campaignId);

            if (campaignSettings.Parametrizations.LimitRedemptionPeriod)
            {
                if (!DateTime.UtcNow.Between(campaignSettings.Parametrizations.RedemptionPeriodStart, campaignSettings.Parametrizations.RedemptionPeriodEnd))
                    throw MotivaiException.ofValidation("Não é permitido efetuar pedido de cartão fora do período de resgate");
            }
            var nowInSaoPaulo = DateTime.UtcNow.ToTimeZoneSaoPaulo();
            if (nowInSaoPaulo.Hour < 8 || nowInSaoPaulo.Hour >= 20 || nowInSaoPaulo.DayOfWeek == DayOfWeek.Saturday || nowInSaoPaulo.DayOfWeek == DayOfWeek.Sunday)
            {
                LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Tentativa de pedido de cartão pré-pago",
                    campaignId, order.useId);
                throw MotivaiException.ofValidation("Funcionalidade indisponível no momento.");
            }

            if (campaignSettings.Parametrizations.EnableAccountRepresentative)
            {
                var participantInfo = await participantRepository.GetParticipantInfo(Guid.Parse(order.userId.ToString()), campaignId);

                if (participantInfo.Type == PersonType.Juridica)
                {
                    if (participantInfo.AccountRepresentative == null || string.IsNullOrEmpty(participantInfo.AccountRepresentative.Document))
                    {
                        throw MotivaiException.ofValidation("Para prosseguir preencha o CPF para pedidos no seu cadastro em minha conta.");
                    }

                    var accountRepresentative = participantInfo.AccountRepresentative;

                    order.accountRepresentative = JObject.FromObject(new
                    {
                        name = accountRepresentative.Name,
                        document = Extractor.RemoveMasks(accountRepresentative.Document),
                        telephone = accountRepresentative.Telephone,
                        email = accountRepresentative.Email
                    });
                }
            }

            order.campaignSnapshotSettings = JObject.FromObject(new
            {
                enableAccountRepresentative = campaignSettings.Parametrizations.EnableAccountRepresentative,
                originTaxIdForInvoicing = campaignSettings.Parametrizations.OriginTaxIdForInvoicing
            });

            return await cardsRepository.CreateCardOrder(order);
        }

        public async Task<CardOrder> GetCardOrderById(Guid campaignId, Guid orderId)
        {
            if (orderId == Guid.Empty)
                throw MotivaiException.ofValidation("Pedido de cartão inválido.");
            var cardOrder = await cardsRepository.GetCardOrderById(orderId);
            if (cardOrder == null)
                throw MotivaiException.ofValidation("Pedido não encontrado.");
            cardOrder.OrderTotalCost.Currency = cardOrder.OrderTotalCost.Points;
            return cardOrder;
        }
    }
}
