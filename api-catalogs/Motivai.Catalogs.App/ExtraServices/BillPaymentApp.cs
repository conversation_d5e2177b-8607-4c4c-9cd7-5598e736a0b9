using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.IApp.Accounts;
using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.Notifications;
using Motivai.Catalogs.Domain.IRepository.Transactions;
using Motivai.Catalogs.Domain.Models.ExtraServices;
using Motivai.Catalogs.Domain.Services.Refunds;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Partners;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Calculators;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.ExtraServices
{
	public class BillPaymentApp : IBillPaymentApp
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly ICatalogExtraServiceRepository catalogExtraServiceRepository;
		private readonly IAccountWalletDebiter accountWallet;
		private readonly INotificationRepository notificationRepository;
		private readonly IUserParticipantRepository userParticipantRepository;
		private readonly ITransactionRepository transactionRepository;
		private readonly RefundProcessorService refundProcessorService;

		public BillPaymentApp(ICampaignRepository campaignRepository, ICatalogExtraServiceRepository catalogExtraServiceRepository,
			IAccountWalletDebiter accountWallet, INotificationRepository notificationRepository, IUserParticipantRepository userParticipantRepository,
			ITransactionRepository transactionRepository, RefundProcessorService refundProcessorService)
		{
			this.campaignRepository = campaignRepository;
			this.catalogExtraServiceRepository = catalogExtraServiceRepository;
			this.accountWallet = accountWallet;
			this.notificationRepository = notificationRepository;
			this.userParticipantRepository = userParticipantRepository;
			this.transactionRepository = transactionRepository;
			this.refundProcessorService = refundProcessorService;
		}

		private async Task<BillPaymentPartnerParametrization> GetActiveBillPaymentPartnerIfEnabled(Guid campaignId, string serviceType = null)
		{
			CampaignSettingsModel campaignSettings;
			try
			{
				campaignSettings = await campaignRepository.GetSettings(campaignId);
			}
			catch (Exception ex)
			{
				throw MotivaiException.WrapIfNotValidationException("CAMPAIGN_CONFIGURATION",
					"Não foi possível carregar as configurações da campanha, por favor, tente novamente.", ex);
			}

			if (campaignSettings.Parametrizations.LimitRedemptionPeriod)
			{
				if (!DateTime.UtcNow.Between(campaignSettings.Parametrizations.RedemptionPeriodStart, campaignSettings.Parametrizations.RedemptionPeriodEnd))
				{
					throw MotivaiException.ofValidation("CAMPAIGN_OUT_OF_REDEEM_PERIOD",
						"Não é permitido efetuar pagamento de contas fora do período de resgate");
				}
			}

			var nowInSaoPaulo = DateTime.UtcNow.ToTimeZoneSaoPaulo();
			if (nowInSaoPaulo.Hour < 8 || nowInSaoPaulo.Hour >= 20 || nowInSaoPaulo.DayOfWeek == DayOfWeek.Saturday || nowInSaoPaulo.DayOfWeek == DayOfWeek.Sunday)
			{
				throw MotivaiException.ofValidation("Funcionalidade indisponível no momento.");
			}

			BillPaymentPartnerParametrization partner;
			try
			{
				partner = await campaignRepository.GetActiveBillPaymentPartner(campaignId, serviceType);
			}
			catch (Exception ex)
			{
				throw MotivaiException.of("CAMPAIGN_PARTNER_CONFIGURATION",
					"Não foi possível carregar o parceiro de pague contas, por favor, tente novamente.", ex);
			}

			if (partner == null || !partner.Active)
			{
				throw MotivaiException.of("BILL_PAYMENT_PARTNER_INVALID", "Nenhum parceiro de pague contas configurado.");
			}
			return partner;
		}

		///<summary>
		/// Calcula os totais para o pague contas
		///</summary>
		private async Task CalculateTotals(Guid campaignId, BillPaymentPartnerParametrization servicePartner, BillDetails billDetails)
		{
			var campaignFee = await campaignRepository.GetFees(campaignId);
			billDetails.BillPaymentPartner = servicePartner.Partner;
			billDetails.PointsFactor = 1;
			if (servicePartner.ConversionFactor.HasValue)
			{
				billDetails.PointsFactor = servicePartner.ConversionFactor.Value;
			}
			else
			{
				billDetails.PointsFactor = campaignFee.PointsConversionFactor;
			}

			var conversor = AmountConversor.WithPointsConversionFactor(billDetails.PointsFactor);

			billDetails.PartnerCost = conversor.ApplyPointsFactorAndCreateAmount(billDetails.BillingAmount);
			var partialTotalPoints = billDetails.PartnerCost.GetPoints();

			// Aplica a taxa do Parceiro da integração
			billDetails.PartnerFee = servicePartner.PartnerFee;
			billDetails.PartnerFeeAmount = conversor.ApplyPointsFactorAndCreateAmount(
				AmountValueHelper.RoundAmountValue(PercentageCalculator.CalculatePartUsingPercentage(billDetails.BillingAmount, servicePartner.PartnerFee))
			);
			partialTotalPoints += billDetails.PartnerFeeAmount.GetPoints();

			// Aplica a taxa da Motivai
			billDetails.GpFee = servicePartner.GpFee;
			decimal gpFee = PercentageCalculator.CalculatePartUsingPercentage(partialTotalPoints, servicePartner.GpFee);
			billDetails.GpFeeAmount = conversor.RevertPointsFactorAppliedAndCreateAmount(gpFee);
			partialTotalPoints = partialTotalPoints + gpFee;

			// Aplica a taxa geral de parceiros do cliente
			billDetails.ClientPartnerFee = campaignFee.ClientPartnersFee * 100;
			decimal clientPartnerFee = PercentageCalculator.CalculatePartUsingDecimal(partialTotalPoints, campaignFee.ClientPartnersFee);
			billDetails.ClientPartnerFeeAmount = conversor.RevertPointsFactorAppliedAndCreateAmount(clientPartnerFee);
			partialTotalPoints = partialTotalPoints + clientPartnerFee;

			billDetails.ParticipantCost = conversor.RevertPointsFactorAppliedAndCreateAmount(partialTotalPoints);
		}

		private async Task ValidateBillForPayment(Guid campaignId, BillDetails billDetails, BillPaymentPartnerParametrization servicePartner)
		{
			await this.ValidateBillPaymentMinimumDueDays(campaignId, billDetails);

			// Se estiver agendando então valida data selecionada
			if (billDetails.ScheduledPaymentDate.HasValue)
			{
				billDetails.ScheduledPaymentDate = billDetails.ScheduledPaymentDate.Value.AtStartOfDay();
				// Se estiver agendando então valida data selecionada
				if (billDetails.MinimumScheduledPaymentDate.HasValue && billDetails.ScheduledPaymentDate.Value < billDetails.MinimumScheduledPaymentDate.Value.AtStartOfDay())
				{
					throw MotivaiException.ofValidation("BILL_PAYMENT_MINIMUM_SCHEDULING_DATE_EXCEEDED",
						$"Não é permitido agendar pagamento para antes do dia {billDetails.MinimumScheduledPaymentDate.Value:dd/MM/yyyy}.");
				}

				if (billDetails.MaximumScheduledPaymentDate.HasValue && billDetails.ScheduledPaymentDate.Value > billDetails.MaximumScheduledPaymentDate.Value.AtEndOfDay())
				{
					throw MotivaiException.ofValidation("BILL_PAYMENT_MAXIMUM_SCHEDULING_DATE_EXCEEDED",
						$"Não é permitido agendar pagamento para depois do dia {billDetails.MaximumScheduledPaymentDate.Value:dd/MM/yyyy}.");
				}
			}

			// Asserção do valor
			var receivedTotal = billDetails.ParticipantCost.GetPointsOrZero();
			await CalculateTotals(campaignId, servicePartner, billDetails);
			// TODO: se o parceiro de consulta e o parceiro de liquidação tiverem taxas diferentes aqui poderá variar
			if (receivedTotal != billDetails.ParticipantCost.GetPointsOrZero())
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_TOTAL_CHANGED", "Houve alteração do valor calculado, por favor, tente novamente.");
			}
		}

		private async Task ValidateBillPaymentMinimumDueDays(Guid campaignId, BillDetails bill)
		{
			if (bill.BillingAmount >= 250_000)
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_MAXIMUM_AMOUNT_EXCEEDED", "Não é permitido o pagamento de boletos com valor maior ou igual a R$ 250.000,00.");
			}

			var campaignSettings = await this.campaignRepository.GetSettings(campaignId);

			// Cálculo da data mínima para agendamento
			var saoPauloDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, DateHelper.TIME_ZONE_SAO_PAULO);

			// Verifica se está dentro do horário permitido para agendar no mesmo dia
			if ((campaignSettings.Parametrizations.BillPaymentMinimumHour.HasValue && saoPauloDateTime.Hour >= campaignSettings.Parametrizations.BillPaymentMinimumHour.Value) &&
				(campaignSettings.Parametrizations.BillPaymentMaximumHour.HasValue && saoPauloDateTime.Hour < campaignSettings.Parametrizations.BillPaymentMaximumHour.Value))
			{
				bill.MinimumScheduledPaymentDate = DateTime.Today;
			}
			else
			{
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Horário {2} fora do período de agendamento das {3} as {4}.",
					campaignId, bill.UserId, saoPauloDateTime.Hour,
					campaignSettings.Parametrizations.BillPaymentMinimumHour, campaignSettings.Parametrizations.BillPaymentMaximumHour);
				bill.MinimumScheduledPaymentDate = DateTime.Today.AddDays(1);
			}
			bill.MinimumScheduledPaymentDate = bill.MinimumScheduledPaymentDate.Value.AtStartOfDay().AddHours(12);

			if (bill.DueDate == null || !bill.DueDate.HasValue)
			{
				return;
			}
			bill.DueDate = bill.DueDate.Value.AtEndOfDay();

			if (campaignSettings.Parametrizations.HasBillPaymentMinimumDueDays())
			{
				// Cálculo da qtde de dias mínimo permitido para agendamento antes do vencimento
				var days = campaignSettings.Parametrizations.BillPaymentMinimumDueDays.Value;
				var today = DateTime.UtcNow.AddDays(days).AtEndOfDay();
				if (today > bill.DueDate)
				{
					LoggerFactory.GetLogger().Warn("Cmp {0} - Usr {1} - Boleto {2} está abaixo do período mínimo de vencimento ({3})",
						campaignId, bill.UserId, bill.BarCode, days);
					throw MotivaiException.ofValidation("BILL_PAYMENT_MINIMUM_DUE_DATE_EXCEEDED",
						$"Não é permitido o pagamento de conta com menos de {days} dia(s) para o vencimento.");
				}

				var dueDate = bill.DueDate.Value;
				for (int i = 0; i < days;)
				{
					dueDate = dueDate.AddDays(-1);
					if (dueDate.DayOfWeek != DayOfWeek.Saturday && dueDate.DayOfWeek != DayOfWeek.Sunday)
					{
						i++;
					}
				}
				bill.MaximumScheduledPaymentDate = dueDate;
			}
			else
			{
				bill.MaximumScheduledPaymentDate = bill.DueDate;
			}
			// Se a data de venc cair no final de semana avança para segunda-feira (próximo dia útil até consulta de calendário)
			if (bill.MaximumScheduledPaymentDate.HasValue && bill.MaximumScheduledPaymentDate.IsWeekend())
			{
				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Boleto {2} com vencimento no final de semana",
					campaignId, bill.UserId, bill.BarCode);
				int daysToAdd = bill.MaximumScheduledPaymentDate.Value.DayOfWeek == DayOfWeek.Saturday ? 2 : 1;
				bill.MaximumScheduledPaymentDate = bill.MaximumScheduledPaymentDate.Value.AddDays(daysToAdd);
			}
		}

		public async Task<BillDetails> GetBillDetails(Guid campaignId, string barCode, Guid userId, string userDocument,
			string userAccountOperatorDocument, Guid accountOperatorId, Guid accountOperatorLoginId,
			bool skipBalanceValidation = false)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida");
			if (userId == Guid.Empty)
				throw MotivaiException.ofValidation("USER_INVALID", "Usuário inválido");
			if (string.IsNullOrEmpty(barCode))
				throw MotivaiException.ofValidation("BILL_PAYMENT_BARCODE_INVALID", "Linha digitável inválido.");

			var servicePartner = await GetActiveBillPaymentPartnerIfEnabled(campaignId);

			BillDetails billDetails;
			try
			{
				billDetails = await catalogExtraServiceRepository.GetBillDetails(campaignId, servicePartner.Partner, barCode);
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Usr {1} - Boleto {2} - Erro do parceiro: {3}", campaignId, userId, barCode, ex.InnerException?.Message ?? ex.Message);
				await ExceptionLogger.LogException(ex, "BillPayment - Consulta Conta", "Erro ao consultar a conta");
				throw MotivaiException.WrapIfNotValidationException("BILL_PAYMENT_BARCODE_QUERY_ERROR",
					"Não foi possível consultar a conta, por favor, tente novamente.", ex);
			}

			if (billDetails == null) return null;

			billDetails.ValidateBillDetails(servicePartner);


			await HasBillPaymentPartnerWithFilters(campaignId, billDetails, servicePartner, userId, userDocument,
				userAccountOperatorDocument, accountOperatorId, accountOperatorLoginId);

			billDetails.UserId = userId;
			billDetails.BillDetailsQueryPartner = servicePartner.Partner;
			billDetails.BillPaymentPartner = servicePartner.Partner;

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Boleto {2} - Valor {3} - Data de vencimento retornada: {4}",
				campaignId, billDetails.UserId, billDetails.BarCode, billDetails.BillingAmount, billDetails.DueDate?.ToString("dd/MM/yyyy"));

			await ValidateBillPaymentMinimumDueDays(campaignId, billDetails);

			await CalculateTotals(campaignId, servicePartner, billDetails);

			if (!skipBalanceValidation)
			{
				var participantBalance = await accountWallet.GetBalance(campaignId, userId);
				if (participantBalance < billDetails.ParticipantCost.GetPointsOrZero())
				{
					LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Boleto {2} - Saldo insuficiente para o pagto de conta - Saldo: {3}",
						campaignId, billDetails.UserId, billDetails.BarCode, participantBalance);
					throw MotivaiException.ofValidation("USER_INSUFFICIENT_BALANCE",
						"Saldo insuficiente para efetuar o pagamento da conta.");
				}
			}

			return billDetails;
		}



		public async Task<ExtraServiceOperationResult> IssueBillPaymentTicket(Guid campaignId, BillDetails bill)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida");
			if (bill == null)
				throw MotivaiException.ofValidation("BILL_PAYMENT_INVALID", "Conta para pagamento inválida");

			bill.ValidateToPay();


			BillPaymentPartnerParametrization servicePartner;
			try
			{
				servicePartner = await GetActiveBillPaymentPartnerIfEnabled(campaignId, bill.ServiceType.ToString());
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Boleto {1} - Valor {2} - Erro ao buscar o parceiro de pague contas pelo tipo de serviço: {3}",
					campaignId, bill.BarCode, bill.ParticipantCost, ex.Message);
				await ExceptionLogger.LogException(ex, "BillPayment - Buscar parceiro de Pagto de conta", "Erro ao buscar o parceiro de pague contas pelo tipo de serviço.", true);
				throw MotivaiException.WrapIfNotValidationException("BILL_PAYMENT_CREATION_ERROR",
					"Ocorreu um erro ao buscar o parceiro de pague contas pelo tipo de serviço.", ex);
			}
			if (servicePartner == null)
				throw MotivaiException.ofValidation("BILL_PAYMENT_CREATION_ERROR", "Parceiro de pagamento de conta não encontrado.");

			await HasBillPaymentPartnerWithFilters(campaignId, bill, servicePartner, bill.UserId.Value,
					bill.UserDocument, bill.AccountOperator?.AccountOperatorDocument,
					bill.AccountOperator?.AccountOperatorId, bill.AccountOperator?.AccountOperatorLoginId);

			await ValidateBillForPayment(campaignId, bill, servicePartner);

			var participantBalance = await accountWallet.GetBalance(campaignId, bill.UserId.Value);
			if (participantBalance < bill.ParticipantCost.GetPointsOrZero())
				throw MotivaiException.ofValidation("USER_INSUFFICIENT_BALANCE", "Saldo insuficiente para efetuar o pagamento da conta.");

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Boleto {2} - Valor {3} - Iniciando pagto",
				campaignId, bill.UserId, bill.BarCode, bill.BillingAmount);
			ExtraServiceOperationResult result;
			try
			{
				bill.BillPaymentPartner = servicePartner.Partner;
				result = await catalogExtraServiceRepository.StartBillPayment(campaignId, bill.UserId.Value, servicePartner.Partner, bill);
				if (result == null)
				{
					throw MotivaiException.ofValidation("BILL_PAYMENT_CREATION_ERROR",
						"Não foi possível efetuar o pagamento da conta, por favor, tente novamente.");
				}
				result.BillPaymentPartner = servicePartner.Partner;
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Usr {1} - Boleto {2} - Valor {3} - Erro ao iniciar pagto: {4} - Erro do parceiro: {5}",
					campaignId, bill.UserId, bill.BarCode, bill.BillingAmount, ex.Message, ex.InnerException?.Message);
				await ExceptionLogger.LogException(ex, "BillPayment - Iniciar Pagto", "Ocorreu um erro efetuar o pagamento da conta.", true);

				throw MotivaiException.WrapIfNotValidationException("BILL_PAYMENT_CREATION_ERROR",
					"Ocorreu um erro efetuar o pagamento da conta, por favor, tente novamente.", ex);
			}

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - Boleto {2} - Valor {3} - Pagto iniciado {4}",
				campaignId, bill.UserId, bill.BarCode, bill.BillingAmount, result.CatalogExtraServiceId);
			return result;
		}

		private async Task HasBillPaymentPartnerWithFilters(Guid campaignId, BillDetails bill,
			BillPaymentPartnerParametrization servicePartner, Guid userId, string userDocument,
			string accountOperatorDocument, Guid? accountOperatorId, Guid? accountOperatorLoginId)
		{
			if (servicePartner.HasAnyPaymentFilter())
			{
				var billsPaymentPartnerFilters = BillsPaymentPartnerFilters.BuildFrom(bill, servicePartner.Partner, userId,
					userDocument, accountOperatorDocument, accountOperatorId, accountOperatorLoginId
				);
				var hasBillPaymentPartnerForServiceType = await campaignRepository.HasBillPaymentPartnerWithFilters(campaignId, billsPaymentPartnerFilters);
				if (hasBillPaymentPartnerForServiceType == null)
				{
					var errorMessage = servicePartner.Parametrizations?.FilterRejectionMessage;
					if (string.IsNullOrEmpty(errorMessage))
						errorMessage = "Não é permitido o pagamento desse tipo de conta.";

					throw MotivaiException.ofValidation(
						"BILL_PAYMENT_PARTNER_BY_SERVICE_TYPE_NOT_EXISTENT", errorMessage, true
					);
				}
			}
		}

		public async Task<ExtraServiceConfirmationResult> ConfirmBillPaymentTicket(Guid campaignId, BillPaymentTicket ticket)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida");
			if (ticket == null)
				throw MotivaiException.ofValidation("BILL_PAYMENT_INVALID", "Conta para pagamento inválida");
			ticket.Validate();

			var servicePartner = await GetActiveBillPaymentPartnerIfEnabled(campaignId, ticket.ServiceType);
			if (servicePartner.Partner != ticket.BillPaymentPartner)
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_PARTNER_INVALID",
					"Parceiro configurado na campanha não é o mesmo utilizado na criação do pagamento.");
			}

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - ID {2} - Boleto {3} - Valor {4} - Confirmando pagto {5}",
				campaignId, ticket.UserId, ticket.CatalogExtraServiceId, ticket.BarCode, ticket.BillingAmount, ticket.Confirm);

			Guid debitTransactionId = Guid.Empty;
			if (ticket.Confirm)
			{
				debitTransactionId = await accountWallet.Debit(campaignId, ticket.UserId, ticket.ParticipantId,
						TransactionOrigin.BillPayment, ticket.CatalogExtraServiceId, $"Pagto Contas - {ticket.Assignor}", ticket.ParticipantCost);

				LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - ID {2} - Debitado {3}",
					campaignId, ticket.UserId, ticket.CatalogExtraServiceId, debitTransactionId);
			}

			ExtraServiceConfirmationResult result;
			try
			{
				result = await catalogExtraServiceRepository.ConfirmBillPayment(campaignId, ticket.UserId, servicePartner.Partner, ticket);
				if (result == null)
					throw MotivaiException.ofValidation("BILL_PAYMENT_CONFIRMATION_ERROR", "Não foi possível efetuar o pagamento da conta, por favor, tente novamente.");
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Usr {1} - ID {2} - Erro ao confirmar pagto: {3} - Error do parceiro: {4}",
					campaignId, ticket.UserId, ticket.CatalogExtraServiceId, ex.Message, ex.InnerException?.Message);
				await ExceptionLogger.LogException(ex, "BillPayment - Confirmar Pagto", "Ocorreu um erro efetuar o pagamento da conta.", true);
				await this.refundProcessorService.RefundTransactionById(campaignId, ticket.UserId, debitTransactionId, ticket.CatalogExtraServiceId, TransactionOrigin.BillPayment, "Estorno Pagamento de Contas - " + ticket.Assignor);
				throw MotivaiException.WrapIfNotValidationException("BILL_PAYMENT_CONFIRMATION_ERROR",
					"Ocorreu um erro efetuar o pagamento da conta, por favor, tente novamente.", ex);
			}

			LoggerFactory.GetLogger().Info("Cmp {0} - Usr {1} - ID {2} - Pagto confirmado: {3}",
				campaignId, ticket.UserId, ticket.CatalogExtraServiceId, result.IsConfirmed);
			if (result.IsConfirmed)
			{
				await SendNotification(campaignId, ticket);
			}
			return result;
		}

		public async Task<dynamic> ScheduleBillPayment(Guid campaignId, BillDetails billDetails)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("CAMPAIGN_INVALID", "Campanha inválida");
			if (billDetails == null)
				throw MotivaiException.ofValidation("BILL_PAYMENT_INVALID", "Conta para pagamento inválida");
			billDetails.ValidateToSchedule();

			var campaignSettings = await campaignRepository.GetSettings(campaignId);

			if (campaignSettings.Parametrizations.LimitRedemptionPeriod)
			{
				if (!DateTime.UtcNow.Between(campaignSettings.Parametrizations.RedemptionPeriodStart, campaignSettings.Parametrizations.RedemptionPeriodEnd))
					throw MotivaiException.ofValidation("CAMPAIGN_OUT_OF_REDEEM_PERIOD", "Não é permitido efetuar pagamento de contas fora do período de resgate");
			}

			var nowInSaoPaulo = DateTime.UtcNow.ToTimeZoneSaoPaulo();
			if (nowInSaoPaulo.Hour < 8 || nowInSaoPaulo.Hour >= 20 || nowInSaoPaulo.DayOfWeek == DayOfWeek.Saturday || nowInSaoPaulo.DayOfWeek == DayOfWeek.Sunday)
			{
				LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Tentativa de agendamento de pague contas",
					campaignId, billDetails.UserId);
				throw MotivaiException.ofValidation("Funcionalidade indisponível no momento.");
			}

			ParticipantInfo participantInfo = await this.userParticipantRepository.GetParticipantInfo(billDetails.UserId.Value, campaignId);
			billDetails.SetParticipantInfo(participantInfo);

			var servicePartner = await GetActiveBillPaymentPartnerIfEnabled(campaignId);
			await ValidateBillForPayment(campaignId, billDetails, servicePartner);

			var participantBalance = await accountWallet.GetBalance(campaignId, billDetails.UserId.Value);
			if (participantBalance < billDetails.ParticipantCost.GetPointsOrZero())
				throw MotivaiException.ofValidation("USER_INSUFFICIENT_BALANCE", "Saldo insuficiente para efetuar o pagamento da conta.");

			var servicePartnerForPayment = await GetActiveBillPaymentPartnerIfEnabled(campaignId, billDetails.ServiceType.ToString());
			billDetails.BillPaymentPartner = servicePartnerForPayment.Partner;

			return await catalogExtraServiceRepository.ScheduleBillPayment(campaignId, billDetails);
		}

		private async Task SendNotification(Guid campaignId, BillPaymentTicket ticket)
		{
			try
			{
				var emailSent = await notificationRepository.NotifyBillPayment(campaignId, ticket);
				if (!emailSent)
					throw MotivaiException.ofValidation("BILL_PAYMENT_NOTIFICATION_NOT_SENT", "Notificação de pedido de pague contas não foi enviada.");
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "BillPayment - Notificação", "Não foi possível enviar notificação de pedido de pague contas.", true);
				LoggerFactory.GetLogger().Info("BillPayment - Cmp {0} - Usr {1} - Boleto {2} - Erro ao enviar notificação: {3}",
					campaignId, ticket.UserId, ticket.BarCode, ex.Message);
			}
		}

		public async Task<dynamic> GetBillPaymentsOrderById(Guid campaignId, Guid orderId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (orderId == Guid.Empty)
				throw MotivaiException.ofValidation("Pedido de pague contas inválido.");
			var billPaymentOrder = await catalogExtraServiceRepository.GetBillPaymentsOrderById(campaignId, orderId);
			if (billPaymentOrder == null)
				throw MotivaiException.ofValidation("Pedido não encontrado.");
			return billPaymentOrder;
		}
	}
}
