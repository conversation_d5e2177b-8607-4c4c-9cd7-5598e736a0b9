﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IApp.CampaignCatalog;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.Regions;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.Catalogo.App.CampaignsCatalog
{
    public class CampaignCatalogApp : ICampaignCatalogApp
    {
        private readonly CatalogProductService _catalogProductService;
        private readonly ICampaignRepository _campaignRepository;
        private readonly IProductCatalogRepository _productCatalogRepository;

        public CampaignCatalogApp(CatalogProductService catalogProductService, ICampaignRepository campanhaRepository,
            IProductCatalogRepository productCatalogRepository)
        {
            this._catalogProductService = catalogProductService;
            this._campaignRepository = campanhaRepository;
            this._productCatalogRepository = productCatalogRepository;
        }

        public async Task<Guid> GetCampaignIdByDomain(string domain)
        {
            domain.ForNullOrEmpty("Domínio inválido.");
            return await _campaignRepository.GetCampaignIdByDomain(domain);
        }

        public async Task<CoinName> GetCoinNameByCampaign(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            return await _campaignRepository.GetCoinName(campaignId);
        }

        public async Task<CampaignSettingsModel> GetSettingsByCampaign(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            return await _campaignRepository.GetSettings(campaignId);
        }

        public async Task<CampaignCatalogSettings> GetPagesSettingsByCampaign(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            return await _campaignRepository.GetPagesSettings(campaignId);
        }

        public async Task<dynamic> GetRegulationByCampaign(Guid campaignId, Guid? userId = null)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var groupRegulation = await _campaignRepository.GetActiveRegulation(campaignId, userId);
            if (groupRegulation == null) return new { };
            return new
            {
                groupRegulation.regulationId,
                groupRegulation.version,
                groupRegulation.regulationContent
            };
        }

        public async Task<dynamic> GetPrivacyPolicyByCampaign(Guid campaignId, Guid? userId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var shippingPolicy = await _campaignRepository.GetActivePrivacyPolicy(campaignId, userId);
            if (shippingPolicy == null) return new { };
            return new
            {
                shippingPolicy.contentId,
                shippingPolicy.version,
                shippingPolicy.content,
                shippingPolicy.cookieAcceptanceContent
            };
        }

        public async Task<dynamic> GetShippingPolicyByCampaign(Guid campaignId, Guid? userId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var shippingPolicy = await _campaignRepository.GetActiveShippingPolicy(campaignId, userId);
            if (shippingPolicy == null) return new { };
            return new
            {
                shippingPolicy.contentId,
                shippingPolicy.version,
                shippingPolicy.content
            };
        }

        public async Task<List<dynamic>> GetFaqsByCampaign(Guid campaignId, Guid? userId = null, string term = null)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            return await _campaignRepository.GetFaqs(campaignId, userId, term);
        }

        public async Task<List<ProductShowcaseModel>> GetHomeProductsByCampaign(Guid campaignId, Guid userId, Guid participantId,
            decimal? availableBalance = null, int? size = 0, bool? random = false)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            // Quantidade de produtos para exibir na home
            var qtyProds = 20;
            if (size.HasValue & size.Value > 0)
            {
                qtyProds = size.Value;
            }

            List<ProductShowcaseModel> homeProducts;
            if (random.HasValue && random.Value)
            {
                homeProducts = await GetHomeProductsRandom(campaignId, userId, participantId, qtyProds);
            }
            else
            {
                homeProducts = await GetHomeProductsByCampaignRules(campaignId, userId, participantId, availableBalance, qtyProds);
            }

            await _catalogProductService.CalculateOutdatedPrices(campaignId, userId, homeProducts, availableBalance);

            return homeProducts;
        }

        private async Task<List<ProductShowcaseModel>> GetHomeProductsByCampaignRules(Guid campaignId, Guid userId, Guid participantId, decimal? availableBalance, int qtyProds)
        {
            var homeProducts = new List<ProductShowcaseModel>();
            var take = qtyProds;

            // Carrega os produtos destacados do Marketing
            var featuredSkusIds = await _campaignRepository.GetHomeFeaturedProducts(campaignId);

            // Se tiver produtos configurados para home então é carregado
            if (!featuredSkusIds.IsNullOrEmpty())
            {

                var fSkusId = new List<Guid>();

                if (featuredSkusIds != null)
                    fSkusId = featuredSkusIds.Select(a => a.SkuId).ToList();

                var featuredProducts = await _productCatalogRepository.GetProductsBySkusIds(campaignId, userId, participantId, fSkusId);
                if (!featuredProducts.IsNullOrEmpty())
                {
                    homeProducts.AddRange(featuredProducts);
                    // Se tiver menos de 16 produtos destacados então carrega outros para preencher
                    take = featuredProducts.Count < qtyProds ? take - featuredProducts.Count : 0;
                }
            }

            // Carrega o restante dos produtos caso não tenha completado o tamanho estipulado
            if (take > 0)
            {
                decimal? fromCurrency = default;
                decimal? toCurrency = default;
                decimal balancePoints = availableBalance ?? 0;
                if (balancePoints > 0)
                {
                    var campaignFees = await _campaignRepository.GetFees(campaignId);
                    if (campaignFees.ClientPartnersFee > 0)
                    {
                        balancePoints *= (decimal)1.0 + campaignFees.ClientPartnersFee;
                    }
                    var balanceCurrency = AmountConversor.RevertPointsFactorApplied(campaignFees.PointsConversionFactor, balancePoints);
                    fromCurrency = balanceCurrency * (decimal)0.9;
                    toCurrency = balanceCurrency * (decimal)1.1;
                }

                var products = await _productCatalogRepository.GetProducts(campaignId, userId, participantId, null, null, 0, take, fromCurrency, toCurrency);
                // carrega novamente sem aplicar filtro de pontos
                if (products.IsNullOrEmpty() && balancePoints > 0)
                {
                    products = await _productCatalogRepository.GetProducts(campaignId, userId, participantId, null, null, 0, take);
                }

                if (!products.IsNullOrEmpty())
                {
                    homeProducts.AddRange(products.Where(prod => !homeProducts.Any(hp => hp.Id == prod.Id)));
                    take = homeProducts.Count < qtyProds ? qtyProds - homeProducts.Count : 0;

                    // Carrega novamente sem filtrar por pontos
                    if (take > 0)
                    {
                        // TODO: Take, em casos de poucos produtos pode acabar trazendo os msms produtos acima
                        products = await _productCatalogRepository.GetProducts(campaignId, userId, participantId, null, null, 0, take);
                        if (!products.IsNullOrEmpty())
                        {
                            homeProducts.AddRange(products.Where(prod => !homeProducts.Any(hp => hp.Id == prod.Id)));
                        }
                    }
                }
            }

            return homeProducts;
        }

        private Task<List<ProductShowcaseModel>> GetHomeProductsRandom(Guid campaignId, Guid userId, Guid participantId, int qtyProds)
        {
            return _productCatalogRepository.GetProducts(campaignId, userId, participantId, null, null, 0, qtyProds);
        }

        public Task<CampaignCatalogSettingsFooter> GetFooterSettingsByCampaign(Guid campaignId)
        {
            if (campaignId == Guid.Empty) throw MotivaiException.ofValidation("Campanha inválida.");
            return this._campaignRepository.GetCatalogFooterSettingsByCampaign(campaignId);
        }
    }
}