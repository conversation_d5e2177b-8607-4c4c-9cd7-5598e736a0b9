﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.Catalogs.Domain.Services.ShoppingCarts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using System.Collections.Generic;
using Motivai.Catalogs.Domain.Services.Security;

namespace Motivai.Catalogo.App
{
    public class OrderCatalogApp : IOrderCatalogApp
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly IUserParticipantRepository participantRepository;
		private readonly IOrderRepository _orderRepository;
		private readonly IProductCatalogRepository _productRepository;
		private readonly ICorreiosRepository _correiosRepository;
		private readonly ICompanyRepository _companyRepository;
		private readonly IEmailRepository _emailRepository;

		private readonly ShoppingCartManager shoppingCartManager;

		public OrderCatalogApp(ICampaignRepository campaignRepository, ICompanyRepository companyRepository,
			IProductCatalogRepository productRepository, ICorreiosRepository correiosRepository, IOrderRepository orderRepository,
			IUserParticipantRepository participantRepository, IEmailRepository emailRepository,
			ShoppingCartManager shoppingCartManager)
		{
			this.campaignRepository = campaignRepository;
			this._orderRepository = orderRepository;
			this.participantRepository = participantRepository;
			this._productRepository = productRepository;
			this._correiosRepository = correiosRepository;
			this._companyRepository = companyRepository;
			this._emailRepository = emailRepository;
			this.shoppingCartManager = shoppingCartManager;
		}

		#region Criação de Pedido

		public async Task<Cart> CreateOrder(Cart cart)
		{
			try
			{
				var masterOrder = await ValidateAndPrepareOrder(cart);
				var createdOrder = await _orderRepository.CreateOrder(masterOrder);
				cart.SetCreatedOrder(createdOrder);

				var childOrders = masterOrder.ChildrenOrders;
				var hasProduct = childOrders.Any(p => p.HasAnyProduct());
				var hasService = childOrders.Any(p => p.HasAnyService());

				// Migrado para o processo de validação do pedido
				// if (hasProduct)
				// {
				// 	await SendOrderEmail(cart.OrderId);
				// }

				// if (hasService)
				// {
				// 	await SendOrderServiceEmail(cart.OrderId);
				// }

				await shoppingCartManager.SetCartOrdered(cart.CampaignId, cart.UserId, cart.OrderId, cart.InternalOrderNumber);
				return cart;
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {} - Usr {} - Erro ao criar pedido: {}",
						cart.CampaignId, cart.UserId, ex.Message);
				await ExceptionLoggerMiddleware.HandleException(ex, "Orders - Finalizar Async",
						"Ocorreu um erro durante o processamento do pedido", true);
				throw MotivaiException.WrapIfNotValidationException(
					"ORDER_PROCESSING", "Ocorreu um erro durante o processamento do pedido.", ex
				);
			}
		}

		public async Task<Cart> CreateOrderSync(Cart cart)
		{
			try
			{
				var masterOrder = await ValidateAndPrepareOrder(cart);
				var createdOrder = await _orderRepository.CreateOrderSync(masterOrder);
				cart.SetCreatedOrder(createdOrder);
				return cart;
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex, "Orders - Finalizar Sync",
						"Ocorreu um erro durante o processamento do pedido", true);
				throw MotivaiException.WrapIfNotValidationException(
					"ORDER_PROCESSING", "Ocorreu um erro durante o processamento do pedido.", ex
				);
			}
		}

		public async Task<MasterOrder> ValidateAndPrepareOrder(Cart cart)
		{
			if (cart == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

			var campaignSettings = await campaignRepository.GetSettings(cart.CampaignId);

			CampaignOrderRestrictedPeriodVerifier.ValidateCampaignPlaceOrderRestriction(cart.CampaignId, cart.UserId, campaignSettings);

			await ValidateOrder(campaignSettings, cart);
			try
			{
				// Verifica se o participante tem saldo suficiente se não permite resgate sem saldo
				if (!campaignSettings.Parametrizations.AllowRedeemWithoutBalance && !campaignSettings.Parametrizations.EnablePaymentMethodsSelection)
				{
					var balance = await participantRepository.GetAvailableBalance(cart.CampaignId, cart.UserId);
					if (balance < cart.GetTotalPoints())
						throw MotivaiException.ofValidation("Saldo disponível é insuficiente para o fechamento do pedido.");
				}

				// Corrige o endereço de entrega
				if (cart.ShippingAddress != null)
					await CorrectShippingAddress(cart);

				// Carregar dados da campanha e dos parceiros e seta os fatores e as taxas da campanha
				var campaignFees = await campaignRepository.GetFees(cart.CampaignId);
				cart.SetCampaignFees(campaignFees);
				cart.ClientId = campaignSettings.ClientId;

				// Carrega o tipo de processo dos parceiros
				if (cart.Type.HasMarketplace())
				{
					await cart.ChildrenCarts.ForEachAsync(async cp =>
					{
						if (cp.ItemGrouperId == Guid.Empty) return;
						// Carrega o tipo de processo do parceiro
						cp.ProcessType = await _companyRepository.GetProcessTypeByPartner(cp.ItemGrouperId);
					});
				}

				// Chamar API de Checkout
				MasterOrder masterOrder = MasterOrder.From(cart);
				masterOrder.SetCampaignSnapshotSettingsFrom(campaignSettings.Parametrizations);
				return masterOrder;
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex, "Orders - Finalizar",
						"Ocorreu um erro durante o processamento do pedido", true);
				throw MotivaiException.WrapIfNotValidationException(
					"ORDER_PROCESSING", "Ocorreu um erro durante o processamento do pedido.", ex
				);
			}
		}

		private async Task ValidateOrder(CampaignSettingsModel campaignSettings, Cart cart)
		{
			cart.ValidateForOrder();

			ParticipantInfo participantInfo;
			try
			{
				participantInfo = await participantRepository.GetParticipantInfo(cart.UserId, cart.CampaignId);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Orders - Finalizar", "Erro ao carregar os dados do participante.");
				throw MotivaiException.WrapIfNotValidationException(
					"ORDER_PARTICIPANT_VALIDATION", "Não foi possível carregar os dados do participante, por favor, tente novamente.", ex
				);
			}

			if (participantInfo == null)
			{
				// No B2B ignora inexistencia do participante na campanha para poder criar
				if (cart.Type.IsB2B())
				{
					return;
				}
				throw MotivaiException.ofValidation("USER_NOT_FOUND", "Participante não encontrado pelo registro no pedido.");
			}
			cart.ParticipantId = participantInfo.ParticipantId;

			if (participantInfo.Type == PersonType.Juridica)
			{
				// Verifica o preenchimento da inscricao estadual
				if (campaignSettings.Parametrizations.OriginTaxIdForInvoicing == OriginTaxIdForInvoicing.AccountDocument) {
					// Carregar informações do participantes para validar AccountRepresentative
					if (campaignSettings.Parametrizations.EnableAccountRepresentative)
					{
						if (participantInfo.AccountRepresentative == null || string.IsNullOrEmpty(participantInfo.AccountRepresentative.Document))
						{
							throw MotivaiException.ofValidation("USER_ACCOUNT_REPRENSENTATIVE_REQUIRED",
									"Para prosseguir, preencha o CPF para pedidos no seu cadastro em Minha Conta.");
						}
						participantInfo.AccountRepresentative.Validate();
						cart.AccountRepresentative = participantInfo.AccountRepresentative;
					}
					else if (!participantInfo.StateInscriptionExempt)
					{
						if (string.IsNullOrEmpty(participantInfo.StateInscription)) {
							throw MotivaiException.ofValidation("USER_ACCOUNT_STATE_INSCRIPTION_REQUIRED",
									"Inscrição estadual é obrigatória, por favor, complete seu cadastro.");
						}
						if (cart.ShippingAddress != null && participantInfo.StateInscriptionUf != cart.ShippingAddress.Uf) {
							throw MotivaiException.ofValidation("USER_ACCOUNT_STATE_INSCRIPTION_UF_DIFFERENT_SHIPPING_ADDRESS_UF",
									"Estado de entrega é diferente do Estado da Instrição Estadual cadastrada. Por favor, selecione um endereço de entrega no mesmo estado da Inscrição Estadual.");
						}
					}
				}
			}
		}

		private async Task SendOrderEmail(Guid orderId)
		{
			try
			{
				await _emailRepository.SendOrderEmail(orderId);
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex, $"Erro ao enviar notificação de pedido recebido: {orderId}");
			}
		}

		private async Task SendOrderServiceEmail(Guid orderId)
		{
			try
			{
				await this._emailRepository.SendOrderServiceEmail(orderId);
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex, $"Erro ao enviar notificação de pedido recebido: {orderId}");
			}
		}

		/// <summary>
		/// Corrige os dados de endereço de entrega.
		/// </summary>
		protected async Task CorrectShippingAddress(Cart cart)
		{
			var address = cart.ShippingAddress;
			try
			{
				var correiosAddress = await _correiosRepository.QueryCep(address.Cep);
				if (correiosAddress == null)
					return;

				if (correiosAddress.Street != address.Street || correiosAddress.City != address.City)
				{
					LoggerFactory.GetLogger().Info("Order - Correção de Endereço - Endereço de entrega no CEP {0} corrigido de '{1}' para '{2}'.",
						address.Cep, address.Street, correiosAddress.Street);
				}

				if (!address.FilledManually)
				{
					if (!string.IsNullOrEmpty(correiosAddress.Street) && address.Street != correiosAddress.Street)
					{
						cart.AddNotification($"Logradouro de entrega do CEP {address.Cep} está diferente do Correios. Informado: '{address.Street}', Correios: '{correiosAddress.Street}'");
						address.Street = correiosAddress.Street;
					}
					if (!string.IsNullOrEmpty(correiosAddress.Neighborhood) && address.Neighborhood != correiosAddress.Neighborhood)
					{
						cart.AddNotification($"Bairro de entrega do CEP {address.Cep} está diferente do Correios. Informado: '{address.Neighborhood}', Correios: '{correiosAddress.Neighborhood}'");
						address.Neighborhood = correiosAddress.Neighborhood;
					}
				}
				if (!string.IsNullOrEmpty(correiosAddress.City) && address.City != correiosAddress.City)
				{
					cart.AddNotification($"Cidade de entrega do CEP {address.Cep} está diferente do Correios. Informado: '{address.City}', Correios: '{correiosAddress.City}'");
					address.City = correiosAddress.City;
				}

				if (!string.IsNullOrEmpty(correiosAddress.State))
				{
					address.State = correiosAddress.State;
					address.Uf = correiosAddress.Uf;
				}
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "Order - Correção Endereço", "Erro ao verificar endereço de entrega.", true);
			}
		}

		#endregion

		public async Task<Cart> GetOrderById(Guid orderId, Guid campaignId, Guid participantId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			/*if (participantId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");*/
			if (orderId == Guid.Empty)
				throw MotivaiException.ofValidation("Pedido inválido.");

			var order = await _orderRepository.GetOrderById(orderId);
			if (order == null)
				throw MotivaiException.ofValidation("Pedido não encontrado.");
			if (order.CampaignId != campaignId)
				throw MotivaiException.ofValidation("Pedido não pertence à campanha.");
			if (participantId != Guid.Empty && order.UserParticipantId != participantId)
				throw MotivaiException.ofValidation("Pedido não pertence ao participante.");

			try
			{
				// Mapear pro cart
				var orderCart = new Cart()
				{
					CampaignId = campaignId,
					UserId = order.UserId,
					ParticipantId = order.UserParticipantId,
					OrderId = orderId,
					InternalOrderNumber = order.InternalOrderNumber,
					CreationDate = order.CreationDate,
					TimezoneOffset = order.TimezoneOffset,
					Timezone = order.Timezone,
					ShippingAddress = order.ShippingAddress,
					Discount = order.Discount,
					DiscountCoupon = order.DiscountCoupon,
					OccurredError = order.OccurredError,
					ErrorMessage = OrderStatusHelper.GetErrorMessageFor(order.OccurredError, order.ExternalStatus),
					PaymentMethods = order.GetPaymentMethods(),
					ChildrenCarts = order.GetChildrenCarts()
				};
				var partnersSkus = orderCart.ChildrenCarts
					// .SelectMany(c => c.Products.Select(p => PartnerSku.OfPartnerAndSkuCode(c.ItemGrouperId, p.SkuCode)))
					.SelectMany(c => c.Products)
					.Where(p => p != null)
					.Select(p => PartnerSku.OfPartnerAndSkuCode(p.GetItemGrouperId(), p.SkuCode))
					.ToList();
				// Carrega todos os itens do pedido mesmo se estiverem desativados
				var products = await _productRepository.GetProductsByPartnersAndSkuCodes(campaignId, order.UserId, order.UserParticipantId, partnersSkus, null, true);

				if (products != null)
				{
					orderCart.ChildrenCarts.ForEach(pCart =>
					{
						pCart.Products.ForEach(item =>
						{
							var prod = products.FirstOrDefault(p => p.Id == item.ElasticId);
							if (prod == null)
							{
								if (string.IsNullOrEmpty(item.ProductName))
								{
									item.ProductName = "Produto não encontrado";
								}
								return;
							}
							item.ProductName = prod.Name;
							item.DynamicPriceDescription = prod.DynamicPriceDescription;
							item.ProductModelLabel = prod.ProductModelLabel;
							item.ProductColorLabel = prod.ProductColorLabel;
							item.ProductSizeLabel = prod.ProductSizeLabel;
							item.ProductVoltageLabel = prod.ProductVoltageLabel;
							item.ProductInformationTabTitle = prod.ProductInformationTabTitle;
							item.ProductTechnicalSpecificationsTabTitle = prod.ProductTechnicalSpecificationsTabTitle;
							item.Image = prod.ImageUrl;
							item.PartnerName = prod.PartnerName;
							item.SkuModel = prod.SkuModel;
							item.SkuSize = prod.SkuSize;
							item.SkuColor = prod.SkuColor;
							item.SkuVoltage = prod.SkuVoltage;
						});
					});
				}
				return orderCart;
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex);
				throw MotivaiException.ofValidation("Não foi possível carregar os dados do pedido.");
			}
		}

		public async Task<MasterOrderResumed> FetchOrderResumed(Guid orderId)
		{
			if (orderId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("Pedido inválido.");
			}

			var orderResumed = await this._orderRepository.FetchOrderResumed(orderId);
			if (orderResumed == null)
				return null;

			orderResumed.StatusDescription = OrderStatusHelper.FromExternalStatusToCatalogStatus(orderResumed.Status);

			return orderResumed;
		}

		public async Task<List<dynamic>> ConsultLinkVouchers(Guid campaignId, Guid orderId, Guid itemGrouperId)
		{
			if (campaignId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("Campanha inválida.");
			}

			if (orderId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("Pedido inválido.");
			}

			if (itemGrouperId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("Pedido parceiro inválido.");
			}

			return await this._orderRepository.ConsultLinkVouchers(orderId, itemGrouperId);
		}


		#region Aprovação e Precificação de Pedido

		public async Task<bool?> PriceFactoryItems(FactoryOrderPrice order)
		{
			if (order == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para continuar.");
			order.Validate();
			return await _orderRepository.PriceFactoryItems(order);
		}

		public async Task<dynamic> ApproveOrder(Guid campaignId, Guid orderId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (orderId == Guid.Empty)
				throw MotivaiException.ofValidation("Pedido inválido.");

			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			if (!campaignSettings.Type.IsB2B())
			{
				throw MotivaiException.ofValidation("Campanha não permite aprovação de pedido.");
			}
			return await _orderRepository.ApproveOrder(orderId);
		}

		public async Task<dynamic> RefuseOrder(Guid campaignId, Guid orderId)
		{
			if (campaignId == Guid.Empty)
				throw MotivaiException.ofValidation("Campanha inválida.");
			if (orderId == Guid.Empty)
				throw MotivaiException.ofValidation("Pedido inválido.");

			var campaignSettings = await campaignRepository.GetSettings(campaignId);
			if (!campaignSettings.Type.IsB2B())
			{
				throw MotivaiException.ofValidation("Campanha não permite reprovação de pedido.");
			}
			return await _orderRepository.RefuseOrder(orderId);
		}

		#endregion
	}
}
