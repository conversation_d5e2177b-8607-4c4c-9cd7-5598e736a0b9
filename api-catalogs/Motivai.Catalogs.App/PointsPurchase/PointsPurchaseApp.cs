﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Microsoft.Extensions.Options;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Configs.PaymentGateways;
using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.IApp.PointsPurchase;
using Motivai.Catalogs.Domain.IRepository.Notifications;
using Motivai.Catalogs.Domain.IRepository.PaymentGateway;
using Motivai.Catalogs.Domain.IRepository.Transactions;
using Motivai.Catalogs.Domain.Models.PaymentGateway;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Enums.Payments;
using Motivai.SharedKernel.Domain.Model.Structures;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.PointsPurchase
{
    public class PointsPurchaseApp : IPointsPurchaseApp
    {
        public const string PAYMENT_TRANSACTION_DESCRIPTION = "Pontos";
        public const string DEBT_TRANSACTION_DESCRIPTION = "Compra de Pontos";

        private readonly PaymentGateways _gatewayTokens;
        private readonly ICampaignRepository _campaignRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly IPaymentGatewayRepository _paymentGatewayRepository;
        private readonly ITransactionRepository _transactionRepository;
        private readonly IUserParticipantRepository _participantRepository;
        private readonly INotificationRepository notificationRepository;

        public PointsPurchaseApp(IOptions<PaymentGateways> getewayTokens, ICampaignRepository campaignRepository,
            IPaymentGatewayRepository paymentGatewayRepository, ITransactionRepository transactionRepository,
            ICompanyRepository companyRepository, IUserParticipantRepository participantRepository,
            INotificationRepository notificationRepository)
        {
            _gatewayTokens = getewayTokens?.Value;
            _campaignRepository = campaignRepository;
            _companyRepository = companyRepository;
            _paymentGatewayRepository = paymentGatewayRepository;
            _transactionRepository = transactionRepository;
            _participantRepository = participantRepository;
            this.notificationRepository = notificationRepository;
        }

        private GatewayApiTokens ResolveApiTokensFrom(GatewayCompany company)
        {
            switch (company)
            {
                case GatewayCompany.PAGARME:
                    return _gatewayTokens.Pagarme;
                default:
                    return _gatewayTokens.Pagarme;
            }
        }

        ///<summary>
        /// Carrega as configurações do gateway quando a campanha permite comprar pontos.
        /// Se os tokens não tiverem sido preenchidos, serão utilizados os configurados no cliente.
        /// Se o fator da campanha não tiver sido preenchido, será utilizado a da campanha.
        ///</summary>
        private async Task<PaymentGatewaySettings> GetConfiguredPaymentGatewaySettings(Guid campaignId)
        {
            var campaignSettings = await _campaignRepository.GetSettings(campaignId);
            if (!campaignSettings.Parametrizations.AllowBuyPoints)
            {
                throw MotivaiException.ofValidation("A compra de pontos está desabilitada nesta campanha.");
            }
            if (_gatewayTokens == null)
                throw MotivaiException.of(ErrorType.Configuration, "Tokens do gateway não estão configurados.");

            var paymentGateway = await _campaignRepository.GetActivePaymentGateway(campaignId);
            if (paymentGateway == null)
                throw MotivaiException.ofValidation("Nenhum gateway de pagamento está ativo.");
            // Verifica se o fator de pontos é customizado ou se usará o mesmo da campanha
            if (!paymentGateway.ChangeConversionFactor || !paymentGateway.ConversionFactor.HasValue)
            {
                var campaignFees = await _campaignRepository.GetFees(campaignId);
                paymentGateway.ConversionFactor = campaignFees.PointsConversionFactor;
            }

            // Assume true pois apenas quando configurado para não usar que será alterado
            var useDefaultTokens = true;
            // Verificar se no Cliente permite alterar os tokens por campanha
            if (campaignSettings.HasClientId())
            {
                var clientSettings = await _companyRepository.GetClientConfiguration(campaignSettings.ClientId);
                useDefaultTokens = !clientSettings.CanChangeKeys;
            }
            // Se não utiliza tokens da campanha então utiliza o da Motivai
            if (useDefaultTokens)
            {
                var gateway = ResolveApiTokensFrom(paymentGateway.GatewayCompany);
                paymentGateway.PublicKey = gateway.PublicToken;
                paymentGateway.PrivateKey = gateway.PrivateToken;
                paymentGateway.MerchantKey = gateway.MerchantKey;
            }
            return paymentGateway;
        }

        private static decimal CalculateMinimumCurrencyAmount(decimal pointsNeeded, PaymentGatewaySettings paymentGateway)
        {
            return AmountConversor
                .WithPointsConversionFactor(paymentGateway.ConversionFactor.Value)
                .RevertPointsFactorApplied(paymentGateway.CalculateMinimumPointsToBuy(pointsNeeded));
        }

        public async Task<decimal> CalculatePurchaseCostFor(Guid campaignId, decimal pointsNeeded)
        {
            if (campaignId == null)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (pointsNeeded <= 0)
                throw MotivaiException.ofValidation("Valor de pontos inválido.");
            if (!await _campaignRepository.IsActive(campaignId))
                throw MotivaiException.ofValidation("Campanha não está ativa.");

            var paymentGateway = await GetConfiguredPaymentGatewaySettings(campaignId);
            decimal currencyValueNeeded = CalculateMinimumCurrencyAmount(pointsNeeded, paymentGateway);

            return paymentGateway.CalculateTotalPriceToBuy(currencyValueNeeded);
        }

        public async Task<List<Entry<decimal, decimal>>> GetPointsOptionsToBuy(Guid campaignId)
        {
            if (campaignId == null)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (!await _campaignRepository.IsActive(campaignId))
                throw MotivaiException.ofValidation("Campanha não está ativa.");

            var paymentGateway = await GetConfiguredPaymentGatewaySettings(campaignId);

            // Se não tem lote então permite digitar o valor desejado
            if (!paymentGateway.HasPointsBatch())
            {
                return Enumerable.Empty<Entry<decimal, decimal>>().ToList();
            }
            var pointsInterval = paymentGateway.Batch.Value;
            var conversor = AmountConversor.WithPointsConversionFactor(paymentGateway.ConversionFactor.Value);
            var pointsOptions = new List<Entry<decimal, decimal>>();
            // Calcula as faixas de preço
            for (var i = 0; i < 10; i++)
            {
                var pointsAmount = pointsInterval * (i + 1);
                var priceToBuy = paymentGateway.CalculateTotalPriceToBuy(conversor.RevertPointsFactorApplied(pointsAmount));
                pointsOptions.Add(new Entry<decimal, decimal>()
                {
                    Key = pointsAmount,
                    Value = priceToBuy
                });
            }
            return pointsOptions;
        }

        public async Task<PaymentOptions> GetPaymentOptionsForPoints(Guid campaignId, decimal pointsNeeded)
        {
            if (campaignId == null)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (pointsNeeded <= 0)
                throw MotivaiException.ofValidation("Valor de pontos inválido.");
            if (!await _campaignRepository.IsActive(campaignId))
                throw MotivaiException.ofValidation("Campanha não está ativa.");

            var paymentGateway = await GetConfiguredPaymentGatewaySettings(campaignId);

            var paymentOptions = paymentGateway.CreatePaymentOptions(CalculateMinimumCurrencyAmount(pointsNeeded, paymentGateway));
            if (paymentOptions != null)
            {
                paymentOptions.PointsToBuy = paymentGateway.CalculateMinimumPointsToBuy(pointsNeeded);
            }
            return paymentOptions;
        }

        public async Task<bool> IssuePointsPurchasePayment(Guid campaignId, PointsPurchaseOrder purchaseOrder)
        {
            if (campaignId == null)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (purchaseOrder == null)
                throw MotivaiException.ofValidation("Dados da compra inválidos.");
            if (!await _campaignRepository.IsActive(campaignId))
                throw MotivaiException.ofValidation("Campanha não está ativa.");

            purchaseOrder.Validate();
            purchaseOrder.CampaignId = campaignId;

            if (!await _participantRepository.IsActiveUserInCampaign(purchaseOrder.UserId, campaignId))
                throw MotivaiException.ofValidation("Usuário não participa na campanha.");

            LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Iniciando compra de pontos - Pontos {} - Total {}",
                    campaignId, purchaseOrder.UserId, purchaseOrder.PointsToBuy, purchaseOrder.Installment.Total);

            var paymentGateway = await GetConfiguredPaymentGatewaySettings(campaignId);
            purchaseOrder.GatewayCompany = paymentGateway.GatewayCompany;

            ValidateSelectedInstallment(purchaseOrder, paymentGateway);

            // Efetua a cobrança no cartão
            await ProcessPayment(paymentGateway, purchaseOrder);

            // Cria a mecânica da compra
            await CreateCreditTransaction(campaignId, purchaseOrder);

            await SendPurchaseNotification(purchaseOrder);

            return true;
        }

        private static void ValidateSelectedInstallment(PointsPurchaseOrder purchaseOrder, PaymentGatewaySettings paymentGateway)
        {
            decimal pointsToBuy = paymentGateway.CalculateMinimumPointsToBuy(purchaseOrder.PointsToBuy);
            var conversor = AmountConversor.WithPointsConversionFactor(paymentGateway.ConversionFactor.Value);
            var currencyValueNeeded = conversor.RevertPointsFactorApplied(pointsToBuy);

            // calcula a parcela para verificar se não houve mudança
            var selectedInstall = paymentGateway.CalculateSpecificInstallment(currencyValueNeeded, purchaseOrder.Installment.Count);
            selectedInstall.Validate(purchaseOrder.Installment);
        }

        private async Task ProcessPayment(PaymentGatewaySettings paymentGateway, PointsPurchaseOrder purchaseOrder)
        {
            LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Iniciando cobrança no gateway",
                    purchaseOrder.CampaignId, purchaseOrder.UserId);
            // Obtem os detalhes das taxas utilizadas na totalização para o split e para salvar na compra
            var detailedInstallment = paymentGateway.GetDetailsAboutInstall(purchaseOrder.PointsToBuy, purchaseOrder.PointsConversionFactor,
                purchaseOrder.CurrencyValue, purchaseOrder.Installment);

            // TODO: incluir parâmetro no cliente para definir se permite split
            var storeApplitedAmount = SplitStoresAmount(purchaseOrder, paymentGateway, detailedInstallment);

            try
            {
                var operationResult = await _paymentGatewayRepository.ChargePaymentOrder(paymentGateway, purchaseOrder,
                        PAYMENT_TRANSACTION_DESCRIPTION, storeApplitedAmount, detailedInstallment);
                LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Resultado do pagamento - ID {} - Status {} - Erro: {}",
                        purchaseOrder.CampaignId, purchaseOrder.UserId, operationResult.PaymentId, operationResult.Status, operationResult.ErrorMessage);

                if (operationResult == null)
                {
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível completar a compra de pontos, por favor, tente novamente.");
                }
                if (!operationResult.IsSuccessful())
                {
                    throw MotivaiException.ofValidation(operationResult.ErrorMessage);
                }
                // Salva o ID da cobrança
                purchaseOrder.PaymentId = operationResult.PaymentId;
            }
            catch (Exception ex)
            {
                LoggerFactory.GetLogger().Error("Cmp {} - Usr {} - Erro ao processar pagamento da compra de pontos: {}",
                        purchaseOrder.CampaignId, purchaseOrder.UserId, ex.Message);
                await ExceptionLoggerMiddleware.HandleException(
                    ex,
                    "Points Purchase - Gateway",
                    "Ocorreu durante o processamento do pagamento da compra de pontos.",
                    true
                );
                throw ex;
            }
        }

        private async Task CreateCreditTransaction(Guid campaignId, PointsPurchaseOrder purchaseOrder)
        {
            try
            {
                LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Iniciando crédito na transação com origem {}",
                        purchaseOrder.CampaignId, purchaseOrder.UserId, purchaseOrder.PaymentId);
                var transactionId = await _transactionRepository.CreateCreditTransaction(campaignId,
                        purchaseOrder.UserId, DEBT_TRANSACTION_DESCRIPTION, DEBT_TRANSACTION_DESCRIPTION, purchaseOrder);
                LoggerFactory.GetLogger().Info("Cmp {} - Usr {} - Transação resultando do crédito {}",
                        purchaseOrder.CampaignId, purchaseOrder.UserId, transactionId);
                if (transactionId == Guid.Empty)
                    throw MotivaiException.ofValidation("Erro ao creditar os pontos comprados.");
            }
            catch (Exception ex)
            {
                LoggerFactory.GetLogger().Error("Cmp {} - Usr {} - Erro ao creditar os pontos comprados: {}",
                        purchaseOrder.CampaignId, purchaseOrder.UserId, ex.Message);
                await ExceptionLoggerMiddleware.HandleException(
                    ex,
                    "Points Purchase - Crédito",
                    "Erro ao creditar os pontos comprados",
                    true
                );
                throw ex;
            }
        }

        private async Task SendPurchaseNotification(PointsPurchaseOrder purchaseOrder)
        {
            try
            {
                var emailSent = await notificationRepository.SendPurchaseNotification(purchaseOrder);
                if (!emailSent)
                    throw MotivaiException.ofValidation("Notificação de pedido de compra de pontos não foi enviada.");
            }
            catch (Exception ex)
            {
                await ExceptionLoggerMiddleware.HandleException(
                    ex,
                    "Points Purchase - Notificação",
                    "Erro durante o envio da notificação de pedido de compra de pontos."
                );
            }
        }

        private StoreAmount SplitStoresAmount(PointsPurchaseOrder purchaseOrder, PaymentGatewaySettings paymentGateway, DetailedPaymentInstallment detailedInstallment)
        {
            var amount = StoreAmount.OfTotal(purchaseOrder.Installment.Total);
            var gateway = ResolveApiTokensFrom(paymentGateway.GatewayCompany);

            // Calcula o split calculando o valor líq da taxa adicional sobre o total transacionado
            decimal additionalFeePercentageOverTotal = Math.Ceiling(detailedInstallment.AdditionalAmount / detailedInstallment.TotalAmount * 100);

            if (additionalFeePercentageOverTotal > 0)
            {
                amount.Splits.Add(new AmountSplit
                {
                    MerchantKey = gateway.MerchantKey,
                    Percentage = (int)additionalFeePercentageOverTotal,
                    PayFee = false
                });
            }
            amount.Splits.Add(new AmountSplit
            {
                MerchantKey = paymentGateway.MerchantKey,
                Percentage = 100 - (int)additionalFeePercentageOverTotal,
                PayFee = true
            });

            return amount;
        }
    }
}