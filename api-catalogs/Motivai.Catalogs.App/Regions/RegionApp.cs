using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.Regions;
using Motivai.Catalogs.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.Regions {
    public class RegionApp : IRegionApp {
        private readonly ICampaignRepository _campaignRepository;
        private readonly IUserParticipantRepository _participantRepository;
        private readonly IRegionsRepository _regionsRepository;
        private readonly IFactoriesRepository _factoriesRepository;

        public RegionApp(ICampaignRepository campaignRepository, IUserParticipantRepository participantRepository, IRegionsRepository regionsRepository,
                IFactoriesRepository factoriesRepository) {
            this._campaignRepository = campaignRepository;
            this._participantRepository = participantRepository;
            this._regionsRepository = regionsRepository;
            this._factoriesRepository = factoriesRepository;
        }

        private async Task<RegionFilter> GetRegionsByAddress(Guid campaignId, Address address) {
            var regionsIds = await _regionsRepository.FindRegions(address.Cep, address.Neighborhood, address.City, address.State);
            if (regionsIds == null || regionsIds.Count == 0) return null;
            var campaignSettings = await _campaignRepository.GetSettings(campaignId);

            // B2B utiliza fábricas no filtro
            List<Guid> factoriesIds = null;
            if (campaignSettings.Type.IsB2B()) {
                factoriesIds = await _factoriesRepository.FindFactoriesIdsForRegions(regionsIds);
            }
            return new RegionFilter() {
                RegionsIds = regionsIds,
                FactoriesIds = factoriesIds
            };
        }

        public async Task<RegionFilter> GetMatchedRegionsForUser(Guid userId, Guid campaignId) {
            if (userId == Guid.Empty)
                return null;

            // ! TODO: Verificar o tipo da campanha para poder carregar o endereço do lojista ou participante (após alteração do Users para ter volante)
            try {
                var address = await _participantRepository.GetMainAddress(userId, campaignId);
                if (address == null)
                    return null;
                return await GetRegionsByAddress(campaignId, address);
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catalogs - Regions", "Erro durante o matching de região.", true);
                return null;
            }
        }
    }
}