﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.ContactUs;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Contacts;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.App.ContactUs
{
    public class CampaignContactApp : ICampaignContactApp
    {
        private readonly ICampaignRepository campaignRepository;

        public CampaignContactApp(ICampaignRepository campaignRepository)
        {
            this.campaignRepository = campaignRepository;
        }

        public async Task<List<dynamic>> GetContactUsSubjects(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            var subjects = await campaignRepository.GetContactSubjects(campaignId);
            if (subjects == null)
            {
                subjects = new List<CampaignContactUsSubject>();
            }
            subjects.Add(CampaignContactUsSubject.CreateDefaultLgpdSubject());
            return subjects.Select(subject => (dynamic)new
            {
                subject.Id,
                subject.Subject
            }).ToList();
        }

        public Task<bool> SendContactUsFormMessage(Guid campaignId, ContactUsModel contactModel)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (contactModel == null)
                throw MotivaiException.ofValidation("Deve ser informado os dados para ser enviado.");
            contactModel.Validate();
            contactModel.CampaignId = campaignId;

            return campaignRepository.SendContactUsFormMessage(campaignId, contactModel);
        }
    }
}