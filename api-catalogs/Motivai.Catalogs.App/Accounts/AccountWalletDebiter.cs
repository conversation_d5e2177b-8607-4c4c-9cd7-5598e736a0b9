using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.Accounts;
using Motivai.Catalogs.Domain.IRepository.Transactions;
using Motivai.Catalogs.Domain.Models.Transactions;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.App.Accounts
{
	public class AccountWalletDebiter : IAccountWalletDebiter
	{
		private readonly ITransactionRepository transactionRepository;
		private readonly IUserParticipantRepository userParticipantRepository;

		public AccountWalletDebiter(ITransactionRepository transactionRepository,
			IUserParticipantRepository userParticipantRepository)
		{
			this.transactionRepository = transactionRepository;
			this.userParticipantRepository = userParticipantRepository;
		}

		public async Task<decimal> GetBalance(Guid campaignId, Guid userId)
		{
			try {
				return await transactionRepository.GetBalance(campaignId, userId);
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "Account Wallet - Balance", "Erro ao carregar os saldo do participante.");
				var message = (ex is MotivaiException)
					? ex.Message
					: "Ocorreu um erro ao carregar o saldo, por favor, tente novamente.";
				throw MotivaiException.of("USER_BALANCE_ERROR", message, ex);
			}
		}

		public async Task<Guid> Debit(Guid campaignId, Guid userId, Guid? participantId,
				TransactionOrigin origin, Guid originId, string description, Amount amount)
		{
			if (!participantId.HasValue || participantId.Value == Guid.Empty)
			{
				participantId = await GetParticipantId(userId, campaignId);
			}

			var debitTransactionId = await SendDebit(campaignId, userId, participantId ?? Guid.Empty,
				origin, originId, description, amount);

			if (debitTransactionId == Guid.Empty) {
				throw MotivaiException.ofValidation("USER_BALANCE_DEBIT_ERROR",
					"Não foi possível efetuar o débito do saldo, por favor, tente novamente.");
			}
			return debitTransactionId;
		}

		public async Task<TransactionRefundReceipt> RefundDebit(Guid campaignId, Guid userId, Guid debitTransactionId,
				TransactionOrigin origin, Guid originId, string description)
		{
			TransactionRefundReceipt refundReceipt;
			try
			{
				refundReceipt = await transactionRepository.RefundTransaction(campaignId, userId, debitTransactionId,
					origin, originId, description);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "AccountWallet - Estorno",
					$"Erro ao efetuar estorno da transação {debitTransactionId}", true);
				throw MotivaiException.of("USER_BALANCE_REFUND_ERROR",
					"Não foi possível efetuar o estorno do saldo, por favor, tente novamente.", ex);
			}

			if (refundReceipt == null || !refundReceipt.HasAnyTransaction()) {
				throw MotivaiException.ofValidation("USER_BALANCE_REFUND_ERROR",
					"Não foi possível efetuar o estorno do saldo, por favor, tente novamente.");
			}
			return refundReceipt;
		}

		private async Task<Guid> SendDebit(Guid campaignId, Guid userId, Guid participantId,
			TransactionOrigin origin, Guid originId, string description, Amount amount)
		{
			try
			{
				return await transactionRepository.CreateDebitTransaction(campaignId, userId, participantId,
					origin, originId, description, amount);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "AccountWallet - Débito", "Erro ao efetuar débito do saldo.", true);
				if (ex is MotivaiException gpex && gpex.ErrorType == ErrorType.Validation) {
					if (string.IsNullOrEmpty(gpex.ErrorCode)) {
						gpex.ErrorCode = "USER_BALANCE_DEBIT_ERROR";
					}
					throw ex;
				}
				throw MotivaiException.of("USER_BALANCE_DEBIT_ERROR",
					"Não foi possível efetuar o débito do saldo, por favor, tente novamente.", ex);
			}
		}

		private async Task<Guid> GetParticipantId(Guid userId, Guid campaignId)
		{
			try
			{
				return await userParticipantRepository.GetParticipantIdByUserAndCampaign(userId, campaignId);
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "AccountWallet - User Info", "Erro ao carregar os dados do participante");
				throw MotivaiException.of("USER_INFO_ERROR",
					"Não foi possível carregar os dados do participante, por favor, tente novamente.", ex);
			}
		}
	}
}