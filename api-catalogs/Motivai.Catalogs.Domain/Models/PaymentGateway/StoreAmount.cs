using System.Collections.Generic;

using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Domain.Models.PaymentGateway
{
    ///<summary>
    /// Classe que representa um item do split de pagamento.
    ///</summary>
    public class StoreAmount
    {
        public decimal Total { get; set; }
        public List<AmountSplit> Splits { get; set; }

        public static StoreAmount OfTotal(decimal total)
        {
            return new StoreAmount()
            {
                Total = AmountValueHelper.RoundAmountValue(total),
                Splits = new List<AmountSplit>()
            };
        }
    }

    public class AmountSplit
    {
        public string MerchantKey { get; set; }
        public int Percentage { get; set; }
        public bool PayFee { get; set; }
    }
}