using System;

namespace Motivai.Catalogs.Domain.Models.PaymentGateway
{
    public class GatewayOperationResult
    {
        public Guid PaymentId { get; set; }
        public GatewayChargeResultStatus Status { get; set; }
        public string TransactionId { get; set; }
        public string ErrorMessage { get; set; }

        public bool IsSuccessful()
        {
            return Status == GatewayChargeResultStatus.PAID;
        }
    }

    public enum GatewayChargeResultStatus
    {
        PAID,
        FAILED
    }
}