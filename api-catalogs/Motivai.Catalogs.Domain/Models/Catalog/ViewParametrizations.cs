using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Motivai.Catalogs.Domain.Models.Catalog
{
    public class ViewParametrizations
    {
        public bool VisibleOnCatalog { get; set; }
        public bool VisibleOnSite { get; set; }
        public bool VisibleOnAppCashback { get; set; }
        public List<Guid> Department { get; set; }
        public List<String> SitePages { get; set; }
        public ImageParametrizations ImageParametrizations { get; set; }

    }
}