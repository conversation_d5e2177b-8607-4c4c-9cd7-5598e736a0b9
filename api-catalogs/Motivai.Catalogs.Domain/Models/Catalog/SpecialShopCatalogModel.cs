using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Enums.Campaigns;

namespace Motivai.Catalogs.Domain.Models.Catalog
{
	public class SpecialShopCatalogModel
    {
        public Guid Id { get; set; }
        public CampaignParametrizationOrigin Origin { get; set; }
        public string Name { get; set; }
        public bool Active { get; set; }
        public DateTime UpdateDate { get; set; }
        public SpecialShopParametrizations Parametrizations { get; set; }
        public SpecialShopViewParametrizations ViewParametrizations { get; set; }
        public List<PartnerProductSku> PartnerProductSku { get; set; }
    }
}