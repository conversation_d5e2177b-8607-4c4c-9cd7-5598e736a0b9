using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Campaign;

namespace Motivai.Catalogs.Domain.Models.Catalog
{
    public class MediaBoxCatalogModel
    {
        public String Id { get; set; }
        public String Name { get; set; }
        public Boolean Active { get; set; }
        public DateTime UpdateDate { get; set; }
        public Parametrizations Parametrizations { get; set; }
        public ViewParametrizations ViewParametrizations { get; set; }
        public PartnerProductSku PartnerProductSku { get; set; }
    }
}