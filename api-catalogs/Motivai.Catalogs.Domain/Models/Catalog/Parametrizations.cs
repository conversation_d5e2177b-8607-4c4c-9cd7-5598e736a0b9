using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Motivai.Catalogs.Domain.Models.Catalog
{
    public class Parametrizations
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool OpenInNewTab { get; set; }
        public int Position { get; set; }
        public string Link { get; set; }
        public ContentType ContentType { get; set; }
        public bool EnableSegmentations { get; set; }
    }
}