using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Motivai.Catalogs.Domain.Models.Catalog
{
    public class SpecialShopParametrizations
    {
        public PresentationType PresentationType { get; set; }
        public bool ShowAtCatalogMenu { get; set; }
        public bool ShowAsFeatured { get; set; }
        public int GridItemPosition { get; set; }
        public int GridItemSize { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }
}