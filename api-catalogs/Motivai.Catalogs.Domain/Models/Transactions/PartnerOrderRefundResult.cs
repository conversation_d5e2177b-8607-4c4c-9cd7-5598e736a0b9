using System;

namespace Motivai.Catalogs.Domain.Models {
    public class PartnerOrderRefundResult {
        public bool Refunded { get; set; }
        public string Ticket { get; set; }
        public string ErrorMessage { get; set; }

        public static PartnerOrderRefundResult OfError(string message) {
            return new PartnerOrderRefundResult() {
                Refunded = false,
                ErrorMessage = message
            };
        }

        public static PartnerOrderRefundResult OfTicket(string ticket) {
            return new PartnerOrderRefundResult() {
                Refunded = true,
                Ticket = ticket
            };
        }
    }
}