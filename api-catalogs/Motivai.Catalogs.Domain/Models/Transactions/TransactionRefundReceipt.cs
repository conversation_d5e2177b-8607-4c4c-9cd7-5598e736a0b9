using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.Catalogs.Domain.Models.Transactions
{
	public class TransactionRefundReceipt
	{
        public Guid TraceId { get; set; }
        public DateTime FinishDate { get; set; }
        public bool ParticipantNotified { get; set; }
        public List<string> TransactionsIds { get; set; }
        public List<TransactionResult> TransactionResults { get; set; }

		public bool HasAnyTransaction()
		{
			return !TransactionsIds.IsNullOrEmpty();
		}
	}
}