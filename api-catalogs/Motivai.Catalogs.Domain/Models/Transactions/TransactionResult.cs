using System;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Models.Transactions
{
    public class TransactionResult
    {
        public string TransactionId { get; set; }
        public Amount TotalAmount { get; set; }

        public TransactionResult(string TransactionId, Amount TotalAmount)
        {
            this.TransactionId = TransactionId;
            this.TotalAmount = TotalAmount;
        }

        public static TransactionResult From(string TransactionId, Amount TotalAmount)
        {
            return new TransactionResult(TransactionId, TotalAmount);
        }
    }
}