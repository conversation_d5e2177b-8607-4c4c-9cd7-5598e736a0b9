using System;

namespace Motivai.Catalogs.Domain.Models.Product
{
    public class ClassificationModel {
        public Guid DepartmentId { get; set; }
        public Guid? CategoryId { get; set; }
        public Guid? SubcategoryId { get; set; }

        ///<summary>
        /// Retorna true se aplica para todo o departamento.
        ///</summary>
        public bool IsDepartment() {
            return !CategoryId.HasValue && !SubcategoryId.HasValue;
        }

        ///<summary>
        /// Retorna true se aplica para todo a categoria.
        ///</summary>
        public bool IsCategory() {
            return CategoryId.HasValue && !SubcategoryId.HasValue;
        }

        ///<summary>
        /// Retorna true se aplica para a subcategoria.
        ///</summary>
        public bool IsSubcategory() {
            return CategoryId.HasValue && SubcategoryId.HasValue;
        }

        public bool CanApply(Guid departmentId, Guid categoryId, Guid? subcategoryId) {
            if (this.IsDepartment())
                return this.DepartmentId == departmentId;
            if (this.IsCategory())
                return this.DepartmentId == departmentId && this.CategoryId == categoryId;
            return this.DepartmentId == departmentId && this.CategoryId == categoryId
                && this.SubcategoryId == subcategoryId;
        }
    }
}