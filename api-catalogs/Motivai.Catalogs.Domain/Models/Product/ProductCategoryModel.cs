using System;

namespace Motivai.Catalogs.Domain.Models.Product
{
    public class ProductCategoryModel
    {
        public Guid Id { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? ParentId { get; set; }
        public string Name { get; set; }
        public short Level { get; set; }
        public bool Active { get; set; }

        public bool IsDepartment() {
            return Level == 1;
        }

        public bool IsCategory() {
            return Level == 2 && DepartmentId.HasValue && DepartmentId.Value != Guid.Empty;
        }

        public bool IsSubcategory() {
            return Level == 3
                && ParentId.HasValue && ParentId.Value != Guid.Empty
                && DepartmentId.HasValue && DepartmentId.Value != Guid.Empty;
        }
    }
}