using System.Collections.Generic;

namespace Motivai.Catalogs.Domain.Models.Product {
    public class SearchModel {
        public int Total { get; set; }

        public List<ProductShowcaseModel> Result { get; set; }

        public List<Item> Partners { get; set; }
        public List<Item> Departments { get; set; }
        public List<Item> Categories { get; set; }
        public List<Item> Subcategories { get; set; }

        public List<Item> Manufacturers { get; set; }
        public List<Item> Colors { get; set; }
        public List<Item> Voltages { get; set; }

        public bool HasResult() {
            return Total > 0 && Result != null;
        }

    }

    public class Item {
        public string Description { get; set; }
        public int Count { get; set; }

        public static Item Of(string description, int count) {
            return new Item() {
                Description = description,
                    Count = count
            };
        }
    }
}