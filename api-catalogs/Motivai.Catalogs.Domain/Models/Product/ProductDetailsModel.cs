using System;
using System.Collections.Generic;
using System.Linq;

using Motivai.Catalogs.Domain.Dtos;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Validators;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

using Newtonsoft.Json;

namespace Motivai.Catalogs.Domain.Models.Product
{
    public class ProductDetailsModel
	{
		public Guid Id { get; set; }
		public string BuId { get; set; }
		public string ElasticsearchId { get; set; }
		public string Name { get; set; }
		public bool Active { get; set; }
		public string Description { get; set; }
		public bool Offline { get; set; }
		public ProcessType ProcessType { get; set; }
		public ProductType ProductType { get; set; }

		public RegisterType RegisterType { get; set; }

		public ProductLayoutType LayoutType { get; set; }

		public Guid PartnerId { get; set; }
		public string Partner { get; set; }
		public string PartnerIdentifierCode { get; set; }
		public PartnerSettingsModel PartnerSettings { get; set; }
		public Guid ManufacturerId { get; set; }
		public string Manufacturer { get; set; }

		public bool HasClassification { get; set; }
		public Guid DepartmentId { get; set; }
		public string Department { get; set; }
		public Guid CategoryId { get; set; }
		public string Category { get; set; }
		public Guid? SubcategoryId { get; set; }
		public string Subcategory { get; set; }

		public string Information { get; set; }
		public List<KeyValue> Characteristics { get; set; }
		public List<KeyValue> TechnicalSpecifications { get; set; }

		public ProductAttributes Attributes { get; set; }

		// Dados do SKU principal ou selecionado
		public Guid SkuId { get; set; }
		public bool OnlyOneSku { get; set; }
		public string SkuCode { get; set; }
		public string SkuIntegrationCode { get; set; }
		public string SkuEan { get; set; }
		public string SkuModel { get; set; }
		public string SkuSize { get; set; }
		public string SkuColor { get; set; }
		public string SkuVoltage { get; set; }

		public List<CustomAttribute> CustomAttributes { get; set; }

		[JsonIgnore]
		public Sku Sku { get; set; }

		public List<Images> Images;

		public CatalogSettings CatalogSettings { get; set; }
		public bool Available { get; set; }
		public int? Quantity { get; set; }
		public Guid? StockParticipantGroupId { get; set; }
		public SkuPrice Prices { get; set; }
		public bool EnableCustomPromotionalPrice  { get; set; }
		public CustomPromotionalPriceModel CustomPromotionalPrice { get; set; }

		public decimal? AvailableBalance { get; set; }
		// Custo dos pontos necessário para completar o valor do produto
		public decimal? PointsNeededPurchaseCost { get; set; }

		public List<Ranking> Rankings { get; set; }
		[JsonIgnore]
		public Shipping Shipping { get; private set; }

		public ProductDetailsModel(ElasticsearchProduct elasticProd, Sku selectedSku = null, PartnerSettingsDto partnerSettings = null)
		{
			Id = Guid.Parse(elasticProd.Id);
			BuId = elasticProd.BuId;
			ElasticsearchId = elasticProd.ElasticsearchId;
			Name = elasticProd.Name;
			Active = elasticProd.Active;
			Description = elasticProd.Description;

			ProcessType = ProcessTypeHelper.Parse(elasticProd.ProcessType);
			Offline = ProcessTypeHelper.IsOffline(ProcessType);
			ProductType = EnumHelper<ProductType>.Parse(elasticProd.ProductType);
			RegisterType = EnumHelper<RegisterType>.Parse(elasticProd.RegisterType);
			LayoutType = elasticProd.LayoutType;

			ManufacturerId = Guid.Parse(elasticProd.ManufacturerId);
			Manufacturer = elasticProd.Manufacturer;

			SetPartner(elasticProd, new PartnerSettingsModel(partnerSettings));

			// TODO: Classificação é considerada somente quando tiver Departamento e Categoria vinculada
			if (!string.IsNullOrEmpty(elasticProd.DepartmentId) && !string.IsNullOrEmpty(elasticProd.CategoryId))
			{
				DepartmentId = Guid.Parse(elasticProd.DepartmentId);
				Department = elasticProd.Department;
				CategoryId = Guid.Parse(elasticProd.CategoryId);
				Category = elasticProd.Category;
				HasClassification = true;
			}
			if (elasticProd.Rankings != null && elasticProd.Rankings.Count > 0)
				Rankings = elasticProd.Rankings;

			if (!String.IsNullOrEmpty(elasticProd.SubcategoryId))
			{
				SubcategoryId = Guid.Parse(elasticProd.SubcategoryId);
				Subcategory = elasticProd.Subcategory;
			}
			// Keywords = elasticProd.Keywords;
			Information = elasticProd.Information;
			Characteristics = elasticProd.Characteristics;
			TechnicalSpecifications = elasticProd.TechnicalSpecifications;
			CatalogSettings = elasticProd.CatalogSettings;
			// Seta os dados do SKU principal
			if (elasticProd.Skus != null)
			{
				Images = elasticProd.GetSkusImages();
				if (selectedSku == null)
				{
					selectedSku = elasticProd.GetMasterActiveOrFirstActive();
				}
				SetSku(selectedSku);
				Available = elasticProd.IsActive(selectedSku);
				OnlyOneSku = elasticProd.Skus.Count == 1;
				Attributes = ProductAttributes.Of(elasticProd.Skus);
				EnableCustomPromotionalPrice = selectedSku.Price.EnableCustomPromotionalPrice;
				CustomPromotionalPrice = CustomPromotionalPriceModel.Of(selectedSku.Price.CustomPromotionalPrice);
			}
		}

		private void SetPartner(ElasticsearchProduct elasticProd, PartnerSettingsModel partnerSettings)
		{
			PartnerIdentifierCode = elasticProd.PartnerIdentifierCode;
			PartnerId = Guid.Parse(elasticProd.PartnerId);
			Partner = elasticProd.Partner;

			this.SetPartnerSettings(partnerSettings);
		}

		public void SetPartnerSettings(PartnerSettingsModel partnerSettings)
		{
			if (partnerSettings != null)
			{
				PartnerSettings = partnerSettings;
			}
		}


		public void SetDynamicCartItemPriceDefinedByParticipant(CartItem item) {
			if (!Prices.IsDynamicPriceDefinedByParticipant())
				return;

			SetDynamicPriceToCalculate(item.UnitPrices.GetPointsOrZero());
		}

		public void SetDynamicPriceToCalculate(decimal? newPrice)
        {
            if (!IsDynamicPriceDefinedByParticipant())
                throw MotivaiException.ofValidation("Não é permitido alterar o preço do produto.");
            Prices.ValidateDynamicPriceRange = true;
			Prices.PriceFrom = 0;
            Prices.Price = newPrice ?? 0;
            if (Prices.ApplyFactorConversion)
            {
                Prices.PriceCurrency = newPrice ?? 0;
            }
        }

		public bool IsDynamicPriceDefinedByParticipant()
		{
			return  Prices != null && Prices.IsDynamicPriceDefinedByParticipant();
		}
		private void SetSku(Sku selectedSku)
		{
			selectedSku.ForNull("Produto não tem um SKU.");

			this.Sku = selectedSku;
			SkuId = Guid.Parse(selectedSku.Id);
			SkuCode = selectedSku.Code;
			SkuEan = selectedSku.Ean;
			SkuIntegrationCode = selectedSku.IntegrationCode;
			if (selectedSku.Attributes != null)
			{
				SkuModel = selectedSku.Attributes.Model;
				SkuSize = selectedSku.Attributes.Size;
				SkuColor = selectedSku.Attributes.Color;
				SkuVoltage = selectedSku.Attributes.Voltage;
			}
			if (selectedSku.CustomAttributes != null && selectedSku.CustomAttributes.Count > 0)
			{
				CustomAttributes = selectedSku.CustomAttributes;
			}
			Images = selectedSku.Images;
			if (selectedSku.Price != null)
			{
				Prices = SkuPrice.Of(selectedSku.Price);
			}
			Shipping = selectedSku.Shipping;
		}

		public bool HasUpdatedSalePrice()
		{
			return Prices != null;
		}

		public void ValidateDynamicPrice() {
			Prices.ValidateDynamicPriceDefinedByParticipant();
		}
    }

	public class ProductAttributes
	{
		public string SkuId { get; set; }
		public string SkuCode { get; set; }
		public string[] Models { get; set; }
		public string[] Sizes { get; set; }
		public string[] Colors { get; set; }
		public string[] ColorsHashes { get; set; }
		public string[] Voltages { get; set; }
		public List<CustomAttribute> CustomAttributes { get; set; }
        public bool DynamicPrice { get; set; }

        ///<summary>
        /// Indicação do responsável para atribuição do preço dinamico
        ///</summary>
        [BsonRepresentation(BsonType.String)]
        public PriceSettersType? DynamicPricingSetter { get; set; }

        public decimal? DynamicPriceMinimumValue { get; set; }

        public decimal? DynamicPriceMaximumValue { get; set; }

        public bool IsDynamicPriceDefinedByParticipant() {
            return DynamicPricingSetter ==  PriceSettersType.PARTICIPANT;
        }


		public static ProductAttributes Of(List<Sku> skus)
		{
			if (skus == null) return new ProductAttributes();
			// ? Será exibido somente os atributos dos produtos ativos?
			skus = skus.Where(s => s.Active && s.ActivePartner).ToList();
			return new ProductAttributes()
			{
				Models = skus.Where(s => s.Attributes != null).Select(s => s.Attributes.Model)
					.Where(v => !string.IsNullOrEmpty(v))
					.Distinct().ToArray(),
				Sizes = skus.Where(s => s.Attributes != null).Select(s => s.Attributes.Size)
					.Where(v => !string.IsNullOrEmpty(v))
					.Distinct().ToArray(),
				Colors = skus.Where(s => s.Attributes != null).Select(s => s.Attributes.Color)
					.Where(v => !string.IsNullOrEmpty(v))
					.Distinct().ToArray(),
				ColorsHashes = skus.Where(s => s.Attributes != null).Select(s => s.Attributes.ColorHash)
					.Where(v => !string.IsNullOrEmpty(v))
					.Distinct().ToArray(),
				Voltages = skus.Where(s => s.Attributes != null).Select(s => s.Attributes.Voltage)
					.Where(v => !string.IsNullOrEmpty(v))
					.Distinct().ToArray(),
				CustomAttributes = skus.Count == 1 ? skus.FirstOrDefault().CustomAttributes : null
			};
		}
	}
}
