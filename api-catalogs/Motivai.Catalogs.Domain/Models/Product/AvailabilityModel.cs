using Motivai.SharedKernel.Domain.Entities.References.Campaign;

namespace Motivai.Catalogs.Domain.Models.Product
{
	public class AvailabilityModel
	{
		public bool Available { get; set; }
		public bool Promotional { get; set; }
		public decimal? PriceFrom { get; set; }
		public decimal Price { get; set; }
		public CoinName CoinName { get; set; }
		public bool DynamicPrice { get; set; }

		///<summary>
		/// nem todos os parceiros possuem estoque
		///</summary>
		public int? Quantity { get; set; }

		public bool HasPromotionalPrice()
		{
			return PriceFrom.HasValue && PriceFrom > 0 &&
				Price > 0 && PriceFrom > Price;
		}

		public static AvailabilityModel Of(bool available, SkuPrice price, int? quantity)
		{
			var model = new AvailabilityModel()
			{
				Available = available
			};
			if (available)
			{
				model.Promotional = price.Promotional;
				model.PriceFrom = price.PriceFrom;
				model.Price = price.Price;
				model.CoinName = price.CoinName;
				model.DynamicPrice = price.DynamicPrice;
				model.Quantity = quantity;
			}
			return model;
		}
	}
}