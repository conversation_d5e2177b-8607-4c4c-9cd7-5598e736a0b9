using System;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Models.Product {
    public class PartnerSku {
        public Guid PartnerId { get; set; }
        public string PartnerName { get; set; }
        public Guid ProductId { get; set; }
        public Guid SkuId { get; set; }
        public string SkuCode { get; set; }
        public string SkuName { get; set; }
        public string ElasticSearchId { get; set; }

        public static PartnerSku OfPartnerAndSkuCode(Guid partnerId, string skuCode) {
            return new PartnerSku() {
                PartnerId = partnerId,
                SkuCode = skuCode
            };
        }

        public void Validate() {
            if (PartnerId == Guid.Empty)
                throw MotivaiException.ofValidation("Parceiro inválido.");
            if (SkuId == Guid.Empty)
                throw MotivaiException.ofValidation("SKU inválido.");
        }
    }
}