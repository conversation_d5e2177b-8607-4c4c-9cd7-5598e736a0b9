using System;

using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.Catalogs.Domain.Models.Product
{

    public class ResumedProduct
    {
        public string ElasticsearchId { get; set; }
        public Guid PartnerId { get; set; }
        public Guid SkuId { get; set; }
        public string SkuCode { get; set; }
        public ProcessType ProcessType { get; set; }
        public Guid DepartmentId { get; set; }
        public Guid? CategoryId { get; set; }
        public Guid? SubcategoryId { get; set; }
        public decimal TotalAmount { get; set; }
    }
}