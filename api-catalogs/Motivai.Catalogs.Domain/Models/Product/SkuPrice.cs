using System;

using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

using Newtonsoft.Json;

namespace Motivai.Catalogs.Domain.Models.Product
{
    public class SkuPrice
    {
        [JsonIgnore] public bool ApplyFactorConversion { get; set; }

        public bool Promotional { get; set; }
        public decimal? PriceFrom { get; set; }
        public decimal Price { get; set; }
        public decimal PriceCurrency { get; set; }
        public CoinName CoinName { get; set; }

        ///<summary>
        /// Define se o preço do produto pode ser definido pelo participante ou após o fechamento do pedido pelo administrativo.
        ///</summary>
        public bool DynamicPrice { get; set; }

        ///<summary>
        /// Indicação do responsável para atribuição do preço dinamico
        ///</summary>
        [BsonRepresentation(BsonType.String)]
        public PriceSettersType? DynamicPricingSetter { get; set; }

        public decimal? DynamicPriceMinimumValue { get; set; }
        public decimal? DynamicPriceMaximumValue { get; set; }

        ///<summary>
        /// Valida o preço unit informado com o range de preço dinâmica.
        ///</summary>
        public bool ValidateDynamicPriceRange { get; set; }

        public DetailedPrice DetailedPrice { get; set; }
        public DetailedShippingCost DetailedShippingCost { get; set; }

        public Amount Discount { get; set; }

        public bool EnableCustomPromotionalPrice { get; set; }
        public CustomPromotionalPrice CustomPromotionalPrice { get; set; }

        public static SkuPrice OfPriced(decimal? priceFrom, DetailedPrice detailedPrice)
        {
            return new SkuPrice()
            {
                Promotional = priceFrom.HasValue && priceFrom.Value > 0,
                PriceFrom = priceFrom,
                Price = detailedPrice.GetSalePrice().GetPoints(),
                PriceCurrency = detailedPrice.GetSalePrice().GetCurrency(),
                DetailedPrice = detailedPrice
            };
        }

        public static SkuPrice OfCustomPromotionalPrice(SkuPrice prices, decimal? priceFrom, DetailedPrice detailedPrice)
        {
            return new SkuPrice()
            {
                Promotional = priceFrom.HasValue && priceFrom.Value > 0,
                PriceFrom = priceFrom,
                Price = detailedPrice.GetSalePrice().GetPoints(),
                PriceCurrency = detailedPrice.GetSalePrice().GetCurrency(),
                DetailedPrice = detailedPrice,
                EnableCustomPromotionalPrice = prices.EnableCustomPromotionalPrice,
                CustomPromotionalPrice = prices.CustomPromotionalPrice,
            };
        }

        public static SkuPrice OfPriced(DetailedPrice detailedPrice, decimal? priceFrom,
            bool dynamicPrice, PriceSettersType? dynamicPricingSetter = null,
            decimal? dynamicPricingMinimumValue = null, decimal? dynamicPriceMaximumValue = null)
        {
            return new SkuPrice()
            {
                DynamicPrice = dynamicPrice,
                DynamicPricingSetter = dynamicPricingSetter,
                DynamicPriceMinimumValue = dynamicPricingMinimumValue,
                DynamicPriceMaximumValue = dynamicPriceMaximumValue,
                ValidateDynamicPriceRange = dynamicPricingSetter == PriceSettersType.PARTICIPANT,
                Promotional = priceFrom.HasValue && priceFrom.Value > 0,
                PriceFrom = priceFrom,
                Price = detailedPrice.GetSalePrice().GetPoints(),
                PriceCurrency = detailedPrice.GetSalePrice().GetCurrency(),
                DetailedPrice = detailedPrice
            };
        }


        public void UpdatePartnerCosts(decimal price, decimal? priceFrom)
        {
            PriceCurrency = price;
            PriceFrom = priceFrom ?? 0;
            ApplyFactorConversion = true;
        }

        private static SkuPrice OfCurrencyToApplyFactor(Price prices)
        {
            var price = new SkuPrice()
            {
                ApplyFactorConversion = true,
                PriceCurrency = prices.SaleValue,
                EnableCustomPromotionalPrice = prices.EnableCustomPromotionalPrice,
                CustomPromotionalPrice = prices.CustomPromotionalPrice
            };
            price.SetPrices(prices.SaleValue, prices.PromotionalValue);
            return price;
        }

        private static SkuPrice OfSkuAvailabilityApplyingFactorConversion(AvailabilityModel availabilityModel)
        {
            var price = new SkuPrice() { ApplyFactorConversion = true, PriceCurrency = availabilityModel.Price, };

            price.SetPrices(availabilityModel.Price, availabilityModel.PriceFrom);
            return price;
        }

        private static SkuPrice OfPoints(Price prices)
        {
            var price = new SkuPrice()
            {
                ApplyFactorConversion = false,
                EnableCustomPromotionalPrice = prices.EnableCustomPromotionalPrice,
                CustomPromotionalPrice = prices.CustomPromotionalPrice
            };
            price.SetPrices(prices.SaleValue, prices.PromotionalValue);
            return price;
        }

        private static SkuPrice OfPoints(AvailabilityModel availabilityModel)
        {
            var price = new SkuPrice() { ApplyFactorConversion = false, };
            price.SetPrices(availabilityModel.Price, availabilityModel.PriceFrom);
            return price;
        }

        public static SkuPrice Of(Price prices)
        {
            if (prices == null) return null;
            if (prices.DynamicPrice)
            {
                return new SkuPrice()
                {
                    DynamicPrice = true,
                    DynamicPricingSetter = prices.DynamicPricingSetter,
                    DynamicPriceMinimumValue = prices.DynamicPriceMinimumValue,
                    DynamicPriceMaximumValue = prices.DynamicPriceMaximumValue
                };
            }

            return prices.IsApplyConversionFactor()
                ? OfCurrencyToApplyFactor(prices)
                : OfPoints(prices);
        }

        public static SkuPrice Of(AvailabilityModel availabilityModel, bool? applyConversionFactor = null)
        {
            return applyConversionFactor != null && applyConversionFactor == true
                ? OfSkuAvailabilityApplyingFactorConversion(availabilityModel)
                : OfPoints(availabilityModel);
        }

        public bool HasDiscount()
        {
            return Discount.GetPointsOrZero() > 0;
        }

        public void ValidateDynamicPriceDefinedByParticipant()
        {
            if (IsDynamicPriceDefinedByParticipant())
            {
                if (Price < DynamicPriceMinimumValue)
                    throw MotivaiException.ofValidation("Valor minimo para resgate do produto é " +
                                                             DynamicPriceMinimumValue);


                if (Price > DynamicPriceMaximumValue)
                    throw MotivaiException.ofValidation("Valor maximo para resgate do produto é " +
                                                             DynamicPriceMaximumValue);
            }
        }

        public bool HasEmbeddedShippingCost()
        {
            return DetailedShippingCost != null &&
                   DetailedShippingCost.Type == SharedKernel.Domain.Enums.ShippingType.Embedded;
        }

        private void SetPrices(decimal price, decimal? priceFrom)
        {
            if (priceFrom.HasValue && priceFrom.Value > price)
            {
                this.Promotional = true;
                this.PriceFrom = priceFrom;
            }
            else
            {
                this.Promotional = false;
            }

            this.Price = price;
        }

        public void ApplyDiscount(Amount discount)
        {
            if (discount == null)
            {
                this.Discount = Amount.Zero();
                return;
            }

            this.Discount = discount;
            if (!this.Promotional)
            {
                this.PriceFrom = this.Price;
                this.Promotional = true;
            }

            this.Price = this.Price - discount.GetPointsOrZero();
            this.PriceCurrency = this.PriceCurrency - discount.GetCurrencyOrZero();
        }

        public void SetFixedPrice(decimal fixedSalePrice)
        {
            SetPrices(fixedSalePrice, PriceFrom.HasValue && PriceFrom.Value > fixedSalePrice ? PriceFrom : null);
            if (this.DetailedPrice != null)
            {
                this.DetailedPrice.SetFixedPrice(Amount.Of(this.DetailedPrice.GetSalePrice().GetCurrencyOrZero(),
                    fixedSalePrice));
            }
        }

        public Amount GetCalculatedSalePrice()
        {
            if (this.DetailedPrice == null)
            {
                return Amount.Of(PriceCurrency, Price);
            }

            return this.DetailedPrice.GetSalePrice();
        }

        public Amount GetSalePrice()
        {
            return Amount.Of(PriceCurrency, Price);
        }

        private void AddCost(Amount cost)
        {
            PriceCurrency += cost.GetCurrencyOrZero();
            Price += cost.GetPointsOrZero();
            if (PriceFrom.HasValue)
            {
                PriceFrom += cost.GetPointsOrZero();
            }
        }

        public void AddFee(PlatformFeeDetails platformFeeDetails)
        {
            AddCost(platformFeeDetails.RiskAssessmentAnalysisFeeAmount);
            DetailedPrice.RiskAssessmentFee = platformFeeDetails.RiskAssessmentAnalysisFeeAmount;
        }

        public void AddEmbeddedShippingCost(DetailedShippingCost embeddedShippingCostDetails)
        {
            if (embeddedShippingCostDetails.Type != SharedKernel.Domain.Enums.ShippingType.Embedded)
                throw MotivaiException.ofValidation("Frete informado não é embutido no produto.");
            DetailedShippingCost = embeddedShippingCostDetails;
            AddCost(embeddedShippingCostDetails.GetEmbeddedCost());
        }

        public bool IsDynamicPriceDefinedByParticipant()
        {
            return DynamicPrice && DynamicPricingSetter == PriceSettersType.PARTICIPANT;
        }

        public bool ShouldValidateDynamicPriceRange()
        {
            return IsDynamicPriceDefinedByParticipant() && ValidateDynamicPriceRange;
        }

        public bool HasCustomPromotionalValue()
        {
            return EnableCustomPromotionalPrice && CustomPromotionalPrice != null;
        }

        // !DateTime.UtcNow.Between(CustomPromotionalPrice.StartDate.Value, CustomPromotionalPrice.EndDate.Value)
        public void ValidateCustomPromotionalValueAndSetPrice()
        {
            if (!HasCustomPromotionalValue() || !(DateTime.UtcNow >= CustomPromotionalPrice.StartDate.Value && DateTime.UtcNow <= CustomPromotionalPrice.EndDate.Value)) {
                EnableCustomPromotionalPrice = false;
                return;
            }

            PriceFrom = CustomPromotionalPrice.PriceFrom ?? 0;
            PriceCurrency = Price = CustomPromotionalPrice.PriceFor ?? 0;
            Promotional = PriceFrom > PriceCurrency;
            ApplyFactorConversion = true;

            SetDetailedPartnerPrice();
        }

        private void SetDetailedPartnerPrice()
        {
            if (DetailedPrice == null)
            {
                DetailedPrice = new DetailedPrice();
            }

            DetailedPrice.PartnerPrice = Amount.OfCurrency(PriceCurrency);
        }
    }
}