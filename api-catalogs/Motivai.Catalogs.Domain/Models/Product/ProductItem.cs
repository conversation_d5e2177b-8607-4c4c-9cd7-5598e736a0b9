using System;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Models.Product
{
	public class ProductItem
	{
        public ProductType ProductType { get; set; }
		public ProcessType ProcessType { get; set; }
		public Guid PartnerId { get; set; }
		public Guid ProductId { get; set; }
		public string ElasticsearchId { get; set; }
		public Guid SkuId { get; set; }
		public string SkuCode { get; set; }

		public Guid DepartmentId { get; set; }
		public Guid CategoryId { get; set; }
		public Guid? SubcategoryId { get; set; }

		public Amount SalePrice { get; set; }
		public int Quantity { get; set; }

		public static ProductItem Of(ProductShowcaseModel product)
		{
			return new ProductItem()
			{
                ProductType = product.ProductType,
				ProcessType = product.ProcessType,
				PartnerId = product.PartnerId,
				ProductId = product.ProductId,
				ElasticsearchId = product.Id,
				SkuId = product.SkuId,
				SkuCode = product.SkuCode,
				DepartmentId = product.DepartmentId,
				CategoryId = product.CategoryId,
				SubcategoryId = product.SubcategoryId,
				SalePrice = product.GetCalculatedSalePrice(),
				Quantity = 1
			};
		}

		public static ProductItem Of(ProductDetailsModel product)
		{
			return new ProductItem()
			{
                ProductType = product.ProductType,
				ProcessType = product.ProcessType,
				PartnerId = product.PartnerId,
				ProductId = product.Id,
				ElasticsearchId = product.ElasticsearchId,
				SkuId = product.SkuId,
				SkuCode = product.SkuCode,
				DepartmentId = product.DepartmentId,
				CategoryId = product.CategoryId,
				SubcategoryId = product.SubcategoryId,
				SalePrice = product.Prices.GetCalculatedSalePrice(),
				Quantity = 1
			};
		}

		public static ProductItem Of(CartItem item)
		{
			return new ProductItem()
			{
                ProductType = item.ProductType,
				ProcessType = item.ProcessType,
				PartnerId = item.PartnerId ?? Guid.Empty,
				ProductId = item.ProductId,
				ElasticsearchId = item.ElasticId,
				SkuId = item.SkuId,
				SkuCode = item.SkuCode,
				DepartmentId = item.DepartmentId,
				CategoryId = item.CategoryId,
				SubcategoryId = item.SubcategoryId,
				SalePrice = item.GetSalePrice(),
				Quantity = item.Quantity
			};
		}
	}
}