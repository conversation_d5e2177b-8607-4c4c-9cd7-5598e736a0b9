using Motivai.Catalogs.Domain.Dtos;

namespace Motivai.Catalogs.Domain.Models.Product
{
    public class PartnerSettingsModel
    {

        public bool CartItemLimitEnabled { get; set; }
        public int? CartItemLimit { get; set; }
        public bool DisableAvailabilityPriceQuery { get; set; }
        public bool DisableCartCalculation { get; set; }
        public bool DisableVoucherQueryIntegration { get; set; }
        public bool EnableCustomPromotionalPrice { get; set; }

        public PartnerSettingsModel() {}

        public PartnerSettingsModel(PartnerSettingsDto partnerSettings)
        {
            if (partnerSettings != null)
            {
                CartItemLimitEnabled = partnerSettings.CartItemLimitEnabled;
                CartItemLimit = partnerSettings.CartItemLimit;
                DisableAvailabilityPriceQuery = partnerSettings.DisableAvailabilityPriceQuery;
                DisableCartCalculation = partnerSettings.DisableCartCalculation;
                DisableVoucherQueryIntegration = partnerSettings.DisableVoucherQueryIntegration;
                EnableCustomPromotionalPrice = partnerSettings.EnableCustomPromotionalPrice;
            }
        }
    }
}