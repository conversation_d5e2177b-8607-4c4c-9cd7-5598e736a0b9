using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Newtonsoft.Json;


namespace Motivai.Catalogs.Domain.Models.Product {
    public class ProductShowcaseModel {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool Offline { get; set; }
        public string ImageUrl { get; set; }

        public bool Available { get; set; }
        public Guid? ParticipantGroupdIdWithStock { get; set; }

        public bool Promotional { get; set; }
        ///<summary>
        /// Preço De calculado com as regras da campanha.
        ///</summary>
        public decimal? PriceFrom { get; set; }
        ///<summary>
        /// Preço Por calculado com as regras da campanha.
        ///</summary>
        public decimal Price { get; set; }

        public bool EnableCustomPromotionalPrice  { get; set; }
		public CustomPromotionalPriceModel CustomPromotionalPrice { get; set; }

        public decimal? AvailableBalance { get; set; }
        // Custo dos pontos necessário para completar o valor do produto
        public decimal? PointsNeededPurchaseCost { get; set; }
        public bool? DynamicPrice { get; set; }
        public string DynamicPriceDescription { get; set; }
        public string ProductModelLabel { get; set; }
        public string ProductSizeLabel { get; set; }
        public string ProductVoltageLabel { get; set; }
        public string ProductColorLabel { get; set; }
        public string ProductInformationTabTitle { get; set; }
        public string ProductTechnicalSpecificationsTabTitle { get; set; }
        public CoinName CoinName { get; set; }

        public ProcessType ProcessType { get; set; }

        public ProductType ProductType { get; set; }

        public Guid PartnerId { get; set; }
        public string PartnerName { get; set; }

        public string SkuCode { get; set; }
        public string SkuModel { get; set; }
        public string SkuSize { get; set; }
        public string SkuColor { get; set; }
        public string SkuVoltage { get; set; }

        public List<Ranking> Rankings { get; set; }

        [JsonIgnore]
        public Price PartnerPrices { get; set; }
        [JsonIgnore]
        public SkuPrice CampaignPrices { get; set; }

        public Guid ProductId { get; set; }
        public Guid SkuId { get; set; }

        public Guid DepartmentId { get; set; }

        public Guid CategoryId { get; set; }

        public Guid? SubcategoryId { get; set; }

        public List<CustomAttribute> CustomAttributes { get; set; }

        public Amount GetCalculatedSalePrice() {
            if (CampaignPrices == null)
                return Amount.Zero();
            return CampaignPrices.GetCalculatedSalePrice();
        }

        public void SetPrices(SkuPrice price) {
            this.CampaignPrices = price;
            if (price.DynamicPrice) {
                this.DynamicPrice = price.DynamicPrice;
            } else if (price.Promotional && price.PriceFrom > 0) {
                Promotional = true;
                this.PriceFrom = price.PriceFrom;
            } else {
                Promotional = false;
                this.PriceFrom = null;
            }
            this.Price = price.Price;

            if (price.EnableCustomPromotionalPrice)
            {
                this.EnableCustomPromotionalPrice = true;
                this.CustomPromotionalPrice = CustomPromotionalPriceModel.Of(price.CustomPromotionalPrice);
            }
        }
    }

    public class CustomPromotionalPriceModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public static CustomPromotionalPriceModel Of(CustomPromotionalPrice customPromotionalPrice)
        {
            if (customPromotionalPrice == null)
                return null;
            return new CustomPromotionalPriceModel()
            {
                StartDate = customPromotionalPrice.StartDate,
                EndDate = customPromotionalPrice.EndDate.Value.Subtract(TimeSpan.FromMinutes(10))
            };
        }
    }
}
