﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogo.Domain.Models {
    public class MarketplaceItem {
        public string ElasticId { get; set; }
        public string Name { get; set; }
        public string Store { get; set; }
        public decimal Price { get; set; }
        public decimal? AvailableBalance { get; set; }
        public decimal? PointsNeededPurchaseCost { get; set; }

        public static MarketplaceItem From(ProductShowcaseModel product) {
            return new MarketplaceItem() {
                ElasticId = product.Id,
                Store = product.PartnerName,
                Name = product.Name,
                Price = product.Price,
                AvailableBalance = product.AvailableBalance,
                PointsNeededPurchaseCost = product.PointsNeededPurchaseCost
            };
        }
    }
}