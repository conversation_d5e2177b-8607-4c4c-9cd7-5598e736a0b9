using System.Collections.Generic;
using System.Linq;

namespace Motivai.Catalogs.Domain.Models.Integrations
{
	public class CartCalculationResult
	{
		public decimal ShippingCost { get; set; }
		public decimal TotalCostOrders { get; set; }

		public List<CartItemResult> Products { get; set; }

		public bool IsError {
			get {
				return !string.IsNullOrEmpty(ErrorCode);
			}
		}
		public string Error { get; set; }
		public string ErrorType { get; set; }
		public string ErrorCode { get; set; }

		public bool HasProducts()
		{
			return Products != null && Products.Count > 0;
		}

		public CartItemResult GetItemBySkuCode(string skuCode) {
			return Products?.FirstOrDefault(i => i.SkuCode == skuCode);
		}
	}

	public class CartItemResult
	{
		public string SkuCode { get; set; }
		public int Amount { get; set; }
		///<summary>
		/// Determina se deve ou não atualizar o preço unitário após cálculo do carrinho
		/// true não atualiza
		///</summary>
		public bool? KeepUnitCost { get; set; }
		public decimal UnitCost { get; set; }
		///<summary>
		/// Subtotal do produto (valor unit * quantidade)
		///</summary>
		public decimal TotalCost { get; set; }
		public decimal TotalShippingCost { get; set; }
		public string DeliveryForecast { get; set; }

		public bool IsError { get; set; }
		public string Error { get; set; }
		public string ErrorType { get; set; }
		public string ErrorCode { get; set; }

		public decimal GetUnitPrice()
		{
			if (UnitCost > 0) return UnitCost;
			if (Amount <= 0) return 0;
			return TotalCost / Amount;
		}

		public bool IsToKeepUnitCost()
		{
			return this.KeepUnitCost.HasValue && this.KeepUnitCost.Value;
		}
	}
}