using System;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Models.Integrations.BillPayment
{
	public class PartnerBillPaymentIssue
	{
		///<summary>
		/// Parceiro de pague contas da consulta.
		///</summary>
		public BillPaymentPartner BillDetailsQueryPartner { get; set; }
		///<summary>
		/// Parceiro de pague contas usado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }
		public long PartnerCorrelationId { get; set; }
		public RegisterData RegisterData { get; set; }
		public string SettleDate { get; set; }
		public string NextSettle { get; set; }

		public decimal GrossCurrency { get; set; }
		public string Barcode { get; set; }
		public string ServiceType { get; set; }
		public DateTime? MaturityDate { get; set; }
		public string Assignor { get; set; }

		public Amount TotalPartnerCost { get; set; }

		public decimal PartnerFee { get; set; }
		public Amount TotalPartnerFee { get; set; }

		public decimal PlatformFee { get; set; }
		public Amount TotalPlatformFee { get; set; }

		public decimal ClientFee { get; set; }
		public Amount TotalClientFee { get; set; }

		public Amount ParticipantCost { get; set; }

		public Amount TotalDiscount { get; set; }

		public LocationInfo LocationInfo { get; set; }

		public static PartnerBillPaymentIssue Of(BillDetails bill)
		{
			return new PartnerBillPaymentIssue()
			{
				BillDetailsQueryPartner = bill.BillDetailsQueryPartner,
				BillPaymentPartner = bill.BillPaymentPartner,
				Barcode = bill.BarCode,
				ServiceType = bill.ServiceType.ToString(),
				GrossCurrency = bill.BillingAmount,
				MaturityDate = bill.DueDate,
				Assignor = bill.Assignor,
				TotalPartnerCost = bill.PartnerCost,
				PartnerFee = bill.PartnerFee,
				TotalPartnerFee = bill.PartnerFeeAmount,
				PlatformFee = bill.GpFee,
				TotalPlatformFee = bill.GpFeeAmount,
				ClientFee = bill.ClientPartnerFee,
				TotalClientFee = bill.ClientPartnerFeeAmount,
				ParticipantCost = bill.ParticipantCost,
				PartnerCorrelationId = bill.PartnerCorrelationId,
				SettleDate = bill.SettleDate,
				NextSettle = bill.NextSettle,
				RegisterData = bill.RegisterData,
				TotalDiscount = Amount.Zero(),
				LocationInfo = bill.LocationInfo
			};
		}
	}
}