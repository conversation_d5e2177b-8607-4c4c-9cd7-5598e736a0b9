namespace Motivai.Catalogs.Domain.Models.Integrations {
    public class DisponibilityResponse {
        public bool IsError { get; set; }
        public ProductResult Product { get; set; }
    }

    public class ProductResult {
        public bool Availability { get; set; }
        public decimal CostOf { get; set; }
        public decimal Cost { get; set; }
        public int? Quantity { get; set; }

        public bool HasPromotionalPrice() {
            return CostOf > 0 && Cost > 0 && CostOf > Cost;
        }

        public decimal? GetPromotionalPrice() {
            return HasPromotionalPrice() ? (decimal?) CostOf : null;
        }
    }
}