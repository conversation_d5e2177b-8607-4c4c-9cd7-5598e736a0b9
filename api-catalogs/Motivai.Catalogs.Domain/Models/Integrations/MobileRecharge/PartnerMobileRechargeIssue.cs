using System;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.ValuesObject;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Catalogs.Domain.Models.Integrations.MobileRecharge {
    public class PartnerMobileRechargeIssue {
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }
        public Phone Phone { get; set; }
        public DetailedCost Payment { get; set; }

        public static PartnerMobileRechargeIssue Of(MobileRechargeIssue rechargeIssue) {
            return new PartnerMobileRechargeIssue() {
                AccountOperator = rechargeIssue.AccountOperator,
                LocationInfo = rechargeIssue.LocationInfo,
                Phone = new Phone() {
                    CountryNumber = rechargeIssue.CellphoneCountryCode,
                    Ddd = rechargeIssue.CellphoneDdd,
                    PhoneNumber = rechargeIssue.CellphoneNumber,
                    PartnerProviderCode = rechargeIssue.OperatorId,
                    PartnerProviderName = rechargeIssue.OperatorName
                },
                Payment = new DetailedCost() {
                    Type = PaymentType.Cash,
                    Installments = 1,
                    // PartnerTotalPoints = 0,
                    TotalPartnerCost = rechargeIssue.PartnerCost,
                    PlatformFee = rechargeIssue.GpFee,
                    TotalPlatformFee = rechargeIssue.GpFeeAmount,
                    ClientFee = rechargeIssue.ClientPartnerFee,
                    TotalClientFee = rechargeIssue.ClientPartnerFeeAmount,
                    PartnerFee = rechargeIssue.PartnerFee,
                    TotalPartnerFee = rechargeIssue.PartnerFeeAmount,
                    ParticipantCost = rechargeIssue.ParticipantCost,
                    TotalDiscount = Amount.Zero()
                }
            };
        }
    }

    public class DetailedCost {
        public PaymentType Type { get; set; }
        public long Installments { get; set; }

        public Amount TotalPartnerCost { get; set; }

        public decimal PartnerFee { get; set; }
        public Amount TotalPartnerFee { get; set; }

        public decimal PlatformFee { get; set; }
        public Amount TotalPlatformFee { get; set; }

        public decimal ClientFee { get; set; }
        public Amount TotalClientFee { get; set; }

        public Amount ParticipantCost { get; set; }

        public Amount TotalDiscount { get; set; }
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum PaymentType {
        Cash,
        CreditCard
    }

    public class Phone {
        public string Ddd { get; set; }
        public string PhoneNumber { get; set; }
        public string CountryNumber { get; set; }
        public string PartnerProviderCode { get; set; }
        public string PartnerProviderName { get; set; }
    }
}