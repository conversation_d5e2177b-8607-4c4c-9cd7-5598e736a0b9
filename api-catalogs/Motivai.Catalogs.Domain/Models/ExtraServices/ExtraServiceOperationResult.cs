using System;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;

namespace Motivai.Catalogs.Domain.Models.ExtraServices
{
	public class ExtraServiceOperationResult
	{
		public string CatalogExtraServiceId { get; set; }
		///<summary>
		/// Parceiro de pague de contas utilizado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }
		public string ProofPayment { get; set; }
		public DateTime OperationDate { get; set; }
		public DateTime FinishDate { get; set; }
		public string Protocol { get; set; }
		public bool RequiresConfirmation { get; set; }
	}

	public class ExtraServiceConfirmationResult
	{
		public string Protocol { get; set; }
		public bool IsConfirmed { get; set; }
	}
}