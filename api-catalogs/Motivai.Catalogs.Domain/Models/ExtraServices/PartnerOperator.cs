using System;
using System.Collections.Generic;
using System.Linq;

namespace Motivai.Catalogs.Domain.Models.ExtraServices
{
    public class PartnerOperator
    {
        public string CreditCategory { get; set; }
        public string ProviderId { get; set; }
        public string ProviderName { get; set; }
        public CreditType CreditType { get; set; }
        public decimal MinimumCost { get; set; }
        public decimal MaximumCost { get; set; }

        public bool OnlyRechargeMobile()
        {
            return CreditCategory == "MOBILE" && ProviderId != "2130" && ProviderId != "2132" && ProviderId != "2141" && ProviderId != "2142";
        }
    }

}