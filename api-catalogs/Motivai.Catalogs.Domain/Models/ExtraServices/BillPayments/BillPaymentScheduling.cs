using System;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Models.ExtraServices.BillPayments
{
	public class BillPaymentScheduling
	{
		public BillPaymentPartner ScheduledBillPaymentPartner { get; set; }
		public BillPaymentPartner BillPaymentPartner { get; set; }

		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public string ParticipantName { get; set; }
		public string UserDocument { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public dynamic BillDetails { get; set; }
		public DateTime ScheduledPaymentDate { get; set; }

		public decimal PointsFactor { get; set; }
		public Amount ParticipantCost { get; set; }

		public BillDetailedPrice DetailedPrice { get; set; }

		public static BillPaymentScheduling Of(BillDetails billDetails)
		{
			return new BillPaymentScheduling()
			{
				ScheduledBillPaymentPartner = billDetails.BillDetailsQueryPartner,
				BillPaymentPartner = billDetails.BillPaymentPartner,
				UserId = billDetails.UserId.Value,
				ParticipantId = billDetails.ParticipantId.Value,
				ParticipantName = billDetails.ParticipantName,
				UserDocument = billDetails.UserDocument,
				ScheduledPaymentDate = billDetails.ScheduledPaymentDate.Value,
				PointsFactor = billDetails.PointsFactor,
				ParticipantCost = billDetails.ParticipantCost,
				BillDetails = new
				{
					BarCode = billDetails.BarCode,
					ServiceType = billDetails.ServiceType,
					Assignor = billDetails.Assignor,
					DueDate = billDetails.DueDate.HasValue ? billDetails.DueDate.Value.ToString("yyyy-MM-dd") : null,
					BillingAmount = billDetails.BillingAmount,
					FilledManually = billDetails.FilledManually,
					SettleDate = billDetails.SettleDate,
					NextSettle = billDetails.NextSettle,
					PartnerCorrelationId = billDetails.PartnerCorrelationId,
					RegisterData = billDetails.RegisterData
				},
				DetailedPrice = BillDetailedPrice.Of(billDetails),
				AccountOperator = billDetails.AccountOperator,
				LocationInfo = billDetails.LocationInfo
			};
		}
	}

	public class BillDetailedPrice
	{
		///<summary>
		/// Custo do boleto no parceiro.
		///</summary>
		public Amount PartnerCost { get; set; }

		///<summary>
		/// Taxa do Parceiro da Integração em porcentagem (%).
		///</summary>
		public decimal PartnerFee { get; set; }
		///<summary>
		/// Valor taxa do Parceiro da Integração em reais (R$).
		///</summary>
		public Amount PartnerFeeAmount { get; set; }

		///<summary>
		/// Taxa da Motivai em porcentagem (%).
		///</summary>
		public decimal PlatformFee { get; set; }
		///<summary>
		/// Valor da taxa da Motivai em reais (R$).
		///</summary>
		public Amount PlatformFeeAmount { get; set; }

		///<summary>
		/// Taxa geral de parceiros do cliente em porcentagem (%).
		///</summary>
		public decimal ClientPartnerFee { get; set; }
		///<summary>
		/// Valor da taxa geral de parceiros do cliente em reais (R$).
		///</summary>
		public Amount ClientPartnerFeeAmount { get; set; }

		public static BillDetailedPrice Of(BillDetails billDetails)
		{
			return new BillDetailedPrice()
			{
				PartnerCost = billDetails.PartnerCost,
				PartnerFee = billDetails.PartnerFee,
				PartnerFeeAmount = billDetails.PartnerFeeAmount,
				PlatformFee = billDetails.GpFee,
				PlatformFeeAmount = billDetails.GpFeeAmount,
				ClientPartnerFee = billDetails.ClientPartnerFee,
				ClientPartnerFeeAmount = billDetails.ClientPartnerFeeAmount
			};
		}
	}
}