using System;
using System.Collections.Generic;

namespace Motivai.Catalogs.Domain.Models.ExtraServices {
    public class BaseResult<T> {
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string Status { get; set; }
        public bool RequiresConfirmation { get; set; }
        public T PartnerReturn { get; set; }

        public bool IsSuccessful() {
            return Status == "SUCCESS" || ErrorCode == "000";
        }

        public bool HasReturn() {
            return PartnerReturn != null;
        }
    }
}