using System;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Domain.Models.Shippings {
    public class ItemShippingModel {
        public string ElasticsearchId { get; set; }
        public string SkuCode { get; set; }
        public string SkuModel { get; set; }
        public Guid? ShippingAddressId { get; set; }
        public string Cep { get; set; }
        public int Quantity { get; set; }

        public void Validate() {
            ElasticsearchId.ForNullOrEmpty("Informe o produto para o cálculo.");
            SkuCode.ForNullOrEmpty("Informe o SKU para o cálculo.");
            Cep.ForNullOrEmpty("Informe o CEP para o cálculo.");
            if (Quantity <= 0)
                Quantity = 1;
        }
    }
}