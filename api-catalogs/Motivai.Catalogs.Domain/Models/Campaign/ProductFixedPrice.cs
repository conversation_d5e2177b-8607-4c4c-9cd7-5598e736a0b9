using System;

namespace Motivai.Catalogs.Domain.Models.Campaign {
    public class ProductFixedPrice {
        public Guid PartnerId { get; set; }
        public Guid ProductId { get; set; }
        public Guid SkuId { get; set; }
        public decimal FixedSalePrice { get; set; }

        public bool Matches(Guid partnerId, Guid productId, Guid skuId) {
            return this.PartnerId == partnerId && this.ProductId == productId && this.SkuId == skuId;
        }
    }
}