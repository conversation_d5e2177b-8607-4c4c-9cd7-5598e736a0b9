using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Catalogs.Domain.Models.Campaign
{
	public class CampaignShipping
	{
		public Guid Id { get; set; }
		public ShippingType Type { get; set; }

		public FieldValueType? FixedCostType { get; set; }
		public decimal? FixedCostValue { get; set; }

		// public bool CorreiosIntegration { get; set; }
		// public string SourceCep { get; set; }

		public bool ApplyAdditionalValue { get; set; }
		public FieldValueType? AdditionalType { get; set; }
		public decimal? AdditionalValue { get; set; }

		// Filtros de aplicação
		public List<Guid> PartnersFilters { get; set; }
		public List<ClassificationModel> CategoriesFilters { get; set; }
		public List<PartnerSkuFilter> SkusFilters { get; set; }

		public bool CanApply(ShippingCostItemCalculation item)
		{
			return CanApply(item.SkuId, item.PartnerId, item.DepartmentId, item.CategoryId, item.SubcategoryId);
		}

		public bool CanApply(ProductDetailsModel product)
		{
			return CanApply(product.SkuId, product.PartnerId, product.DepartmentId, product.CategoryId, product.SubcategoryId);
		}

		public bool CanApply(ProductShowcaseModel product)
		{
			return CanApply(product.SkuId, product.PartnerId, product.DepartmentId, product.CategoryId, product.SubcategoryId);
		}

		public bool CanApply(ProductItem product)
		{
			return CanApply(product.SkuId, product.PartnerId, product.DepartmentId, product.CategoryId, product.SubcategoryId);
		}

		public bool CanApply(Guid skuId, Guid partnerId, Guid departmentId, Guid categoryId, Guid? subcategoryId)
		{
			if (PartnersFilters != null && PartnersFilters.Contains(partnerId))
			{
				return true;
			}

			if (CategoriesFilters != null && CategoriesFilters.Any(c => c.CanApply(departmentId, categoryId, subcategoryId)))
			{
				return true;
			}

			if (SkusFilters != null && SkusFilters.Any(s => s.CanApply(partnerId, skuId)))
			{
				return true;
			}
			return false;
		}
	}

	public class PartnerSkuFilter
	{
		public Guid PartnerId { get; set; }
		public Guid SkuId { get; set; }

		public bool CanApply(Guid partnerId, Guid skuId)
		{
			return this.PartnerId == partnerId && this.SkuId == skuId;
		}
	}
}