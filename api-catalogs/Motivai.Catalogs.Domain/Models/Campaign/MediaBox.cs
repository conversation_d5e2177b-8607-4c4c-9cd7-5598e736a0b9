using System;
using System.Collections.Generic;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Catalogs.Domain.Models.Campaign
{
	public class MediaBox
	{
		public string Id { get; set; }
		public MediaboxContentType ContentType { get; set; }
		public string Name { get; set; }
		public short Position { get; set; }
		public PartnerSku Product { get; set; }
		public ProductShowcaseModel FeaturedProduct { get; set; }
		public string ImageUrl { get; set; }
		public string MediumImageUrl { get; set; }
		public string SmallImageUrl { get; set; }
		public string Link { get; set; }
		public bool OpenInNewTab { get; set; }
		public DateTime StartDate { get; set; }
		public DateTime EndDate { get; set; }
		public List<Guid> TargetAudiences { get; set; }
		public bool Communication { get; set; }
		public string Identifier { get; set; }
		public CommunicationLocation Location { get; set; }
		public bool? AlwaysShowModal { get; set; }
		public string Message { get; set; }
		public bool HasTargetAudiences()
		{
			return TargetAudiences != null && TargetAudiences.Count > 0;
		}

		public bool HasProduct()
		{
			return ContentType == MediaboxContentType.PRODUCT && Product != null;
		}

		public static MediaBox BuildFrom(MediaBoxCatalogModel mediaBoxCatalogModel)
		{
			var mediaBox = new MediaBox
			{
				Id = mediaBoxCatalogModel.Id,
				ContentType = (MediaboxContentType)mediaBoxCatalogModel.Parametrizations.ContentType,
				Name = mediaBoxCatalogModel.Name,
				Position = (short)mediaBoxCatalogModel.Parametrizations.Position,
				ImageUrl = mediaBoxCatalogModel.ViewParametrizations.ImageParametrizations?.LargeImageUrl,
				SmallImageUrl = mediaBoxCatalogModel.ViewParametrizations.ImageParametrizations?.SmallImageUrl,
				MediumImageUrl = mediaBoxCatalogModel.ViewParametrizations.ImageParametrizations?.MediumImageUrl,
				Link = mediaBoxCatalogModel.Parametrizations.Link,
				OpenInNewTab = mediaBoxCatalogModel.Parametrizations.OpenInNewTab,
				StartDate = mediaBoxCatalogModel.Parametrizations.StartDate,
				EndDate = mediaBoxCatalogModel.Parametrizations.EndDate
			};

			mediaBox.BuildProducts(mediaBoxCatalogModel.PartnerProductSku);

			return mediaBox;
		}

		private void BuildProducts(PartnerProductSku partnerProductSku)
		{
			if (partnerProductSku == null) {
				return;
			}
			this.Product = new PartnerSku {
				PartnerId = partnerProductSku.PartnerId,
				PartnerName = partnerProductSku.PartnerName,
				ProductId = partnerProductSku.ProductId,
				SkuId = partnerProductSku.SkuId,
				ElasticSearchId = partnerProductSku.ElasticSearchId,
				SkuCode = partnerProductSku.SkuCode,
				SkuName = partnerProductSku.SkuName,
			};
		}
	}

	[JsonConverter(typeof(StringEnumConverter))]
	public enum MediaboxContentType
	{
		BANNER,
		PRODUCT
	}

	[JsonConverter(typeof(StringEnumConverter))]
	public enum MediaboxVisibleOn
	{
		CATALOG,
		CAMPAIGN_SITE,
		APP_CASHBACK
	}
}