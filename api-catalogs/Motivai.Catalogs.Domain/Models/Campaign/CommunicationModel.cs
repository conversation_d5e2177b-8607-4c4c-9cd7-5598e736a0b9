using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.Catalogs.Domain.Models.Campaign {
    public class CommunicationModel {
        public Guid Id { get; set; }
        public string Identifier { get; set; }
        public bool Active { get; set; }
        public string Description { get; set; }
        public string ImageUrl { get; set; }
        public string MediumImageUrl { get; set; }
        public string SmallImageUrl { get; set; }
        public bool OpenInNewTab { get; set; }
        public CommunicationLocation CommunicationLocation { get; set; }
        public string CommunicationMessage { get; set; }
        public DateTime CreateDate { get; set; }
        public List<Guid> TargetAudiences { get; set; }

        public bool HasTargetAudiences() {
            return TargetAudiences != null && TargetAudiences.Count > 0;
        }
    }
}