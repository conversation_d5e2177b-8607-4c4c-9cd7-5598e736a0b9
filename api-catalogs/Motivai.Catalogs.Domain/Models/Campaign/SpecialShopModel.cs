using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.Catalogs.Domain.Models.Campaign
{
    public class SpecialShopModel
    {
        public Guid Id { get; set; }
        public CampaignParametrizationOrigin Origin { get; set; }
        public bool Active { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Name { get; set; }
        public int GridItemPosition { get; set; }
        public PartnerSku Product { get; set; }
        public string PrincipalBannerImageUrl { get; set; }
        public string LinkBannerImageUrl { get; set; }
        public string LargeImageUrl { get; set; }
        public string MediumImageUrl { get; set; }
        public string SmallImageUrl { get; set; }
        public ViewParametrization ViewParametrization { get; set; }
        public Parametrizations Parametrizations { get; set; }
        public string PageLinkUrl { get; set; }
        public string PageContent { get; set; }
        public List<Guid> Skus { get; set; }
        public List<ProductShowcaseModel> Products { get; set; }
        public List<Guid> TargetAudiences { get; set; }
        public bool IsActive()
        {
            return Active && DateTime.Now.Between(StartDate, EndDate);
        }

        public bool HasTargetAudiences()
        {
            return TargetAudiences != null && TargetAudiences.Count > 0;
        }

        public static SpecialShopModel BuildFrom(SpecialShopCatalogModel specialShopCatalogModel, CampaignParametrizationOrigin origin)
        {
            var specialShop = new SpecialShopModel
            {
                Id = specialShopCatalogModel.Id,
                Origin = origin,
                Name = specialShopCatalogModel.Name,
                Active = specialShopCatalogModel.Active,
                GridItemPosition = specialShopCatalogModel.Parametrizations.GridItemPosition,
                PrincipalBannerImageUrl = specialShopCatalogModel.ViewParametrizations.ImageParametrizations?.LargeImageUrl,
                LinkBannerImageUrl = specialShopCatalogModel.ViewParametrizations.ImageParametrizations?.MediumImageUrl,
                LargeImageUrl = specialShopCatalogModel.ViewParametrizations.ImageParametrizations?.LargeImageUrl,
                MediumImageUrl = specialShopCatalogModel.ViewParametrizations.ImageParametrizations?.MediumImageUrl,
                SmallImageUrl = specialShopCatalogModel.ViewParametrizations.ImageParametrizations?.SmallImageUrl,
                PageLinkUrl = specialShopCatalogModel.ViewParametrizations.PagelinkUrl,
                StartDate = specialShopCatalogModel.Parametrizations.StartDate,
                EndDate = specialShopCatalogModel.Parametrizations.EndDate
            };

            specialShop.BuildProducts(specialShopCatalogModel.PartnerProductSku);

            return specialShop;
        }

        private void BuildProducts(List<PartnerProductSku> partnerProductSku)
        {
            if (partnerProductSku.IsNullOrEmpty()) {
                return;
            }
			      this.Skus = partnerProductSku.Select(p => p.SkuId).ToList();
        }
    }

    public class ViewParametrization
    {
        public bool ShowAsFeatured { get; set; }
        public int GridItemPosition { get; set; }
        public int GridItemSize { get; set; }
        public string PresentationType { get; set; }
        public bool OpenInNewTab { get; set; }
    }
}
