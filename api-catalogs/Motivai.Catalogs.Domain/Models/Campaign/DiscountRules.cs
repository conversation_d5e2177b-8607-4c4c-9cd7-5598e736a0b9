using System;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Filters;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Motivai.Catalogs.Domain.Models.Campaign {
    public class DiscountRules {
        public Guid Id { get; set; }
        public string Description { get; set; }

        public DiscountModalityType ModalityType { get; set; }

        public bool Active { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public CampaignDiscountType DiscountType { get; set; }
        public decimal DiscountValue { get; set; }

        public FilterRules Filters { get; set; }

        public bool IsValid() {
            return Active && DateTime.UtcNow.Between(StartDate, EndDate);
        }

        public bool OnlyApplicableOnItems() {
            return Filters != null && Filters.HasAtLeastOneFilter();
        }

        public bool OnlyApplcableAtCart() {
            return Filters == null || !Filters.HasAtLeastOneFilter();
        }

        public bool HasAnyFilter() {
            return Filters != null && Filters.HasAtLeastOneFilter();
        }

        public bool CanApplyTo(ProductItem item) {
            return IsValid() && (!HasAnyFilter() ||
                Filters.Match(item.PartnerId, item.ProductId, item.SkuId, item.DepartmentId, item.CategoryId, item.SubcategoryId));
        }

        public Amount CalculateDiscountAmount(Amount salePrice) {
            var discountedPoints = 0m;
            if (DiscountType == CampaignDiscountType.PERCENTAGE) {
                discountedPoints = salePrice.GetPointsOrZero() * DiscountValue / 100m;
            } else {
                discountedPoints = DiscountValue;
            }
            return AmountConversor.WithPointsConversionFactorFromAmount(salePrice)
                .RevertPointsFactorAppliedAndCreateAmount(discountedPoints);
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum DiscountModalityType {
            CATALOG,
            COUPON
        }
    }
}