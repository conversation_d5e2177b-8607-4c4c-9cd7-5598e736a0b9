using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Models.Orders {
    public class FactoryOrderPrice {
        public Guid OrderId { get; set; }
        public Guid ItemGrouperId { get; set; }
        public List<FactoryItemPrice> Items { get; set; }

        public void Validate() {
            if (OrderId == Guid.Empty || ItemGrouperId == Guid.Empty)
                throw MotivaiException.ofValidation("Pedido inválido.");
            if (Items == null || Items.Count == 0)
                throw MotivaiException.ofValidation("Pedido precisa ter pelo menos um item para precificar.");
            Items.ForEach(i => i.Validate());
        }
    }

    public class FactoryItemPrice {
        public Guid SkuId { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal ShippingCost { get; set; }
        public int Quantity { get; set; }

        public void Validate() {
            if (SkuId == Guid.Empty)
                throw MotivaiException.ofValidation("Item inválido.");
            if (UnitPrice < 0)
                throw MotivaiException.ofValidation("Preço do item não pode ser menor que zero.");
            if (Quantity < 0)
                throw MotivaiException.ofValidation("Quantidade do produto não pode ser menor que zero.");
        }
    }
}