using System;
using System.Collections.Generic;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.Catalogs.Domain.Models.Orders
{
    public class MasterOrderResumed
    {
        public bool OccurredError { get; set; }
        public string ErrorDescription { get; set; }
        public OrderStatus Status { get; set; }
        public DateTime UpdateDate { get; set; }
        public string InternalOrderNumber { get; set; }
		public List<UserSelectedPaymentMethod> PaymentMethods { get; set; }
        public string StatusDescription { get; set; }
    }
}
