using System;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Models.Orders {
    public class CardOrder {
        public string OrderNumber { get; set; }
        public string Status { get; set; }
        public string ResumedStatus { get; set; }
        public string CardType { get; set; }
        public DateTime CreateDate { get; set; }
        public string Timezone { get; set; }

        public decimal PointsToTransfer { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal TotalFeesAmount { get; set; }
        public Amount OrderTotalCost { get; set; }
        public DetailedFees detailedFees { get; set; }

        public bool IssueNewCard { get; set; }
        public bool ReissueCard { get; set; }
        public Card Card { get; set; }
        public dynamic ShippingAddress { get; set; }

        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class Card {
        public string Number { get; set; }
    }

    public class DetailedFees {
        public decimal PlasticCost { get; set; }
        public decimal ShippingCost { get; set; }
        public decimal ChargeCost { get; set; }
        public decimal ServiceFee { get; set; }
    }
}