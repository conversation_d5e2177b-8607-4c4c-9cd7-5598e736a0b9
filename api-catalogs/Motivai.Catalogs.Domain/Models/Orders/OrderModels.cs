using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.Catalogs.Domain.Models.Orders
{
	public class MasterOrder
	{
		public Guid Id { get; set; }
		public CampaignType Type { get; set; }
		// Dados do Pedido
		public string InternalOrderNumber { get; set; }
		public DateTime? OrderStartTime { get; set; }
		public DateTime? CreationDate { get; set; }
		public int? TimezoneOffset { get; set; }
		public string Timezone { get; set; }

		public OrderStatus ExternalStatus { get; set; }

		// Dados da Campanha
		public Guid CampaignId { get; set; }
		public Guid? ClientId { get; set; }
		public decimal CampaignPointsFactor { get; set; }
		public decimal ClientPartnersFee { get; set; }

		// Dados do Participante
		public Guid UserId { get; set; }
		public Guid UserParticipantId { get; set; }
        public Guid? SessionId { get; set; }
        public string SessionTrackingId { get; set; }

		// Valores do Pedido
		public string DiscountCoupon { get; set; }
		public Amount Discount { get; set; }

		// Totais do Carrinho
		public Amount TotalAmount { get; set; }
		public Amount ShippingCost { get; set; }

		public List<UserSelectedPaymentMethod> PaymentMethods { get; set; }

		// Itens por Parceiro
		public List<ChildOrder> ChildrenOrders { get; set; }
		public int? EstimatedDeliveryDays { get; set; }
		public bool RequiresAllChildren { get; set; }

		// Dados da Entrega
		public AddressModel ShippingAddress { get; set; }

		// Call Center
		public Guid? CallCenterUserId { get; set; }
		public string CallCenterName { get; set; }
		public string CallCenterEmail { get; set; }
		public DateTime? SessionStart { get; set; }
		public DateTime? SessionFinish { get; set; }

		public bool OccurredError { get; set; }
		public string ErrorDescription { get; set; }

		public List<string> Notifications { get; set; }

		// Dados do Operador
		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }
		public AccountRepresentative AccountRepresentative { get; set; }
		public string InvoiceTaxId { get; set; }
		public CampaignSnapshotSettings CampaignSnapshotSettings { get; set; }

		public static MasterOrder From(Cart cart)
		{
			var orderModel = new MasterOrder
			{
				Type = cart.Type.HasMarketplace() ? cart.Type : CampaignType.B2B,
				CreationDate = cart.CreationDate,
				OrderStartTime = cart.OrderStartTime,
				CampaignId = cart.CampaignId,
				ClientId = cart.ClientId,
				CampaignPointsFactor = cart.CampaignPointsFactor,
				ClientPartnersFee = cart.ClientPartnersFee,
				UserId = cart.UserId,
				UserParticipantId = cart.ParticipantId,
                SessionId = cart.SessionId,
                SessionTrackingId = cart.SessionTrackingId,
				AccountOperator = cart.AccountOperator,
				AccountRepresentative = cart.AccountRepresentative,
				LocationInfo = cart.LocationInfo,
				Timezone = cart.Timezone,
				TimezoneOffset = cart.TimezoneOffset,
				InternalOrderNumber = cart.InternalOrderNumber,
				Discount = cart.Discount,
				TotalAmount = cart.GetTotalAmount(),
				ShippingCost = cart.GetTotalShippingCost(),
				ShippingAddress = cart.ShippingAddress,
				EstimatedDeliveryDays = cart.EstimatedDeliveryDays,
				RequiresAllChildren = cart.RequiresAllChildren,
				Notifications = cart.Notifications,
				PaymentMethods = cart.PaymentMethods.Select(UserSelectedPaymentMethod.Of).ToList(),
				ChildrenOrders = cart.ChildrenCarts.Select(pCart => new ChildOrder()
				{
					ItemGrouperId = pCart.ItemGrouperId,
					ClientPartnerFee = pCart.ClientPartnerFee,
					TotalAmount = pCart.GetTotalAmount(),
					ShippingCost = pCart.ShippingCost,
                    DetailedShippingCost = pCart.DetailedShippingCost,
					Offline = ProcessTypeHelper.IsOffline(pCart.ProcessType),
					EstimatedDeliveryDays = pCart.EstimatedDeliveryDays,
					Items = pCart.Products?.Select(OrderItem.From).ToList()
				}).ToList()
			};
			// Dados do call center
			if (cart.IsCartFromCallCenter())
			{
				orderModel.CallCenterUserId = cart.CallCenterUserId;
				orderModel.CallCenterName = cart.CallCenterName;
				orderModel.CallCenterEmail = cart.CallCenterEmail;
				orderModel.SessionStart = cart.SessionStartDate;
				orderModel.SessionFinish = cart.SessionEndDate;
			}
			return orderModel;
		}

		public List<UserSelectedPaymentMethodDto> GetPaymentMethods()
		{
			if (PaymentMethods == null)
				return null;

			var paymentMethods = new List<UserSelectedPaymentMethodDto>();
			foreach (var paymentMethod in PaymentMethods)
			{
				paymentMethods.Add(UserSelectedPaymentMethodDto.Of(paymentMethod));
			}

			return paymentMethods;

		}

		public List<ChildCart> GetChildrenCarts()
		{
			if (ChildrenOrders == null)
                return null;
			var childrenCarts = new List<ChildCart>();
			foreach (var childCart in ChildrenOrders)
			{
				if (childCart == null) continue;
				var cartItems = new List<CartItem>();
				if (!childCart.Items.IsNullOrEmpty())
				{
					foreach (var item in childCart.Items)
					{
						if (item == null || string.IsNullOrEmpty(item.ElasticId) || item.SkuId == Guid.Empty)
							continue;
						cartItems.Add(CartItem.From(item));
					}
				}
				childrenCarts.Add(new ChildCart()
				{
					ItemGrouperId = childCart.ItemGrouperId,
					ShippingCost = childCart.ShippingCost,
					Status = childCart.Status,
					OccurredError = childCart.OccurredError,
					ErrorMessage = childCart.ErrorDescription,
					Products = cartItems,
					Vouchers = childCart.Vouchers
				});
			}
			return childrenCarts;
		}

		public void SetCampaignSnapshotSettingsFrom(ParametrizationsModel parametrizations)
		{
			this.CampaignSnapshotSettings = new CampaignSnapshotSettings
			{
				OriginTaxIdForInvoicing = parametrizations.OriginTaxIdForInvoicing,
				EnableAccountRepresentative = parametrizations.EnableAccountRepresentative
			};
		}

		public ChildOrder GetChildCartByPartner(Guid itemGrouperId)
		{
			return ChildrenOrders?.FirstOrDefault(o => o.ItemGrouperId == itemGrouperId);
		}
	}

	// Pedido no Parceiro
	public class ChildOrder
	{
		public Guid Id { get; set; }
		// ID do Parceiro ou Fábrica/Distribuidora
		public Guid ItemGrouperId { get; set; }
		public string ItemGrouperName { get; set; }

		public decimal? ClientPartnerFee { get; set; }
		public string IntegratedOrderNumber { get; set; }

		public bool Offline { get; set; }

		public Amount TotalAmount { get; set; }
		public Amount ShippingCost { get; set; }
        public DetailedShippingCost DetailedShippingCost { get; set; }
		public int? EstimatedDeliveryDays { get; set; }

		public List<OrderItem> Items { get; set; }
		public List<VirtualItem> Vouchers { get; set; }

		// Feed back do pedido
		public PartnerOrderStatus? Status { get; set; }
		public bool CanBeCanceled { get; set; }
		public bool CanBeRefunded { get; set; }

		public bool OccurredError { get; set; }
		public string ErrorDescription { get; set; }

        public OrderItem GetItemBySkuId(Guid skuId)
		{
			return Items.FirstOrDefault(i => i.SkuId == skuId);
		}

		public bool HasAnyService() {
			return Items.Any(i => i.LayoutType == ProductLayoutType.BASIC_SERVICE);
		}

		public bool HasAnyProduct() {
			return Items.Any(i => i.LayoutType == ProductLayoutType.PRODUCT);
		}
	}

	public class OrderItem
	{
		public Guid? FactoryId { get; set; }
		public Guid? PartnerId { get; set; }

		public Guid ProductId { get; set; }
		public Guid SkuId { get; set; }
		public ProductType ProductType { get; set; }
		public ProcessType ProcessType { get; set; }
		public ProductLayoutType LayoutType { get; set; }
		public bool Offline { get; set; }

		public string SkuCode { get; set; }
		public string SkuIntegrationCode { get; set; }
		public string ElasticId { get; set; }
		public string Name { get; set; }
		public string Image { get; set; }

		public string Model { get; set; }
		public string Size { get; set; }
		public string Color { get; set; }
		public string Voltage { get; set; }
		public List<CustomAttribute> CustomAttributes { get; set; }
		public List<Guid> Rankings { get; set; }

		public bool DynamicPrice { get; set; }
		public PriceSettersType? DynamicPricingSetter;
		public decimal? DynamicPriceMinimumValue { get; set; }
		public decimal? DynamicPriceMaximumValue { get; set; }
		public bool Priced { get; set; }
		public int Quantity { get; set; }
		public Amount UnitPrice { get; set; }
		public DetailedPrice DetailedPrice { get; set; }

		public Amount ShippingCost { get; set; }
		public DetailedShippingCost DetailedShippingCost { get; set; }
		public Guid? ShippingModalityId { get; set; }

		public OrderItemStatus Status { get; set; }
		public int? EstimatedDeliveryDays { get; set; }

		public List<VoucherDetails> VouchersDetails { get; set; }

		public string TrackingCode { get; set; }
		public string TrackingLink { get; set; }
		public List<dynamic> TrackingEvents { get; set; }

		public bool CanBeDebted { get; set; }
		public bool CanBeCanceled { get; set; }
		public bool CanBeRefunded { get; set; }
		public bool CanBePriced { get; set; }

		// Feedback do Pedido
		public bool OccurredError { get; set; }
		public string ErrorDescription { get; set; }
		public string InvoiceLinkPdf { get; set; }

		public string ProductName
		{
			get
			{
				return this.Name;
			}
			set
			{
				this.Name = value;
			}
		}

		public decimal GetSubtotalPoints()
		{
			return UnitPrice.GetPoints() * Quantity;
		}

		public static OrderItem From(CartItem item)
		{
			return new OrderItem()
			{
				Offline = item.Offline,
				PartnerId = item.PartnerId,
				FactoryId = item.FactoryId,
				ProductId = item.ProductId,
				SkuId = item.SkuId,
				SkuCode = item.SkuCode,
				SkuIntegrationCode = item.SkuIntegrationCode,
				ElasticId = item.ElasticId,
				ProcessType = item.ProcessType,
				ProductType = item.ProductType,
				LayoutType = item.LayoutType,
				Model = item.SkuModel,
				CustomAttributes = item.CustomAttributes,
				DynamicPrice = item.DynamicPrice,
				DynamicPricingSetter = item.DynamicPricingSetter,
				DynamicPriceMinimumValue = item.DynamicPriceMinimumValue,
				DynamicPriceMaximumValue = item.DynamicPriceMaximumValue,
				Quantity = item.Quantity,
				UnitPrice = item.UnitPrices,
				DetailedPrice = item.DetailedPrice,
				ShippingCost = item.ShippingCost,
				DetailedShippingCost = item.DetailedShippingCost,
				EstimatedDeliveryDays = item.EstimatedDeliveryDays,
				Rankings = item.Rankings,
				InvoiceLinkPdf = item.InvoiceLinkPdf,
			};
		}
	}

	public class VirtualItem
	{
		public DateTime? CreateDate { get; set; }
		public string Name { get; set; }
		public string Link { get; set; }
		public string Status { get; set; }
		public string SkuCode { get; set; }
		public bool Notificated { get; set; }
		public DateTime? NotificationDate { get; set; }
	}

	public class AddressModel
	{
		public Guid Id { get; set; }
		public string Cep { get; set; }
		public bool FilledManually { get; set; }
		public string AddressName { get; set; }
		public string Street { get; set; }
		public string Number { get; set; }
		public string Complement { get; set; }
		public string Neighborhood { get; set; }
		public string City { get; set; }
		public string State { get; set; }
		// public string InitialState { get; set; }
		public string Uf { get; set; }
		public string Reference { get; set; }
		public ReceiverModel Receiver { get; set; }

		public string InitialState
		{
			get
			{
				return this.Uf;
			}
			set
			{
				this.Uf = value;
			}
		}
	}

	public class VoucherDetails
	{
		public string Code { get; set; }
		public DateTime? ExpirationDate { get; set; }
		public string Link { get; set; }
	}

	public class ReceiverModel
	{
		public string Name { get; set; }
		public string Cpf { get; set; }
		public string Email { get; set; }
		public string Telephone { get; set; }
		public string Cellphone { get; set; }
	}

	public class CreatedOrder
	{
		public Guid Id { get; set; }
		public string InternalOrderNumber { get; set; }
	}
}
