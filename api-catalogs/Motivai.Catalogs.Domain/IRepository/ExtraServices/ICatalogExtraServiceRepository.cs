using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices.BillPayments;
using Motivai.Catalogs.Domain.Models.Integrations.MobileRecharge;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;

namespace Motivai.Catalogs.Domain.IRepository.ExtraServices {
    public interface ICatalogExtraServiceRepository {
        Task<List<PartnerOperator>> GetOperatorsForMobilePartner(Guid campaignId, MobilePartner mobilePartner, string ddd);
        Task<List<PartnerOperationCost>> GetPartnerOptionsCostsForDdd(Guid campaignId, MobilePartner mobilePartner, string ddd, string operatorId);

        Task<ExtraServiceOperationResult> StartMobileRecharge(Guid campaignId, Guid userId, MobilePartner mobilePartner, MobileRechargeIssue rechargeIssue);
        Task<ExtraServiceConfirmationResult> ConfirmMobileRecharge(Guid campaignId, Guid userId, MobilePartner mobilePartner, MobileRechargeTicket ticket);
        Task<dynamic> GetRechargeOrderById(Guid campaignId, Guid orderId);

        Task<BillDetails> GetBillDetails(Guid campaignId, BillPaymentPartner partner, string barCode);
        Task<ExtraServiceOperationResult> StartBillPayment(Guid campaignId, Guid userId, BillPaymentPartner partner, BillDetails bill);
        Task<ExtraServiceConfirmationResult> ConfirmBillPayment(Guid campaignId, Guid userId, BillPaymentPartner partner, BillPaymentTicket ticket);
        Task<ScheduledPaymentReceipt> ScheduleBillPayment(Guid campaignId, BillDetails billDetails);
        Task<dynamic> GetBillPaymentsOrderById(Guid campaignId, Guid orderId);
    }
}