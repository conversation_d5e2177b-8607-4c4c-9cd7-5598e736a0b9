using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Enums.Campaigns;

namespace Motivai.Catalogs.Domain.IRepository.ExtraServices {
    public interface ICardsRepository {
        Task<dynamic> CreateCardOrder(dynamic order);
        Task<List<dynamic>> GetParticipantActiveCards(Guid campaignId, Guid userId, PrepaidCardType? cardType);
        Task<CardOrder> GetCardOrderById(Guid orderId);
    }
}