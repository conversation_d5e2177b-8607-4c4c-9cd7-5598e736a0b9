﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Orders;

namespace Motivai.Catalogo.Domain.IRepository
{
	public interface IOrderRepository
	{
		Task<CreatedOrder> CreateOrder(MasterOrder masterOrder);
		Task<MasterOrder> CreateOrderSync(MasterOrder masterOrder);
		Task<MasterOrder> GetOrderById(Guid masterOrderId);
		Task<MasterOrderResumed> FetchOrderResumed(Guid orderId);
		Task<bool?> PriceFactoryItems(FactoryOrderPrice order);
		Task<dynamic> ApproveOrder(Guid orderId);
		Task<dynamic> RefuseOrder(Guid orderId);
		Task<List<dynamic>> ConsultLinkVouchers(Guid orderId, Guid itemGrouperId);
	}
}
