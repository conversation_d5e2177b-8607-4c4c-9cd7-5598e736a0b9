﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Product;

namespace Motivai.Catalogo.Domain.IRepository
{
    public interface ICategoryRepository
    {
        Task<ProductCategoryModel> GetCategory(Guid id);
        Task<List<Guid>> GetDepartmentsWithProducts(Guid campaignId, Guid participantId);
        Task<List<Guid>> GetCategoriesWithProducts(Guid campaignId, Guid participantId);
    }
}