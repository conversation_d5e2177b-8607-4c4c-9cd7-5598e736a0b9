using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.Models.PaymentGateway;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;

namespace Motivai.Catalogs.Domain.IRepository.PaymentGateway
{
    public interface IPaymentGatewayRepository
    {
        Task<GatewayOperationResult> ChargePaymentOrder(PaymentGatewaySettings paymentGateway,
            PointsPurchaseOrder purchaseOrder, string issueDescription, StoreAmount amountSplit, DetailedPaymentInstallment details);
    }
}