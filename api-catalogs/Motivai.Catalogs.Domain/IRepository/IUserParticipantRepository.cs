﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;

namespace Motivai.Catalogo.Domain.IRepository {
    public interface IUserParticipantRepository {
        Task<decimal> GetAvailableBalance(Guid campaignId, Guid userId);
        Task<bool> IsActiveUserInCampaign(Guid userId, Guid campaignId);
        Task<Guid> GetParticipantIdByUserAndCampaign(Guid userId, Guid campaignId);
        Task<Address> GetMainAddress(Guid userId, Guid campaignId);
        Task<Address> GetAddressById(Guid userId, Guid campaignId, Guid shippingAddressId);
        Task<ParticipantContact> GetParticipantContact(Guid userId, Guid campaignId);
        Task<ParticipantInfo> GetParticipantInfo(Guid userId, Guid campaignId);
        Task<AccountRepresentative> GetAccountRepresentative(Guid userId, Guid campaignId);
    }
}