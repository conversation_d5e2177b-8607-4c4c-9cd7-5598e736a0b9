﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Entities.Stocks;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Partners;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Enums.Campaigns;

using Newtonsoft.Json.Linq;

namespace Motivai.Catalogo.Domain.IRepository
{
    public interface ICampaignRepository
    {
        Task<bool> IsActive(Guid campaignId);
        Task<Guid> GetCampaignIdByDomain(string domain);
        Task<Guid> GetTheme(Guid campaignId);
        Task<CoinName> GetCoinName(Guid campaignId);
        Task<JArray> GetMenu(Guid campaignId);
        Task<CampaignSettingsModel> GetSettings(Guid campaignId);
        Task<CampaignCatalogSettings> GetPagesSettings(Guid campaignId);
        Task<CampaignFees> GetFees(Guid campaignId);
        Task<PartnerConfiguration> GetPartnerConfiguration(Guid campaignId, Guid partnerId);
        Task<List<PartnerConfiguration>> GetPartnersConfigurations(Guid campaignId);

        Task<List<Guid>> GetTargetAudiencesByParticipant(Guid campaignId, Guid participantId);
        Task<string> GetSourceShippingCep(Guid campaignId);
        Task<CommunicationModel> GetCommunicationById(Guid campaignId, Guid communicationId);
        Task<List<MediaBox>> GetActiveMediaBoxesForDepartment(Guid campaignId, Guid userId, string departmentId);
        Task<List<MediaBox>> GetActiveMediaBoxesForSitePage(Guid campaignId, Guid userId, string page);
        Task<List<SpecialShopModel>> GetActiveSpecialShopsByCampaign(Guid campaignId);
        Task<SpecialShopModel> GetActiveSpecialShopById(Guid campaignId, Guid specialShopId);
        Task<SpecialShopModel> GetRandomActiveSpecialShopByCampaign(Guid campaignId);

        Task<dynamic> GetActiveRegulation(Guid campaignId, Guid? userId = null);
        Task<dynamic> GetActivePrivacyPolicy(Guid campaignId, Guid? userId = null);
        Task<dynamic> GetActiveShippingPolicy(Guid campaignId, Guid? userId = null);
        Task<List<dynamic>> GetFaqs(Guid campaignId, Guid? userId = null, string term = null);

        #region Fale Conosco

        Task<List<CampaignContactUsSubject>> GetContactSubjects(Guid campaignId);
        Task<bool> SendContactUsFormMessage(Guid campaignId, ContactUsModel model);

        #endregion

        Task<List<PartnerSku>> GetHomeFeaturedProducts(Guid campaignId);
        Task<List<PartnerSku>> GetDepartmentFeaturedProducts(Guid campaignId, Guid departmentId);
        Task<PaymentGatewaySettings> GetActivePaymentGateway(Guid campaignId);
        Task<List<CampaignShipping>> GetActiveShippings(Guid campaignId);

        #region Parceiros Extra Services

        Task<MobilePartnerParametrization> GetActiveMobilePartner(Guid campaignId);
        Task<BillPaymentPartnerParametrization> GetActiveBillPaymentPartner(Guid campaignId, string serviceType = null);
        Task<dynamic> GetBankTransferConfiguration(Guid campaignId);

        #endregion

        Task<List<ProductFixedPrice>> GetProductsFixedPrices(Guid campaignId);
        Task<List<DiscountRules>> GetDiscounts(Guid campaignId);
        Task<DiscountRules> GetDiscountByCouponCode(Guid campaignId, string couponCode);

        Task<List<Guid>> GetUserGroups(Guid campaignId, Guid userId);

        #region Estoque Customizado

        Task<List<CampaignGroupSkuStock>> GetCampaignSkuStocks(Guid campaignId, Guid partnerId, Guid skuId);

        #endregion

        #region catalogs

        Task<CampaignCatalogSettingsFooter> GetCatalogFooterSettingsByCampaign(Guid campaignId);

        #endregion

        #region cartoes

        Task<dynamic> GetCardsPageConfiguration(Guid campaignId, string cardType);
        Task<dynamic> GetCardParametrizations(Guid campaignId);

        Task<dynamic> HasBillPaymentPartnerWithFilters(Guid campaignId,
            BillsPaymentPartnerFilters billsPaymentPartnerFilters);

        #endregion

        #region Formas de Pagamentos

        Task<List<MarketplacePaymentMethodConfiguration>> GetActivePaymentMethods(Guid campaignId);

        #endregion
    }
}