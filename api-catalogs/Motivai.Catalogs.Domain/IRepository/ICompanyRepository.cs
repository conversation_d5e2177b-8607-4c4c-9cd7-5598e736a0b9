﻿using System;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Dtos;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.Catalogo.Domain.IRepository
{
    public interface ICompanyRepository {
        ValueTask<ProcessType> GetProcessTypeByPartner(Guid partnerId);
        Task<ClientConfiguration> GetClientConfiguration(Guid clientId);
        Task<PartnerSettingsDto> GetPartnerSettings(Guid partnerId);
    }
}