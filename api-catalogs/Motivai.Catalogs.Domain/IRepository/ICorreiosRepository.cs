﻿using Motivai.Catalogo.Domain.Models.Correios;
using Motivai.SharedKernel.Domain.Entities.References.Correios;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Motivai.Catalogo.Domain.IRepository
{
    public interface ICorreiosRepository
    {
        Task<CorreiosAddress> QueryCep(string cep);
        Task<List<CorreiosShippingResult>> CalculateShipping(string fromCep, string toCep, decimal weight, decimal hight, decimal width, decimal depth, decimal diameter);
        Task<List<CorreiosShippingResult>> CalculateShipping(string fromCep, string toCep, decimal weight, decimal height, decimal width, decimal depth);
    }
}
