using System;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.Models.Transactions;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.IRepository.Transactions
{
    public interface ITransactionRepository
    {
        Task<decimal> GetBalance(Guid campaignId, Guid userId);
        Task<Guid> CreateCreditTransaction(Guid campaignId, Guid userId, string mechanicDescription, string transactionDescrition,
            PointsPurchaseOrder purchaseOrder);
        Task<Guid> CreateDebitTransaction(Guid campaignId, Guid userId, Guid participantId,
            TransactionOrigin transactionOrigin, Guid originId, string transactionDescrition, Amount amountToDebt);
        Task<TransactionRefundReceipt> RefundTransaction(Guid campaignId, Guid userId, Guid debitTransactionId,
            TransactionOrigin transactionOrigin, Guid originId, string refundReason, Guid? traceId = null);

        Task<TransactionRefundReceipt> RefundTransactionById(Guid campaignId, Guid userId, Guid debitTransactionId, Guid originId, TransactionOrigin origin, string refundReason);
    }
}