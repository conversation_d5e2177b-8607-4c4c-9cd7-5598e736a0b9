using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;

namespace Motivai.Catalogs.Domain.IRepository.Integrations
{
    public interface IOrderIntegrationRepository
    {
        Task CalculateIntegrationCart(string shippingAddressZipcode, ChildCart parterCart);
        Task<ShippingCostResult> CalculateShippingForItem(PartnerConfiguration partnerConf,
            string shippingAddressZipcode, string skuCode, int quantity);
    }
}