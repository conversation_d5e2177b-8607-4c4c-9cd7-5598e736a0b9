﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Products;

namespace Motivai.Catalogo.Domain.IRepository
{
	public interface IProductCatalogRepository
	{
		Task<ElasticsearchProduct> GetProductByElasticId(Guid campaignId, Guid userId, Guid participantId, string elasticId, bool? evenInactive = false);
		Task<List<ElasticsearchProduct>> GetAllProductsBySkuCode(Guid campaignId, Guid userId, Guid participantId, string skuCode, bool takeOnlySameSku = true);
		Task<ProductDetailsModel> GetProductForCalculationByElasticId(Guid campaignId, Guid userId, Guid participantId, string elasticId, string skuCode);
		Task<ElasticsearchProduct> GetProductForCartItemByElasticIdAndSkuCode(Guid campaignId, Guid userId, Guid participantId, string elasticId, string skuCode);
		Task<List<ProductDetailsModel>> GetProductsForCalculationByPartnersAndSkus(Guid campaignId, Guid userId, Guid participantId, List<PartnerSku> partnersSkus);
		Task<List<ProductShowcaseModel>> GetProducts(Guid campaignId, Guid userId, Guid participantId, Guid? departmentId, RegionFilter regionFilter,
			int from, int size, decimal? fromCurrency = null, decimal? toCurrency = null);
		Task<List<ProductShowcaseModel>> GetProductsBySkusIds(Guid campaignId, Guid userId, Guid participantId, List<Guid> skus,
			RegionFilter regionFilter = null, bool evenInactives = false);

		Task<SearchModel> SearchProducts(Guid campaignId, Guid userId, Guid participantId, Guid? departmentId, string query, string[] partners = null,
			string[] departments = null, string[] categories = null, string[] subcategories = null, string[] manufacturers = null,
			string[] colors = null, string[] voltagens = null, string sortBy = null, bool? asc = null, string nestedPath = null, decimal? fromPoint = null, decimal? toPoints = null,
			RegionFilter regionFilter = null, int from = 0, int size = 8, string[] productType = null);
		Task<List<ProductShowcaseModel>> GetProductsByCategory(Guid campaignId, Guid userId, Guid participantId, Guid categoryId, int from = 0, int size = 8);
		Task<List<ProductShowcaseModel>> GetProductsByEan(Guid campaignId, Guid userId, Guid participantId, string ean);
		Task<ProductAttributes> GetSkusAttributes(Guid campaignId, Guid userId, Guid participantId, string elasticId,
			string model = null, string voltagem = null, string color = null, string size = null);
		Task<bool> RegisterAvailableNotification(Guid productId, Guid participantId);
		Task<List<ProductShowcaseModel>> SearchProductsByTermOrSkuCode(Guid campaignId, Guid userId, Guid participantId,
			string skuCode, string name, Guid? factoryId, RegionFilter regionFilter, int from = 0, int size = 8);
		Task<List<ProductShowcaseModel>> GetProductsByPartnersAndSkuCodes(Guid campaignId, Guid userId, Guid participantId,
			List<PartnerSku> partnersSkus, RegionFilter regionFilter, bool evenInactives = false);
	}
}