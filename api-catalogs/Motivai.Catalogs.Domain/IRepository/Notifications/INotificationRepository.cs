using System;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Entities.PointsPurchase;

namespace Motivai.Catalogs.Domain.IRepository.Notifications {
    public interface INotificationRepository {
        Task<bool> NotifyMobileRecharge(Guid campaignId, MobileRechargeTicket ticket);
        Task<bool> NotifyBillPayment(Guid campaignId, BillPaymentTicket ticket);
        Task<bool> SendPurchaseNotification(PointsPurchaseOrder purchaseOrder);
        Task<bool> SendSms(string from, string cellphone, string smsText);
    }
}