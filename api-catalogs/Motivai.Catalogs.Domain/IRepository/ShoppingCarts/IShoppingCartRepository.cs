using System;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.Carts;

namespace Motivai.Catalogs.Domain.IRepository.ShoppingCarts
{
    public interface IShoppingCartRepository
    {
        Task<ShoppingCart> GetParticipantShoppingCart(Guid campaignId, Guid userId);
        Task Save(ShoppingCart shoppingCart);
        Task<int> CountSkuGroupStock(Guid campaignId, Guid participantGroupId, Guid skuId);
        Task<bool> SetCreatingOrder(Guid campaignId, Guid userId);
        Task<bool> SetCartOrdered(Guid campaignId, Guid userId, Guid orderId, string orderNumber);
    }
}