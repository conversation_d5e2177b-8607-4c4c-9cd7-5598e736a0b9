using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
	public class BillPaymentTicket
	{
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public string Assignor { get; set; }
		public string BarCode { get; set; }
		public decimal BillingAmount { get; set; }
		public string ServiceType { get; set; }

		public string Protocol { get; set; }
		///<summary>
		/// Parceiro de pague de contas utilizado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }
		public string ProofPayment { get; set; }
		public Guid CatalogExtraServiceId { get; set; }
		public bool Confirm { get; set; }

		public Amount ParticipantCost { get; set; }

		public void Validate()
		{
			if (UserId == Guid.Empty)
				throw MotivaiException.ofValidation("USER_INVALID", "Usuário inválido.");
			Protocol.ForNullOrEmpty("BILL_PAYMENT_PROTOCOL_INVALID", "Protocolo de pagamento inválido.");
			if (CatalogExtraServiceId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_ID_INVALID", "Identificador da transação inválido.");
			}
			if (ParticipantCost == null)
				throw MotivaiException.ofValidation("BILL_PAYMENT_TOTAL_AMOUNT_INVALID", "Valor total do pagamento inválido.");
		}
	}
}