using System;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
    public class RegisterData
    {
        /// Cpf ou Cnpj do Beneficiário.
        /// </summary>
        /// <returns></returns>
        public string DocumentRecipient { get; set; }

        /// Cpf ou Cnpj do pagador.
        /// </summary>
        /// <returns></returns>
        public string DocumentPayer { get; set; }

        /// <summary>
        /// Define a linha digitável a ser consultada.
        /// </summary>
        /// <returns></returns>
        public string Digitable { get; set; }

        /// Data de baixa do boleto.
        /// </summary>
        /// <returns></returns>
        public DateTime? PayDueDate { get; set; }

        /// Próximo dia útil.
        /// </summary>
        /// <returns></returns>
        public DateTime? NextBusinessDay { get; set; }

        /// Data de vencimento do registro.
        /// </summary>
        /// <returns></returns>
        public DateTime? DueDateRegister { get; set; }

        /// Permissão de alteração do valor do boleto.
        /// </summary>
        /// <returns></returns>
        public bool AllowChangeValue { get; set; }

        /// Nome do beneficiário.
        /// </summary>
        /// <returns></returns>
        public string Recipient { get; set; }

        /// Nome do pagador.
        /// </summary>
        /// <returns></returns>
        public string Payer { get; set; }

        /// Valor do desconto calculado.
        /// </summary>
        /// <returns></returns>
        public double DiscountValue { get; set; }

        /// Valor juros já calculado.
        /// </summary>
        /// <returns></returns>
        public double InterestValueCalculated { get; set; }

        /// Valor máximo permitido para pagamento do título.
        /// </summary>
        /// <returns></returns>
        public double MaxValue { get; set; }

        /// Valor mínimo permitido para pagamento do título.
        /// </summary>
        /// <returns></returns>
        public double MinValue { get; set; }

        /// Valor multa já calculado.
        /// </summary>
        /// <returns></returns>
        public double FineValueCalculated { get; set; }

        /// <summary>
        /// Define o valor nominal do título a ser pago
        /// </summary>
        /// <returns></returns>
        public double OriginalValue { get; set; }

        /// Valor atualizado a ser pago do título.
        /// </summary>
        /// <returns></returns>
        public double TotalUpdated { get; set; }
        /// Valor total de descontos e abatimentos.
        /// </summary>
        /// <returns></returns>
        public double TotalWithDiscount { get; set; }

        /// Valor total de descontos e abatimentos.
        /// </summary>
        /// <returns></returns>
        public double TotalWithAdditional { get; set; }

        public RegisterData(dynamic registerData)
        {
            this.DocumentRecipient = registerData?.documentRecipient;
            this.DocumentPayer = registerData?.documentPayer;
            this.Digitable = registerData?.digitable;
            this.PayDueDate = registerData?.payDueDate;
            this.NextBusinessDay = registerData?.nextBusinessDay;
            this.DueDateRegister = registerData?.dueDateRegister;
            this.AllowChangeValue = registerData?.allowChangeValue ?? false;
            this.Recipient = registerData?.recipient;
            this.Payer = registerData?.payer;
            this.DiscountValue = registerData?.discountValue ?? 0;
            this.InterestValueCalculated = registerData?.interestValueCalculated ?? 0;
            this.MaxValue = registerData?.maxValue ?? 0;
            this.MinValue = registerData?.minValue ?? 0;
            this.FineValueCalculated = registerData?.fineValueCalculated ?? 0;
            this.OriginalValue = registerData?.originalValue ?? 0;
            this.TotalUpdated = registerData?.totalUpdated ?? 0;
            this.TotalWithDiscount = registerData?.totalWithDiscount ?? 0;
            this.TotalWithAdditional = registerData?.totalWithAdditional ?? 0;
        }
    }
}