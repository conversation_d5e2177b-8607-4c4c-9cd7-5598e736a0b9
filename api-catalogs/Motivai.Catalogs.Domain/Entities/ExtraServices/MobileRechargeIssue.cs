using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
	public class MobileRechargeIssue
	{
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }
		public decimal RechargeCost { get; set; }

		public decimal PointsFactor { get; set; }

		///<summary>
		/// Custo da recarga no parceiro.
		///</summary>
		public Amount PartnerCost { get; set; }

		///<summary>
		/// Taxa do Parceiro da Integração em porcentagem (%).
		///</summary>
		public decimal PartnerFee { get; set; }
		///<summary>
		/// Valor taxa do Parceiro da Integração em reais (R$).
		///</summary>
		public Amount PartnerFeeAmount { get; set; }

		///<summary>
		/// Taxa da Motivai em porcentagem (%).
		///</summary>
		public decimal GpFee { get; set; }
		///<summary>
		/// Valor da taxa da Motivai em reais (R$).
		///</summary>
		public Amount GpFeeAmount { get; set; }

		///<summary>
		/// Taxa geral de parceiros do cliente em porcentagem (%).
		///</summary>
		public decimal ClientPartnerFee { get; set; }
		///<summary>
		/// Valor da taxa geral de parceiros do cliente em reais (R$).
		///</summary>
		public Amount ClientPartnerFeeAmount { get; set; }

		///<summary>
		/// Custo para o participante.
		///</summary>
		public Amount ParticipantCost { get; set; }

		public string CellphoneCountryCode { get; set; }
		public string CellphoneDdd { get; set; }
		public string CellphoneNumber { get; set; }

		public string OperatorId { get; set; }
		public string OperatorName { get; set; }

		public void Validate()
		{
			if (string.IsNullOrEmpty(CellphoneDdd))
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_MOBILE_PHONE_DDD_INVALID", "Informe o DDD do celular.");
			if (string.IsNullOrEmpty(CellphoneNumber))
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_MOBILE_PHONE_INVALID", "Informe o número do celular.");
		}

		public string GetCellphone()
		{
			return String.Format("{0} {1}", CellphoneDdd, CellphoneNumber);
		}

		public void CopyFeesFrom(PartnerOperationOption option)
		{
			this.RechargeCost = option.RechargeCost;
			this.PartnerCost = option.PartnerCost;
			this.PartnerFee = option.PartnerFee;
			this.PartnerFeeAmount = option.PartnerFeeAmount;
			this.GpFee = option.GpFee;
			this.GpFeeAmount = option.GpFeeAmount;
			this.ClientPartnerFee = option.ClientPartnerFee;
			this.ClientPartnerFeeAmount = option.ClientPartnerFeeAmount;
			this.ParticipantCost = option.ParticipantCost;
		}
	}
}