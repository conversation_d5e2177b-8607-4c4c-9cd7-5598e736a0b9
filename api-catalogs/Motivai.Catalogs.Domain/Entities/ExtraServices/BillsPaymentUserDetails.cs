using System;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
	public class BillsPaymentUserDetails
	{
		public Guid UserId { get; set; }
		public string UserDocument { get; set; }
		public string UserAccountOperatorDocument { get; set; }
		public Guid? AccountOperatorId { get; set; }
		public Guid? AccountOperatorLoginId { get; set; }

		public static BillsPaymentUserDetails of(Guid userId, string userDocument,
			string userAccountOperatorDocument, Guid? accountOperatorId, Guid? accountOperatorLoginId)
		{
			return new BillsPaymentUserDetails
			{
				UserId = userId,
				UserDocument = userDocument,
				UserAccountOperatorDocument = userAccountOperatorDocument,
				AccountOperatorId = accountOperatorId,
				AccountOperatorLoginId = accountOperatorLoginId
			};
		}
	}
}
