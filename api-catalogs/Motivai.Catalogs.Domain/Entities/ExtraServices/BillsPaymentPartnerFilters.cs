using System;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Partners;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
	public class BillsPaymentPartnerFilters
	{
		public BillPaymentPartner PartnerConsultedBillInfo { get; set; }
		public string Assignor { get; set; }
		public decimal BillingAmount { get; set; }
		public string BarCode { get; set; }
		public BillPaymentServiceType ServiceType { get; set; }
		public string Recipient { get; set; }
		public string RecipientDocument { get; set; }
		public string Payer { get; set; }
		public string PayerDocument { get; set; }

		///<summary>
		/// O BillsPaymentUserDetails não é utilizado no filtro, apenas para registro em caso de erro
		///</summary>
		public BillsPaymentUserDetails UserDetails { get; set; }

		public static BillsPaymentPartnerFilters BuildFrom(BillDetails billDetails, BillPaymentPartner partnerConsultedBillInfo, Guid userId,
			string userDocument, string userAccountOperatorDocument, Guid? accountOperatorId, Guid? accountOperatorLoginId)
		{
			if (billDetails == null)
				return null;

			var billPaymentFilters = new BillsPaymentPartnerFilters();
			billPaymentFilters.PartnerConsultedBillInfo = partnerConsultedBillInfo;

			billPaymentFilters.Assignor = billDetails.Assignor;
			billPaymentFilters.BillingAmount = billDetails.BillingAmount;
			billPaymentFilters.BarCode = billDetails.BarCode;
			billPaymentFilters.ServiceType = billDetails.ServiceType;

			if (billDetails.RegisterData == null)
				return billPaymentFilters;

			billPaymentFilters.Recipient = billDetails.RegisterData.Recipient;
			billPaymentFilters.RecipientDocument = billDetails.RegisterData.DocumentRecipient;

			billPaymentFilters.Payer = billDetails.RegisterData.Payer;
			billPaymentFilters.PayerDocument = billDetails.RegisterData.DocumentPayer;

			billPaymentFilters.UserDetails = BillsPaymentUserDetails.of(userId, userDocument,
				userAccountOperatorDocument, accountOperatorId, accountOperatorLoginId
			);

			return billPaymentFilters;
		}
	}
}