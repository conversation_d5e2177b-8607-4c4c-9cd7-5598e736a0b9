using System;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.References.Security;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
	public class MobileRechargeTicket
	{
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public string CellphoneDdd { get; set; }
		public string CellphoneNumber { get; set; }

		public string Protocol { get; set; }
		public string ProofPayment { get; set; }

		public Guid CatalogExtraServiceId { get; set; }
		public bool Confirm { get; set; }

		public string OperatorName { get; set; }
		public decimal RechargeValue { get; set; }
		public Amount ParticipantCost { get; set; }

		public string GetCellphone()
		{
			return String.Format("{0} {1}", CellphoneDdd, CellphoneNumber);
		}

		public void Validate()
		{
			if (UserId == Guid.Empty || ParticipantId == Guid.Empty)
				throw MotivaiException.ofValidation("USER_INVALID", "Usuário inválido.");
			Protocol.ForNullOrEmpty("MOBILE_RECHARGE_PROTOCOL_INVALID", "Protocolo de recarga inválido.");
			if (CatalogExtraServiceId == Guid.Empty)
			{
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_ID_INVALID", "Identificador da transação inválido.");
			}
			if (ParticipantCost == null)
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_TOTAL_AMOUNT_INVALID", "Valor total do pagamento inválido.");
		}
	}
}