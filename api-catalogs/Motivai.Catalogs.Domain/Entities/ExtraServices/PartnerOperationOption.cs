using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices {
    public class PartnerOperationOption {
        public string Description { get; set; }
        ///<summary>
        /// Custo da recarga no parceiro em R$.
        ///</summary>
        public decimal RechargeCost { get; set; }
        public decimal BonusValue { get; set; }
        public int Validate { get; set; }

        public decimal PointsFactor { get; set; }

        ///<summary>
        /// Custo da recarga no parceiro.
        ///</summary>
        public Amount PartnerCost { get; set; }

        ///<summary>
        /// Taxa do Parceiro da Integração em porcentagem (%).
        ///</summary>
        public decimal PartnerFee { get; set; }
        ///<summary>
        /// Valor taxa do Parceiro da Integração em reais (R$).
        ///</summary>
        public Amount PartnerFeeAmount { get; set; }

        ///<summary>
        /// Taxa da Motivai em porcentagem (%).
        ///</summary>
        public decimal GpFee { get; set; }
        ///<summary>
        /// Valor da taxa da Motivai em reais (R$).
        ///</summary>
        public Amount GpFeeAmount { get; set; }

        ///<summary>
        /// Taxa geral de parceiros do cliente em porcentagem (%).
        ///</summary>
        public decimal ClientPartnerFee { get; set; }
        ///<summary>
        /// Valor da taxa geral de parceiros do cliente em reais (R$).
        ///</summary>
        public Amount ClientPartnerFeeAmount { get; set; }

        ///<summary>
        /// Custo para o participante.
        ///</summary>
        public Amount ParticipantCost { get; set; }
    }
}