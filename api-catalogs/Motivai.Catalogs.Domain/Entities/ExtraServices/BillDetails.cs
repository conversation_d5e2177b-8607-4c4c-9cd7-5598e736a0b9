using System;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Partners;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;

namespace Motivai.Catalogs.Domain.Entities.ExtraServices
{
	public class BillDetails
	{
		///<summary>
		/// Parceiro de pague contas da consulta.
		///</summary>
		public BillPaymentPartner BillDetailsQueryPartner { get; set; }
		///<summary>
		/// Parceiro de pague contas usado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }

		public string BarCode { get; set; }
		public BillPaymentServiceType ServiceType { get; set; }
		public string Assignor { get; set; }

		public RegisterData RegisterData { get; set; }
		public string SettleDate { get; set; }
		public string NextSettle { get; set; }
		public long PartnerCorrelationId { get; set; }

		public decimal BillingAmount { get; set; }
		public DateTime? DueDate { get; set; }

		public bool? FilledManually { get; set; }

		public DateTime? MinimumScheduledPaymentDate { get; set; }
		public DateTime? MaximumScheduledPaymentDate { get; set; }
		public DateTime? ScheduledPaymentDate { get; set; }

		public decimal PointsFactor { get; set; }

		///<summary>
		/// Custo do boleto no parceiro.
		///</summary>
		public Amount PartnerCost { get; set; }

		///<summary>
		/// Taxa do Parceiro da Integração em porcentagem (%).
		///</summary>
		public decimal PartnerFee { get; set; }
		///<summary>
		/// Valor taxa do Parceiro da Integração em reais (R$).
		///</summary>
		public Amount PartnerFeeAmount { get; set; }

		///<summary>
		/// Taxa da Motivai em porcentagem (%).
		///</summary>
		public decimal GpFee { get; set; }
		///<summary>
		/// Valor da taxa da Motivai em reais (R$).
		///</summary>
		public Amount GpFeeAmount { get; set; }

		///<summary>
		/// Taxa geral de parceiros do cliente em porcentagem (%).
		///</summary>
		public decimal ClientPartnerFee { get; set; }
		///<summary>
		/// Valor da taxa geral de parceiros do cliente em reais (R$).
		///</summary>
		public Amount ClientPartnerFeeAmount { get; set; }

		public Amount ParticipantCost { get; set; }

		public Guid? UserId { get; set; }
		public Guid? ParticipantId { get; set; }
		public string ParticipantName { get; set; }
		public string UserDocument { get; set; }

		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public void ValidateToPay()
		{
			if (string.IsNullOrEmpty(BarCode))
				throw MotivaiException.ofValidation("BILL_PAYMENT_BARCODE_INVALID", "Código de barras inválido.");

			if (!UserId.HasValue || UserId.Value == Guid.Empty)
				throw MotivaiException.ofValidation("USER_INVALID", "Usuário inválido.");
			// if (!ParticipantId.HasValue || ParticipantId.Value == Guid.Empty)
			// 	throw MotivaiException.ofValidation("USER_INVALID", "Usuário inválido.");

			if (BillingAmount <= 0)
				throw MotivaiException.ofValidation("BILL_PAYMENT_BILLING_AMOUNT_INVALID", "Valor do boleto precisa ser maior que zero.");

			if (!DueDate.HasValue)
				throw MotivaiException.ofValidation("BILL_PAYMENT_DUE_DATE_INVALID", "Informe a data de vencimento.");
			if (DueDate.Value.AtEndOfDay() < DateTime.UtcNow)
				throw MotivaiException.ofValidation("BILL_PAYMENT_OVERDUE", "Não é permitido o pagamento de conta vencida.");
		}

		public void ValidateToSchedule()
		{
			ValidateToPay();

			if (!ScheduledPaymentDate.HasValue)
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_SCHEDULING_DATE_INVALID",
					"Data de agendamento do pagamento é obrigatória.");
			}
			if (ScheduledPaymentDate.Value.AtEndOfDay() < DateTime.UtcNow)
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_MINIMUM_SCHEDULING_DATE_INVALID",
					"Data de agendamento do pagamento não pode ser menor que a atual.");
			}
			if (ScheduledPaymentDate.Value.DayOfWeek == DayOfWeek.Saturday || ScheduledPaymentDate.Value.DayOfWeek == DayOfWeek.Sunday)
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_SCHEDULING_DATE_INVALID",
					"Não é permitido agendar nos finais de semana.");
			}
		}

		public void ValidateCost() {
			// Validação implementada para barrar os casos de boletos com o valor divergente.
			if (RegisterData != null && RegisterData.OriginalValue < RegisterData.MinValue)
				throw MotivaiException.ofValidation("BILL_PAYMENT_BILLING_AMOUNT_INCOMPATIBLE", "Existe uma divergência no valor do boleto. Procure a Central de Atendimento.");

		}

		public void SetParticipantInfo(ParticipantInfo participantInfo)
		{
			this.ParticipantId = participantInfo.ParticipantId;
			this.UserDocument = participantInfo.GetDocument();
		}

		public bool IsGenericBill()
		{
			return this.ServiceType == BillPaymentServiceType.FICHACOMPENSACAO;
		}

		public bool IsUtilityBill()
		{
			return this.ServiceType == BillPaymentServiceType.CONTACONCESSIONARIA;
		}

		public bool HasRecipient()
		{
			return this.RegisterData != null && !string.IsNullOrEmpty(this.RegisterData.DocumentRecipient) && !string.IsNullOrEmpty(this.RegisterData.Recipient);
		}


		public void ValidateBillDetails(BillPaymentPartnerParametrization servicePartner)
		{
			ValidateCost();

			if (servicePartner.Parametrizations == null)
				return;

			if (IsGenericBill() && servicePartner.Parametrizations.RejectGenericBillsPaymentWithoutReceiverDetails
				|| IsUtilityBill() && servicePartner.Parametrizations.RejectUtilityBillsPaymentWithoutReceiverDetails)
			{
				if (!this.HasRecipient())
				{
					throw MotivaiException.ofValidation("BILL_PAYMENT_WITHOUT_RECEIVER_INFORMATION",
							"Dados do beneficiario são obrigatórios.");
				}
			}


		}

	}

}