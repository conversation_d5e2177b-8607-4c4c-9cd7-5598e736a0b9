using System;

namespace Motivai.Catalogs.Domain.Entities.Stocks {
    public class CampaignGroupSkuStock {
        public Guid PartnerId { get; set; }
        // public Guid ProductId { get; set; }
        public Guid SkuId { get; set; }
        public Guid? ParticipantGroupId { get; set; }

        public int CurrentQuantity { get; set; }

        public bool HasStock() {
            return CurrentQuantity > 0;
        }
    }
}