using System;

namespace Motivai.Catalogs.Domain.Entities.Stocks {
    public class StockAvailability {
        public bool Available { get; set; }

        ///<summary>
        /// Grupo de participante com o estoque.
        /// Guid.Empty quando for geral da campanha.
        ///</summary>
        public Guid? ParticipantGroupId { get; set; }

        public bool IsCampaignStock() {
            return ParticipantGroupId.HasValue && ParticipantGroupId.Value == Guid.Empty;
        }

        public static StockAvailability OfAvailability(bool available) {
            return new StockAvailability() {
                Available = available
            };
        }

        public static StockAvailability OfGroupStockAvailability(bool available, Guid participantGroupId) {
            return new StockAvailability() {
                Available = available,
                ParticipantGroupId = participantGroupId
            };
        }
    }
}