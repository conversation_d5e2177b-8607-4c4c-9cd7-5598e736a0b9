using Motivai.Catalogs.Domain.Models.GeneralSettings;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.Marketplace
{
    public class PlatformFeeDetails
    {
        public Amount RiskAssessmentAnalysisFeeAmount { get; set; }

        public static PlatformFeeDetails Of(AmountConversor conversor, RiskAssessmentSettings riskAssessmentSettings)
        {
            return new PlatformFeeDetails()
            {
                RiskAssessmentAnalysisFeeAmount = conversor.ApplyPointsFactorAndCreateAmount(riskAssessmentSettings.AnalysisFeeAmount)
            };
        }

        public bool HasFee()
        {
            return RiskAssessmentAnalysisFeeAmount.GetPointsOrZero() > 0;
        }
    }
}