using System;

using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.Payments;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Entities.PointsPurchase
{
    public class PointsPurchaseOrder
    {
        public string ReferenceId { get; set; }
        public string OrderNumber { get; set; }

        public Guid CampaignId { get; set; }
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public GatewayCompany GatewayCompany { get; set; }

        ///<summary>
        /// Valor dos pontos da compra em reais
        ///</summary>
        public decimal CurrencyValue { get; set; }
        ///<summary>
        /// Pontos da compra
        ///</summary>
        public decimal PointsToBuy { get; set; }
        ///<summary>
        /// Fator de conversão de pontos utilizado no cálculo das parcelas.
        ///</summary>
        public decimal PointsConversionFactor { get; set; }
        ///<summary>
        /// Valor de taxa adicional da compra
        ///</summary>
        public decimal PercentageAdditionalFee { get; set; }
        ///<summary>
        /// Se o anti-fraude está em uso.
        ///</summary>
        public bool EnableAntiFraud { get; set; }

        public string Name { get; set; }
        public string Email { get; set; }
        public string Cpf { get; set; }
        public string Phone { get; set; }

        public PaymentInstallment Installment { get; set; }
        public Address BillingAddress { get; set; }

        public string CardToken { get; set; }

        public Guid PaymentId { get; set; }

        // Mundipagg
        // public MundipaggIssuedToken SecurityControl { get; set; }

        public void Validate()
        {
            if (UserId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");
            if (string.IsNullOrEmpty(Cpf) || !SharedKernel.Domain.ValuesObject.Cpf.IsCpf(Cpf))
                throw MotivaiException.ofValidation("Preencha o CPF corretamente.");
            if (string.IsNullOrEmpty(Phone))
                throw MotivaiException.ofValidation("Preencha o telefone corretamente.");
            if (string.IsNullOrEmpty(CardToken))
                throw MotivaiException.ofValidation("Preencha corretamente os dados do cartão.");
            if (Installment == null)
                throw MotivaiException.ofValidation("Selecione uma forma de pagamento.");
            if (BillingAddress == null)
                throw MotivaiException.ofValidation("Preencha corretamente os dados do endereço de cobrança.");
            BillingAddress.AddressName = "Cobrança";
            BillingAddress.Reference = "Endereço de cobrança";
            BillingAddress.Validate();

            if (PointsToBuy <= 0)
                throw MotivaiException.ofValidation("Quantidade de pontos da compra inválida.");
            if (Installment.Count <= 0 || Installment.Total <= 0)
                throw MotivaiException.ofValidation("Dados do parcelamento inválidos.");
        }

        public string GetPhoneDdd()
        {
            if (Phone.Length >= 2)
                return Phone.Substring(0, 2);
            return "";
        }

        public string GetPhoneNumber()
        {
            if (Phone.Length > 2)
                return Phone.Substring(2);
            return "";
        }
    }
}