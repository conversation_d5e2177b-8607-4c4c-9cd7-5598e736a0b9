using System;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.Shippings
{
	public class ShippingCostItemCalculation
	{
		public string ElasticsearchId { get; set; }
		public Guid ProductId { get; set; }
		public Guid PartnerId { get; set; }
		public ProcessType ProcessType { get; set; }
		public bool Offline { get; set; }
		public ProductType ProductType { get; set; }

		public string Name { get; set; }
		public Guid DepartmentId { get; set; }
		public Guid CategoryId { get; set; }
		public Guid? SubcategoryId { get; set; }

		public Guid SkuId { get; set; }
		public string SkuCode { get; set; }
		public SkuPrice SkuPrices { get; set; }
		public Shipping SkuShipping { get; set; }
		public Attributes SkuAttributes { get; set; }

		public bool Available { get; set; }
		public bool Calculated { get; set; }

		public Amount UnitPrice { get; set; }
		public Amount ShippingCost { get; set; }
		public DetailedShippingCost DetailedShippingCost { get; set; }
		public int EstimatedDeliveryDays { get; set; }

		public int Quantity { get; set; }
		public string DestinationCep { get; set; }

        public ShippingCostItemCalculation() {
        }

        public ShippingCostItemCalculation(ProductDetailsModel product) {
            ElasticsearchId = product.ElasticsearchId;
            ProductId = product.Id;
            PartnerId = product.PartnerId;
            ProcessType = product.ProcessType;
            Offline = product.Offline;
            ProductType = product.ProductType;
            Name = product.Name;
            SkuId = product.SkuId;
            SkuCode = product.SkuCode;
            SkuShipping = product.Sku.Shipping;
            SkuAttributes = product.Sku.Attributes;
            SkuPrices  = product.Prices;
        }

		public static ShippingCostItemCalculation OfProduct(ProductDetailsModel product, CartItem item, string destinationCep)
		{
			return new ShippingCostItemCalculation(product)
			{
				Quantity = item.Quantity,
                Available = item.Available,
                UnitPrice = item.UnitPrices,
                Calculated = item.Calculated,
				DestinationCep = destinationCep
			};
		}

        public static ShippingCostItemCalculation OfProduct(ProductDetailsModel product, int itemQuantity, string destinationCep)
		{
			return new ShippingCostItemCalculation(product)
			{
                Available = true,
				Quantity = itemQuantity,
				DestinationCep = destinationCep
			};
		}

		public static ShippingCostItemCalculation OfCartItem(Cart cart, CartItem item)
		{
			return new ShippingCostItemCalculation()
			{
				ElasticsearchId = item.ElasticId,
				ProductId = item.ProductId,
				PartnerId = item.GetItemGrouperId(),
				ProcessType = item.ProcessType,
				Offline = item.Offline,
				ProductType = item.ProductType,
				Name = item.ProductName,
				SkuId = item.SkuId,
				SkuCode = item.SkuCode,
				Quantity = item.Quantity,
				DestinationCep = cart.Cep,
				Available = item.Available,
				Calculated = item.Calculated,
				UnitPrice = item.UnitPrices,
				ShippingCost = item.ShippingCost ?? item.DetailedShippingCost.CostPrice,
				DetailedShippingCost = item.DetailedShippingCost,
				EstimatedDeliveryDays = item.EstimatedDeliveryDays ?? 0
			};
		}

		// validar nullpointer exception por regra do parceiro (consulta de disponibilidade e preco por parceiro)
		public decimal GetUnitPriceInCurrency()
		{
			if (Calculated)
				return UnitPrice.GetCurrencyOrZero();
			return SkuPrices.PriceCurrency;
		}
	}
}