using System;
using System.Collections.Generic;

using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.Shippings
{
    public class ShippingCostResult
    {
        // Habilitado o calculo de frete
        public bool Enable { get; set; }
        // Habilitado o frete customizado
        public bool EnableCustom { get; set; }

        public Amount Prices { get; set; }
        public DetailedShippingCost DetailedShippingCost { get; set; }
        public int EstimatedDeliveryDays { get; set; }

        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }

        ///<summary>
        /// Fabricantes que entregam na região do CEP.
        ///</summary>
        public List<ProductFactory> Factories { get; set; }

        public static ShippingCostResult OfError(string errorMessage)
        {
            return new ShippingCostResult()
            {
                OccurredError = true,
                ErrorMessage = errorMessage
            };
        }

        public static ShippingCostResult OfDisable(bool enableCustom = false)
        {
            return new ShippingCostResult()
            {
                OccurredError = false,
                Enable = false,
                EnableCustom = enableCustom,
                Prices = Amount.Zero(),
                DetailedShippingCost = DetailedShippingCost.OfCost(Amount.Zero())
            };
        }

        public static ShippingCostResult OfCalculated(Amount cost, int estimatedDeliveryDays)
        {
            return new ShippingCostResult()
            {
                Enable = true,
                Prices = cost,
                DetailedShippingCost = DetailedShippingCost.OfCost(cost),
                EstimatedDeliveryDays = estimatedDeliveryDays
            };
        }


        public void AddFee(PlatformFeeDetails platformFeeDetails)
        {
            DetailedShippingCost.RiskAssessmentFee = platformFeeDetails.RiskAssessmentAnalysisFeeAmount;
            Prices = DetailedShippingCost.GetShippingCost();
        }
    }
}