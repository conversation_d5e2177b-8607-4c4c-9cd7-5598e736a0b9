using System;

using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;

namespace Motivai.Catalogs.Domain.Entities.Shippings
{
    public class CustomShippingCost
    {
        public ShippingType Type { get; set; }
        public decimal Cost { get; set; }
        public decimal AdditionalValue { get; set; }

        public static CustomShippingCost Of(ShippingType type, decimal cost, decimal additionalValue = 0)
        {
            return new CustomShippingCost()
            {
                Type = type,
                Cost = cost,
                AdditionalValue = additionalValue
            };
        }

        public bool HasCost()
        {
            return Cost > 0 || AdditionalValue > 0;
        }

        public bool IsEmbeddedAndHasCost()
        {
            return Type == ShippingType.Embedded && HasCost();
        }

        public decimal GetTotalCost()
        {
            return Cost + AdditionalValue;
        }

        public DetailedShippingCost ToDetailedShippingCost(AmountConversor conversor)
        {
            var costAmount = conversor.ApplyPointsFactorAndCreateAmount(GetTotalCost());
            var additionalValueAmount = conversor.ApplyPointsFactorAndCreateAmount(AdditionalValue);
            return DetailedShippingCost.Of(null, Type, costAmount, additionalValueAmount);
        }
    }
}