using System;
using System.Collections.Generic;
using System.Linq;

using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    public class ChildCart
    {
        public Guid ItemGrouperId { get; set; }
        public string ItemGrouperName { get; set; }

        // Dados para configuracao
        public string CnpjIntegration { get; set; }
        public string CampaignIdIntegration { get; set; }
        public string PartnerIdentifier { get; set; }

        public ProcessType ProcessType { get; set; }
        public decimal? GpPartnerFee { get; set; }
        public decimal? ClientPartnerFee { get; set; }

        public List<CartItem> Products { get; set; }
        public List<VirtualItem> Vouchers { get; set; }

        // Atributos de Retorno
        public Amount ShippingCost { get; set; }
        public DetailedShippingCost DetailedShippingCost { get; set; }
        public int? EstimatedDeliveryDays { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }

        public PartnerOrderStatus? Status { get; set; }
        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }

        public bool IsOfflinePartnerOrHasAnOfflineItem()
        {
            return ProcessTypeHelper.IsOffline(ProcessType) || Products.Exists(p => p.Offline || p.PartnerSettings.DisableCartCalculation);
        }

        public bool HasAnyVoucher()
        {
            return Products.Exists(p => p.IsVoucher());
        }

        public bool IsOnline()
        {
            return ProcessTypeHelper.IsOnline(ProcessType) && Products.Any(p => p.PartnerSettings.DisableCartCalculation == false);
        }

        public void Validate()
        {
            if (Products == null || !Products.Any())
                throw MotivaiException.ofValidation("Adicione pelo menos um item no carrinho.");
            Products.ForEach(p => p.Validate());
        }

        public decimal GetTotalCurrency()
        {
            return Products.Sum(p => p.GetSubtotalCurrency()) + ShippingCost.GetCurrencyOrZero();
        }

        public decimal GetTotalPoints()
        {
            return Products.Sum(p => p.GetSubtotalPoints()) + ShippingCost.GetPointsOrZero();
        }

        public Amount GetTotalAmount()
        {
            return Products.Select(p => p.GetTotalAmount()).Aggregate(Amount.Of(0, 0), (am, acc) => am + acc);
        }

        public bool HasIntegrationData()
        {
            return !string.IsNullOrEmpty(CnpjIntegration) &&
                !string.IsNullOrEmpty(CampaignIdIntegration) &&
                !string.IsNullOrEmpty(PartnerIdentifier);
        }

        public bool IsOnlinePartnerAndWasCalculated()
        {
            return ItemGrouperId != Guid.Empty && ProcessTypeHelper.IsOnline(ProcessType) && !OccurredError;
        }

        public void SetError(string error)
        {
            this.OccurredError = true;
            this.ErrorMessage = error;
        }

        public void TotalizeShippingCost()
        {
            var shippingCost = Amount.Zero();
            var detailedShippingCost = new DetailedShippingCost();
            Products.ForEach(product =>
            {
                shippingCost += product.ShippingCost;
                if (product.DetailedShippingCost != null)
                {
                    detailedShippingCost.CostPrice += product.DetailedShippingCost.CostPrice;
                    detailedShippingCost.CustomCostPrice += product.DetailedShippingCost.CustomCostPrice;
                    detailedShippingCost.AdditionalValue += product.DetailedShippingCost.AdditionalValue;
                }
            });
            detailedShippingCost.RiskAssessmentFee = Amount.Zero();
            ShippingCost = shippingCost;
            DetailedShippingCost = detailedShippingCost;
            VerifyError();
        }

        public void ApplyPlatformFees(PlatformFeeDetails platformFeeDetails)
        {
            if (platformFeeDetails == null || OccurredError) return;
            // se tiver apenas Vale Virtual não aplica a taxa pois está embutido
            if (!Products.Exists(p => p.ProductType != ProductType.ValeVirtual)) return;

            if (ShippingCost == null || DetailedShippingCost == null)
                TotalizeShippingCost();

            ShippingCost += platformFeeDetails.RiskAssessmentAnalysisFeeAmount;

            var apportionmentRiskAssessmentFee = Amount.Of(
                platformFeeDetails.RiskAssessmentAnalysisFeeAmount.GetCurrency() / Products.Count,
                platformFeeDetails.RiskAssessmentAnalysisFeeAmount.GetPoints() / Products.Count
            );

            Products.ForEach(product => product.ApplyPlatformFees(apportionmentRiskAssessmentFee));
            TotalizeShippingCost();
            DetailedShippingCost.RiskAssessmentFee = platformFeeDetails.RiskAssessmentAnalysisFeeAmount;
        }

        private void VerifyError()
        {
            var productWithError = Products.FirstOrDefault(p => p.OccurredError);
            if (productWithError != null)
            {
                OccurredError = true;
                ErrorMessage = productWithError.ErrorMessage;
            }
        }
    }
}