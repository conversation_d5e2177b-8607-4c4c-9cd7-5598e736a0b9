using System;
using System.Collections.Generic;

using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.Models.Integrations;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;

using Newtonsoft.Json;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    public class CartItem
    {
        public Guid? PartnerId { get; set; }
        public Guid? FactoryId { get; set; }

        public PartnerSettingsModel PartnerSettings { get; set;}

        public Guid ProductId { get; set; }
        public Guid SkuId { get; set; }
        public string ElasticId { get; set; }
        public bool Offline { get; set; }
        public ProcessType ProcessType { get; set; }
        public ProductType ProductType { get; set; }
        public ProductLayoutType LayoutType { get; set; }
        public string SkuCode { get; set; }
        public string SkuIntegrationCode { get; set; }

        public Guid DepartmentId { get; set; }
        public Guid CategoryId { get; set; }
        public Guid? SubcategoryId { get; set; }

        // Dados para Consulta do pedido
        public string PartnerName { get; set; }
        public string ProductName { get; set; }
        public string Image { get; set; }
        public string SkuModel { get; set; }
        public string SkuSize { get; set; }
        public string SkuColor { get; set; }
        public string SkuVoltage { get; set; }
        public List<CustomAttribute> CustomAttributes { get; set; }

        public List<Guid> Rankings { get; set; }

        // Preços
        public bool Available { get; set; }
        [JsonIgnore]
        public bool Calculated { get; set; }
        public Guid? StockParticipantGroupId { get; set; }
        public bool DynamicPrice { get; set; }
		public PriceSettersType DynamicPricingSetter;
		public decimal? DynamicPriceMinimumValue { get; set; }
		public decimal? DynamicPriceMaximumValue { get; set; }
        public bool Priced { get; set; }
        public string DynamicPriceDescription { get; set; }
        public string ProductModelLabel { get; set; }
        public string ProductSizeLabel { get; set; }
        public string ProductVoltageLabel { get; set; }
        public string ProductColorLabel { get; set; }
        public string ProductInformationTabTitle { get; set; }
        public string ProductTechnicalSpecificationsTabTitle { get; set; }

        public Amount UnitPrices { get; set; }
        public DetailedPrice DetailedPrice { get; set; }
        public Amount Discount { get; set; }
        public int Quantity { get; set; }

        public bool PriceUpdate { get; set; }

        // Frete
        public Amount ShippingCost { get; set; }
        public DetailedShippingCost DetailedShippingCost { get; set; }

        public Guid? ShippingModalityId { get; set; }
        public int? EstimatedDeliveryDays { get; set; }
        public string TrackingCode { get; set; }
        public string TrackingLink { get; set; }
        public List<dynamic> TrackingEvents { get; set; }

        public OrderItemStatus? Status { get; set; }
        public bool CanBePriced { get; set; }

        public List<VoucherDetails> VouchersDetails { get; set; }

        // Feedback
        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> Notifications { get; set; }

        public bool EnableCustomPromotionalPrice  { get; set; }
		public CustomPromotionalPrice CustomPromotionalPrice { get; set; }
        public string InvoiceLinkPdf { get; set; }

        public static CartItem From(OrderItem item)
        {
            if (item == null) return null;
            return new CartItem()
            {
                PartnerId = item.PartnerId,
                FactoryId = item.FactoryId,
                ProductId = item.ProductId,
                SkuId = item.SkuId,
                ElasticId = item.ElasticId,
                ProductName = item.Name,
                SkuCode = item.SkuCode,
                Offline = item.Offline,
                ProductType = item.ProductType,
                ProcessType = item.ProcessType,
                LayoutType = item.LayoutType,
                CustomAttributes = item.CustomAttributes,
                Quantity = item.Quantity,
                DynamicPrice = item.DynamicPrice && item.Status == OrderItemStatus.UnderAnalysis,
                Priced = item.Priced,
                UnitPrices = item.UnitPrice,
                ShippingCost = item.ShippingCost,
                DetailedShippingCost = item.DetailedShippingCost,
                EstimatedDeliveryDays = item.EstimatedDeliveryDays,
                TrackingCode = item.TrackingCode,
                TrackingLink = item.TrackingLink,
                TrackingEvents = item.TrackingEvents,
                Status = item.Status,
                CanBePriced = item.CanBePriced,
                OccurredError = item.OccurredError,
                ErrorMessage = item.ErrorDescription,
                InvoiceLinkPdf = item.InvoiceLinkPdf,
            };
        }

        public Guid GetItemGrouperId()
        {
            return PartnerId ?? FactoryId ?? Guid.Empty;
        }

        public bool IsVoucher()
        {
            return ProductType.IsVoucher();
        }

        public Amount GetSalePrice()
        {
            if (DetailedPrice != null)
                return DetailedPrice.GetSalePrice();
            return UnitPrices;
        }

        public decimal GetSubtotalCurrency()
        {
            return Quantity * UnitPrices.GetCurrencyOrZero();
        }

        public decimal GetSubtotalPoints()
        {
            return Quantity * UnitPrices.GetPointsOrZero();
        }

        public Amount GetTotalItems()
        {
            return UnitPrices * Quantity;
        }

        public Amount GetTotalAmount()
        {
            return GetTotalItems() + ShippingCost;
        }

        public void SetError(string message)
        {
            OccurredError = true;
            ErrorMessage = message;
        }

        public void AddNotification(string notification)
        {
            if (string.IsNullOrEmpty(notification)) return;
            if (Notifications == null)
                Notifications = new List<string>();
            Notifications.Add(notification);
        }

        public void Validate()
        {
            if (string.IsNullOrEmpty(ElasticId) || string.IsNullOrEmpty(SkuCode))
                throw MotivaiException.ofValidation("Dados do item inválido.");
            if (Quantity < 1)
                throw MotivaiException.ofValidation("Quantidade inválida.");
        }

        public void SetPrice(SkuPrice price)
        {
            this.DynamicPrice = price.DynamicPrice;
            if (this.DynamicPrice && !price.IsDynamicPriceDefinedByParticipant())
            {
                this.UnitPrices = Amount.Zero();
                return;
            }

            var salePrice = price.HasDiscount() ? price.GetSalePrice() : price.GetCalculatedSalePrice();
            if (this.UnitPrices.GetCurrencyOrZero() > 0 && this.UnitPrices.GetCurrencyOrZero() != salePrice.GetCurrencyOrZero())
            {
                this.PriceUpdate = true;
            }

            this.UnitPrices = salePrice;
            this.DetailedPrice = price.DetailedPrice;
            this.Discount = price.Discount;

            if (price.HasEmbeddedShippingCost())
            {
                DetailedShippingCost = price.DetailedShippingCost;
                DetailedShippingCost.CostPrice = ShippingCost;
            }
        }

        public void SetCalculationResult(CartItemResult itemCalc)
        {
            if (itemCalc.IsError)
            {
                SetError(itemCalc.Error);
                return;
            }

            Available = true;
            Calculated = true;

            if (UnitPrices.GetCurrencyOrZero() != itemCalc.GetUnitPrice() && !itemCalc.IsToKeepUnitCost())
            {
                PriceUpdate = true;
                // Vales não retornam valores
                if (ProductType == ProductType.Produto || itemCalc.GetUnitPrice() > 0)
                {
                    UnitPrices = Amount.OfCurrency(itemCalc.GetUnitPrice());
                }
            }

            if (DetailedPrice?.CostPrice?.GetCurrencyOrZero() != itemCalc.GetUnitPrice() && !itemCalc.IsToKeepUnitCost())
            {
                PriceUpdate = true;
                if (DetailedPrice == null)
                {
                    DetailedPrice = new DetailedPrice();
                }
                DetailedPrice.CostPrice = Amount.OfCurrency(itemCalc.GetUnitPrice());
            }

            ShippingCost = Amount.OfCurrency(itemCalc.TotalShippingCost);
            if (itemCalc.Amount > 0)
            {
                Quantity = itemCalc.Amount;
            }

            if (!string.IsNullOrEmpty(itemCalc.DeliveryForecast))
            {
                EstimatedDeliveryDays = Extractor.ExtractNumberFrom(itemCalc.DeliveryForecast);
                AddNotification(itemCalc.DeliveryForecast);
            }
        }

        public void SetShippingCostResult(ShippingCostResult shippingCostResult)
        {
            LoggerFactory.GetLogger().Info(
                "Calculo de Frete - ID {} - Custo Frete {} - Valor Frete {} - Custom Frete {}",
                ElasticId,
                shippingCostResult.DetailedShippingCost?.CostPrice?.Currency,
                shippingCostResult.Prices.Currency,
                shippingCostResult.DetailedShippingCost?.CustomCostPrice?.Currency
            );


            if (shippingCostResult.OccurredError)
            {
                OccurredError = true;
                ErrorMessage = shippingCostResult.ErrorMessage;
            }
            else
            {
                ShippingCost = shippingCostResult.DetailedShippingCost.GetShippingCost();
                DetailedShippingCost = shippingCostResult.DetailedShippingCost;
                EstimatedDeliveryDays = shippingCostResult.EstimatedDeliveryDays;

                AddNotification(shippingCostResult.ErrorMessage);
            }
        }

        public void ApplyPlatformFees(Amount riskAssessmentFee)
        {
            if (OccurredError) return;
            // NPE no DetailedShippingCost (sem calculo em algumas situações)
            DetailedShippingCost.RiskAssessmentFee += riskAssessmentFee;
            Recalculate();
        }

        public void Recalculate()
        {
            ShippingCost = DetailedShippingCost.GetShippingCost();
        }

        public void ApplyDiscount(Amount discountPoints)
        {
            if (discountPoints == null)
            {
                Discount = Amount.Zero();
                return;
            }
            Discount = discountPoints;
            var discountedUnitPrice = this.UnitPrices - Discount;
            if (discountPoints.GetPointsOrZero() != this.UnitPrices.GetPointsOrZero())
            {
                this.PriceUpdate = true;
                this.UnitPrices = discountedUnitPrice;
            }
        }

        public ItemCalculation ToItemCalculation()
        {
            return new ItemCalculation()
            {
                ElasticsearchId = ElasticId,
                PartnerId = GetItemGrouperId(),
                SkuId = SkuId,
                SkuCode = SkuCode,
                ProcessType = ProcessType,
                DepartmentId = DepartmentId,
                CategoryId = CategoryId,
                SubcategoryId = SubcategoryId,
            };
        }
    }
}