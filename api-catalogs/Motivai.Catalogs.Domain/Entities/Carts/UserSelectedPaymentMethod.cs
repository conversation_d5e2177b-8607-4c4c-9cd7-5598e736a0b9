using System;
using System.Collections.Generic;

using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    public class UserSelectedPaymentMethod
    {
        public Guid PaymentMethodId { get; set;}
        public PaymentType Type { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public List<Metadata> Metadata { get; set; }
        public Amount PaymentAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public PaymentStatus Status { get; set; }

        public static UserSelectedPaymentMethod Of(UserSelectedPaymentMethodDto paymentMethodDto)
        {
            var payment = new UserSelectedPaymentMethod
            {
                PaymentMethodId = Guid.NewGuid(),
                Type = paymentMethodDto.Type,
                Code = paymentMethodDto.Code,
                Name = paymentMethodDto.Name,
                Description = paymentMethodDto.Description,
                PaymentAmount = Amount.OfPoints(paymentMethodDto.PaymentAmount),
                PaidAmount = paymentMethodDto.PaidAmount,
                Status = PaymentStatus.PENDING
            };
            return payment;
        }
    }
}
