using System;

using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    public class UserSelectedPaymentMethodDto
    {
        public Guid PaymentMethodId { get; set;}
        public PaymentType Type { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal PaymentAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public PaymentStatus Status { get; set; }

        public static UserSelectedPaymentMethodDto BuildAsCampaignDebit(decimal pointsAmount)
        {
            var payment = new UserSelectedPaymentMethodDto
            {
                Type = PaymentType.CAMPAIGN_DEBIT,
                Code = "1000",
                Name = "Pagamento débito campanha",
                Description = "Débito campanha",
                PaymentAmount = pointsAmount
            };
            return payment;
        }


        public static UserSelectedPaymentMethodDto Of(UserSelectedPaymentMethod paymentMethod)
        {
            var payment = new UserSelectedPaymentMethodDto
            {
                PaymentMethodId = paymentMethod.PaymentMethodId,
                Type = paymentMethod.Type,
                Code = paymentMethod.Code,
                Name = paymentMethod.Name,
                Description = paymentMethod.Description,
                PaymentAmount = paymentMethod.PaymentAmount.GetCurrency(),
                PaidAmount = paymentMethod.PaidAmount,
                Status = paymentMethod.Status
            };

            return payment;
        }
    }
}