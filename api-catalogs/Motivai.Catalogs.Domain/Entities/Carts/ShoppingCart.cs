using System;
using System.Collections.Generic;
using System.Linq;

using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    public class ShoppingCart
	{
		public Guid Id { get; set; }

		[BsonRepresentation(BsonType.String)]
		public ShoppingCartStatus Status { get; set; }

		public Guid CampaignId { get; set; }
		public Guid UserId { get; set; }
        public Guid? SessionId { get; set; }
		public Guid? SessionCartId { get; set; }

		public DateTime LastAccessDate { get; set; }

		public Guid? OrderId { get; set; }
		public string OrderNumber { get; set; }
		public DateTime? OrderDate { get; set; }

		public List<ShoppingCartItem> Items { get; set; }

		public static ShoppingCart New(Guid campaignId, Guid userId)
		{
			return new ShoppingCart()
			{
				Status = ShoppingCartStatus.OPEN,
				CampaignId = campaignId,
				UserId = userId,
				LastAccessDate = DateTime.UtcNow
			};
		}

		public bool IsExpired()
		{
			return LastAccessDate < DateTime.UtcNow.AddMinutes(-15);
		}

		public void ClearItems()
		{
			if (this.Items == null) return;
			this.Items.Clear();
		}

		public ShoppingCartItem AddItem(ShoppingCartItem item)
		{
			this.ValidateCartItemAddition(item);

			if (Items == null)
			{
				this.Items = new List<ShoppingCartItem>();
			}

			var existingItem = this.Items.Find(i => i.PartnerId == item.PartnerId && i.SkuId == item.SkuId);
			if (existingItem != null)
			{
				existingItem.Quantity += item.Quantity;
				return existingItem;
			}

			item.Id = AlphanumericGenerator.GenerateId16();
			item.AddDate = DateTime.UtcNow;
			this.Items.Add(item);
			return item;
		}

		private void ValidateCartItemAddition(ShoppingCartItem cartItem)
        {
            if (cartItem.PartnerSettings == null || !cartItem.PartnerSettings.CartItemLimitEnabled || this.Items == null || !this.Items.Any())
				return;

			var hasCartItemLimit = cartItem.PartnerSettings.CartItemLimit.HasValue;
			var totalItemsPartner = this.Items.Count(i => i.PartnerId == cartItem.PartnerId);

			if (hasCartItemLimit && totalItemsPartner + 1 > cartItem.PartnerSettings.CartItemLimit)
				throw MotivaiException.ofValidation($"O limite máximo de produtos deste parceiro no seu carrinho é {cartItem.PartnerSettings.CartItemLimit} produtos.");

			var itemQuantity = this.GetItemQuantity(cartItem.PartnerId, cartItem.SkuId);
			if (hasCartItemLimit && itemQuantity + cartItem.Quantity > cartItem.PartnerSettings.CartItemLimit)
				throw MotivaiException.ofValidation($"Você pode adicionar no máximo {cartItem.PartnerSettings.CartItemLimit} deste produto no seu carrinho.");
        }

		private int GetItemQuantity(Guid partnerId, Guid skuId)
		{
            return !this.ExistingItemWith(partnerId, skuId) ? 0
				: this.Items.Find(i => i.PartnerId == partnerId && i.SkuId == skuId).Quantity;
        }

        private bool ExistingItemWith(Guid partnerId, Guid skuId)
		{
            return this.Items != null && this.Items.Find(i => i.PartnerId == partnerId && i.SkuId == skuId) != null;
        }

        public void UpdateWith(Cart cart)
		{
			ClearItems();
			SessionId = cart.SessionId;
			SessionCartId = cart.CartId;
			cart.ChildrenCarts.ForEach(childCart =>
			{
				childCart.Products.ForEach(product =>
				{
					AddItem(ShoppingCartItem.FromItem(product));
				});
			});
		}
	}
}