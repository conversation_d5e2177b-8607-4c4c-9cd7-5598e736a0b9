using System;

using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    public class ShoppingCartItem
    {
        public string Id { get; set; }
        public DateTime AddDate { get; set; }

        public Guid PartnerId { get; set; }
        public string PartnerName { get; set; }
        public PartnerSettingsModel PartnerSettings { get; set;}
        public Guid ProductId { get; set; }
        public Guid SkuId { get; set; }
        public string SkuName { get; set; }
        public string SkuCode { get; set; }
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public DetailedPrice PriceDetails { get; set; }
        public DetailedShippingCost ShippingCostDetails { get; set; }
        public Guid? StockParticipantGroupId { get; set; }
        public bool DynamicPrice { get; set; }
        public PriceSettersType? DynamicPricingSetter { get; set; }
        public decimal? DynamicPriceMinimumValue { get; set; }
        public decimal? DynamicPriceMaximumValue { get; set; }

        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }

        public bool EnableCustomPromotionalPrice  { get; set; }
		public CustomPromotionalPrice CustomPromotionalPrice { get; set; }

        public static ShoppingCartItem FromProduct(ProductDetailsModel product, int quantity = 1)
        {
            return new ShoppingCartItem()
            {
                PartnerId = product.PartnerId,
                PartnerName = product.Partner,
                PartnerSettings = product.PartnerSettings,
                ProductId = product.Id,
                SkuId = product.SkuId,
                SkuName = product.Name,
                SkuCode = product.SkuCode,
                Price = product.Prices.Price,
                PriceDetails = product.Prices.DetailedPrice,
                Quantity = quantity,
                StockParticipantGroupId = product.StockParticipantGroupId,
                DynamicPrice = product.Prices.DynamicPrice,
                DynamicPricingSetter = product.Prices.DynamicPricingSetter,
                DynamicPriceMinimumValue = product.Prices.DynamicPriceMinimumValue,
                DynamicPriceMaximumValue = product.Prices.DynamicPriceMaximumValue,
                EnableCustomPromotionalPrice = product.Prices.EnableCustomPromotionalPrice
            };
        }

        public static ShoppingCartItem FromItem(CartItem product)
        {
            return new ShoppingCartItem()
            {
                PartnerId = product.PartnerId ?? product.FactoryId ?? Guid.Empty,
                PartnerName = product.PartnerName,
                PartnerSettings = product.PartnerSettings,
                ProductId = product.ProductId,
                SkuId = product.SkuId,
                SkuName = product.ProductName,
                SkuCode = product.SkuCode,
                Price = product.GetSalePrice().GetPointsOrZero(),
                PriceDetails = product.DetailedPrice,
                ShippingCostDetails = product.DetailedShippingCost,
                Quantity = product.Quantity,
                StockParticipantGroupId = product.StockParticipantGroupId,
                OccurredError = product.OccurredError,
                ErrorMessage = product.ErrorMessage,
                EnableCustomPromotionalPrice = product.EnableCustomPromotionalPrice

            };
        }
    }
}