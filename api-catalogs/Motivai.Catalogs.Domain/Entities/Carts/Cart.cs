﻿using System;
using System.Collections.Generic;
using System.Linq;

using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Entities.Carts
{
    ///<summary>
    /// Modelo para calcular carrinho e criar pedido.
    /// Os campos com JsonIgnore são usados apenas internamente, sendo assim, não são expostos na API.
    ///</summary>
    public class Cart
    {
        public Guid OrderId { get; set; }
        public CampaignType Type { get; set; }
        public string InternalOrderNumber { get; set; }
        public DateTime? OrderStartTime { get; set; }
        public DateTime? CreationDate { get; set; }
        public int? TimezoneOffset { get; set; }
        public string Timezone { get; set; }

        public Guid CampaignId { get; set; }
        public Guid ClientId { get; set; }
        public decimal CampaignPointsFactor { get; set; }
        public decimal ClientPartnersFee { get; set; }

        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }
        public Guid? SessionId { get; set; }
        public string SessionTrackingId { get; set; }
        public Guid? CartId { get; set; }
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }
        public AccountRepresentative AccountRepresentative { get; set; }

        public string Cep { get; set; }
        public AddressModel ShippingAddress { get; set; }

        public List<UserSelectedPaymentMethodDto> PaymentMethods { get; set; }

        public List<ChildCart> ChildrenCarts { get; set; }
        public bool RequiresAllChildren { get; set; }
        public int? EstimatedDeliveryDays { get; set; }

        public string DiscountCoupon { get; set; }
        public Amount Discount { get; set; }

        // Atributos para pedidos utilizando o Call Center
        public Guid? CallCenterUserId { get; set; }
        public string CallCenterName { get; set; }
        public string CallCenterEmail { get; set; }
        public DateTime? SessionStartDate { get; set; }
        public DateTime? SessionEndDate { get; set; }

        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }
        public string Notification { get; set; }
        public List<string> Notifications { get; set; }
        public string InvoiceTaxId { get; set; }

        public bool IsMarketplaceWithOnlineCart()
        {
            return Type.HasMarketplace() && ChildrenCarts.Exists(pc => ProcessTypeHelper.IsOnline(pc.ProcessType));
        }

        public bool HasAnyChildCartWithError()
        {
            if (ChildrenCarts == null) return false;
            return ChildrenCarts.Exists(c => c.OccurredError);
        }

        public string GetChildCartErrorMessage()
        {
            if (ChildrenCarts == null) return null;
            return ChildrenCarts.Where(c => c.OccurredError)
                .Select(c => c.ErrorMessage)
                .FirstOrDefault();
        }

        public void VerifyErrors()
        {
            if (!OccurredError && HasAnyChildCartWithError())
            {
                OccurredError = true;
                ErrorMessage = GetChildCartErrorMessage();
            }
        }

        public bool IsCartFromCallCenter()
        {
            return CallCenterUserId.HasValue && CallCenterUserId != Guid.Empty;
        }

        public void SetError(string errorMessage)
        {
            this.ErrorMessage = errorMessage;
            this.OccurredError = !string.IsNullOrEmpty(errorMessage);
        }

        public void AddNotification(string message)
        {
            if (this.Notifications == null)
                this.Notifications = new List<string>();
            this.Notifications.Add(message);
        }

        public void Validate()
        {
            if (ChildrenCarts == null || !ChildrenCarts.Any())
                throw MotivaiException.ofValidation("Adicione pelo menos um item no carrinho.");
            ChildrenCarts.ForEach(pc => pc.Validate());
        }

        public void ValidateForOrder()
        {
            Validate();
            if (CampaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha é obrigatória.");
            if (UserId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário é obrigatório.");
            if (CreationDate == null)
                CreationDate = DateTime.UtcNow;

            ValidatePaymentMethods();
            ValidateShippingAddress();
        }

        private void ValidatePaymentMethods()
        {
            if (this.PaymentMethods == null || this.PaymentMethods.Count() == 0)
            {
                this.PaymentMethods = new List<UserSelectedPaymentMethodDto>()
                {
                    UserSelectedPaymentMethodDto.BuildAsCampaignDebit(this.GetTotalAmount().GetPointsOrZero())
                };
            }
        }

        private void ValidateShippingAddress()
        {
            if (ShippingAddress == null)
            {
                return;
            }
            if (string.IsNullOrEmpty(ShippingAddress.Uf) && ShippingAddress.InitialState != null)
            {
                ShippingAddress.Uf = ShippingAddress.InitialState;
            }
            if (string.IsNullOrEmpty(ShippingAddress.Uf) && ShippingAddress.State != null)
            {
                ShippingAddress.Uf = BrazilUfHelper.GetUfByStateName(ShippingAddress.State);
            }
            if (string.IsNullOrEmpty(ShippingAddress.State) && ShippingAddress.Uf != null)
            {
                ShippingAddress.State = BrazilUfHelper.GetStateNameByUf(ShippingAddress.Uf);
            }
            if (string.IsNullOrEmpty(ShippingAddress.Uf))
            {
                throw MotivaiException.ofValidation("Endereço de entrega sem UF, por favor, verifique o cadastro do endereço.");
            }

            if (ShippingAddress.Receiver == null)
            {
                throw MotivaiException.ofValidation("Destinatário é obrigatório.");
            }

            if (string.IsNullOrEmpty(ShippingAddress.Receiver.Name))
            {
                throw MotivaiException.ofValidation("Nome do destinatário é obrigatório.");
            }

            if (string.IsNullOrEmpty(ShippingAddress.Receiver.Cpf))
            {
                throw MotivaiException.ofValidation("CPF do destinatário é obrigatório.");
            }

            if (string.IsNullOrEmpty(ShippingAddress.Receiver.Email))
            {
                throw MotivaiException.ofValidation("E-mail do destinatário é obrigatório.");
            }

            if (string.IsNullOrEmpty(ShippingAddress.Receiver.Cellphone))
            {
                throw MotivaiException.ofValidation("Celular  é obrigatório.");
            }
        }

        public void SetCampaignFees(CampaignFees campaignFees)
        {
            CampaignPointsFactor = campaignFees.PointsConversionFactor;
            ClientPartnersFee = campaignFees.ClientPartnersFee;
            ChildrenCarts.ForEach(pc =>
            {
                pc.GpPartnerFee = campaignFees.GetGpFeeForPartner(pc.ItemGrouperId);
                pc.ClientPartnerFee = campaignFees.GetClientFeeForPartner(pc.ItemGrouperId);
            });
        }

        public decimal GetTotalShippingCostCurrency()
        {
            return ChildrenCarts.Sum(p => p.ShippingCost.GetCurrencyOrZero());
        }

        public decimal GetTotalShippingCostPoints()
        {
            return ChildrenCarts.Sum(p => p.ShippingCost.GetPointsOrZero());
        }

        public Amount GetTotalShippingCost()
        {
            return Amount.Of(GetTotalShippingCostCurrency(), GetTotalShippingCostPoints());
        }

        public decimal GetTotalCurrency()
        {
            return ChildrenCarts.Sum(p => p.GetTotalCurrency());
        }

        public decimal GetTotalPoints()
        {
            return ChildrenCarts.Sum(p => p.GetTotalPoints());
        }

        public Amount GetTotalAmount()
        {
            var totalAmount = ChildrenCarts.Select(p => p.GetTotalAmount()).Aggregate(Amount.Of(0, 0), (am, acc) => am + acc);
            if (HasCouponCode() && Discount != null)
                return totalAmount - Discount;
            return totalAmount;
        }

        public bool HasCouponCode()
        {
            return !string.IsNullOrEmpty(DiscountCoupon);
        }

        public void ApplyDiscount(Amount discountPoints)
        {
            if (discountPoints == null)
            {
                Discount = Amount.Zero();
                return;
            }
            Discount = discountPoints;
        }

        public void ApplyPlatformFees(PlatformFeeDetails platformFeeDetails)
        {
            // TODO: verificar se vai aplicar taxa somente no parceiro online ou em todos
            ChildrenCarts.ForEach(childOrder => childOrder.ApplyPlatformFees(platformFeeDetails));
        }

        public void SetCreatedOrder(CreatedOrder createdOrder)
        {
            if (createdOrder.Id == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Não foi possível processar o pedido, por favor, entre em contato com o atendimento.");
            }
            this.OrderId = createdOrder.Id;
            this.InternalOrderNumber = createdOrder.InternalOrderNumber;
        }

        public void SetCreatedOrder(MasterOrder createdOrder)
        {
            if (createdOrder.Id == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Não foi possível processar o pedido, por favor, entre em contato com o atendimento.");
            }

            this.OrderId = createdOrder.Id;
            this.InternalOrderNumber = createdOrder.InternalOrderNumber;
            this.ChildrenCarts.ForEach(childCart =>
            {
                var processedCart = createdOrder.GetChildCartByPartner(childCart.ItemGrouperId);
                childCart.Status = processedCart.Status;
                childCart.Products.ForEach(product =>
                {
                    var item = processedCart.GetItemBySkuId(product.SkuId);
                    product.Status = item.Status;
                    if (product.IsVoucher())
                    {
                        product.VouchersDetails = item.VouchersDetails;
                    }
                });
            });
        }
    }
}