﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Contacts;

namespace Motivai.Catalogs.Domain.IApp.ContactUs
{
    public interface ICampaignContactApp
    {
        Task<List<dynamic>> GetContactUsSubjects(Guid campaignId);
        Task<bool> SendContactUsFormMessage(Guid campaignId, ContactUsModel contactUsModel);
    }
}
