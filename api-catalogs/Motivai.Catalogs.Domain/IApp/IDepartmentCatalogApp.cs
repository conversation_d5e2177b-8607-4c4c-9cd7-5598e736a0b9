﻿using Motivai.Catalogo.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Models.Catalog;

namespace Motivai.Catalogo.Domain.IApp
{
    public interface IDepartmentCatalogApp
    {
        Task<DepartmentModel> GetDepartmentById(Guid campaignId, Guid departmentId, Guid participantId);
        Task<CategoryModel> GetCategoryById(Guid campaignId, Guid categoryId, Guid participantId);
        Task<List<ProductShowcaseModel>> GetProductsOffersByDepartment(Guid campaignId, Guid userId, Guid departmentId, Guid participantId, decimal? availableBalance = null);
        Task<List<ProductShowcaseModel>> GetDepartmentFeaturedProductsByCampaign(Guid campaignId, Guid departmentId, Guid userId, Guid participantId, decimal? availableBalance = null);
        /*
        Task<List<ProductShowcaseModel>> GetMostRedeemedProducts(Guid campaignId, Guid userId, Guid departmentId);
        Task<List<ProductShowcaseModel>> GetMostSeenProducts(Guid campaignId, Guid userId, Guid departmentId);
        Task<List<ProductShowcaseModel>> GetSaleProductsByDepartment(Guid campaignId, Guid userId, Guid departmentId);
        */
    }
}
