﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.Models;
using Motivai.Catalogs.Domain.Models.Product;

namespace Motivai.Catalogo.Domain.IApp
{
	public interface IProductCatalogApp
	{
		Task<SearchModel> SearchProducts(Guid campaignId, Guid userId, Guid participantId, Guid? departmentId, string query,
			string[] partners = null, string[] departments = null, string[] categories = null, string[] subcategories = null,
			string[] manufacturers = null, string[] colors = null, string[] voltagens = null, string sortBy = null, bool? asc = null, string nestedPath = null, decimal? fromPoint = null,
			decimal? toPoints = null, decimal? availableBalance = null, int from = 0, int size = 8, string[] productType = null);
		Task<ProductDetailsModel> GetProductByElasticId(Guid campaignId, Guid userId, Guid participantId, string elasticId, string skuCode,
			bool? evenInactive = false, bool? searchSimiliarSkuAndEan = false, decimal? availableBalance = null);
		Task<ProductDetailsModel> AddSkuToParticipantCart(Guid campaignId, Guid uid, Guid pid, string elasticId, string skuCode, int quantity = 1, decimal? priceDefinedByParticipant = null);
		Task<List<ProductShowcaseModel>> SearchProductsByTermOrSku(Guid campaignId, Guid? factoryId, Guid userId, Guid? participantId,
			string skuCode, string term, decimal? availableBalance = null, int from = 0, int size = 10);
		Task<List<ProductDetailsModel>> FindProductsBySkuCode(Guid campaignId, Guid userId, Guid participantId, string skuCode);
		Task<List<ProductShowcaseModel>> GetSimilarProducts(Guid campaignId, Guid userId, Guid participantId, Guid categoryLowestLevel, decimal? availableBalance = null);
		Task<List<MarketplaceItem>> GetMarketplacesByEan(Guid campaignId, Guid userId, Guid participantId, string ean, decimal? availableBalance = null);
		Task<ProductAttributes> GetSkusAttributes(Guid campaignId, Guid userId, Guid participantId, string elasticId, string model = null,
			string voltage = null, string color = null, string size = null);
		Task<AvailabilityModel> CheckAvailability(Guid campaignId, Guid userId, Guid participantId, string elasticId, string skuCode, int quantity = 1, decimal? priceDefinedByParticipant = null);
		Task<bool> RegisterAvailableNotification(Guid campaignId, Guid productId, Guid participantId);
	}
}