using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.Catalogs.Domain.IApp.Accounts
{
	public interface IAccountWalletDebiter
	{
		Task<decimal> GetBalance(Guid campaignId, Guid userId);
		Task<Guid> Debit(Guid campaignId, Guid userId, Guid? participantId,
				TransactionOrigin origin, Guid originId, string description, Amount amount);
	}
}