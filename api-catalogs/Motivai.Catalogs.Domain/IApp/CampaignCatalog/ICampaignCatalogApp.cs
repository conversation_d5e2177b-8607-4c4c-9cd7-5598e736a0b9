﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.Catalogo.Domain.IApp.CampaignCatalog {
    public interface ICampaignCatalogApp {
        Task<Guid> GetCampaignIdByDomain(string url);
        Task<CoinName> GetCoinNameByCampaign(Guid campaignId);
        Task<CampaignSettingsModel> GetSettingsByCampaign(Guid campaignId);
        Task<CampaignCatalogSettings> GetPagesSettingsByCampaign(Guid campaignId);

        Task<dynamic> GetRegulationByCampaign(Guid campaignId, Guid? userId = null);
        Task<dynamic> GetPrivacyPolicyByCampaign(Guid campaignId, Guid? userId = null);
        Task<dynamic> GetShippingPolicyByCampaign(Guid campaignId, Guid? userId = null);

        Task<List<dynamic>> GetFaqsByCampaign(Guid campaignId, Guid? userId = null, string term = null);
        Task<List<ProductShowcaseModel>> GetHomeProductsByCampaign(Guid campaignId, Guid userId, Guid participantId, decimal? availableBalance = null, int? size = 0, bool? random = false);
        Task<CampaignCatalogSettingsFooter> GetFooterSettingsByCampaign(Guid campaignId);
    }
}