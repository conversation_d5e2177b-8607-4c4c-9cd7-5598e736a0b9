using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices;

namespace Motivai.Catalogs.Domain.IApp.ExtraServices {
    public interface IMobileRechargeApp {
        Task<List<PartnerOperator>> GetAvailableProvidersForDdd(Guid campaignId, string ddd);
        Task<List<PartnerOperationOption>> GetOperatorOptionsForDdd(Guid campaignId, string ddd, string operatorId);
        Task<ExtraServiceOperationResult> IssueRechargeMobileTicket(Guid campaignId, MobileRechargeIssue rechargeIssue);
        Task<ExtraServiceConfirmationResult> ConfirmRechargeMobile(Guid campaignId, MobileRechargeTicket rechargeTicket);
        Task<dynamic> GetRechargeOrderById(Guid campaignId, Guid orderId);
    }
}
