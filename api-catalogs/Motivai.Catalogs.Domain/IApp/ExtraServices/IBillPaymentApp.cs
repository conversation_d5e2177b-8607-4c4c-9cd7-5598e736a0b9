using System;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices;

namespace Motivai.Catalogs.Domain.IApp.ExtraServices
{
    public interface IBillPaymentApp
    {
        Task<BillDetails> GetBillDetails(Guid campaignId, string barCode, Guid userId, string userDocument,
            string userAccountOperatorDocument, Guid accountOperatorId, Guid accountOperatorLoginId,
            bool skipBalanceValidation = false);
        Task<ExtraServiceOperationResult> IssueBillPaymentTicket(Guid campaignId, BillDetails bill);
        Task<ExtraServiceConfirmationResult> ConfirmBillPaymentTicket(Guid campaignId, BillPaymentTicket ticket);
        Task<dynamic> ScheduleBillPayment(Guid campaignId, BillDetails billDetails);

        Task<dynamic> GetBillPaymentsOrderById(Guid campaignId, Guid orderId);
    }
}
