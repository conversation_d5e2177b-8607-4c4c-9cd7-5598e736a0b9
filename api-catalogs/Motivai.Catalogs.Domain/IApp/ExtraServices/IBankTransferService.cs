using System;
using System.Threading.Tasks;

namespace Motivai.Catalogs.Domain.IApp.ExtraServices
{
	public interface IBankTransferService
	{
		Task<dynamic> GetBankTransferConfiguration(Guid campaignId);
		Task<dynamic> CalculateBankTransferOrderFees(Guid campaignId, dynamic order);
		Task<dynamic> CreateBankTransferOrder(Guid campaignId, dynamic order);
		Task<dynamic> GetBankTransferOrderById(Guid campaignId, Guid orderId);
	}
}