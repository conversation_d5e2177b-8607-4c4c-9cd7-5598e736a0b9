using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Enums.Campaigns;

namespace Motivai.Catalogs.Domain.IApp.ExtraServices {
    public interface ICardsService {
        Task<dynamic> GetCardsConfiguration(Guid campaignId, string cardType);
        Task<dynamic> CalculateCardOrderFees(Guid campaignId, dynamic order);
        Task<dynamic> CreateCardOrder(Guid campaignId, dynamic order);
        Task<List<dynamic>> GetParticipantActiveCards(Guid campaignId, Guid userId, PrepaidCardType? cardType);
        Task<CardOrder> GetCardOrderById(Guid campaignId, Guid orderId);
        Task<dynamic> GetCardParametrizations(Guid campaignId);
    }
}