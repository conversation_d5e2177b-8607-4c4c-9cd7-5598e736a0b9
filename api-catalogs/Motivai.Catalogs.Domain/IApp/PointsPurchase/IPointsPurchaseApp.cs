﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Model.Structures;

namespace Motivai.Catalogs.Domain.IApp.PointsPurchase
{
    public interface IPointsPurchaseApp
    {
        Task<decimal> CalculatePurchaseCostFor(Guid campaignId, decimal pointsNeeded);
        Task<PaymentOptions> GetPaymentOptionsForPoints(Guid campaignId, decimal pointsNeeded);
        Task<bool> IssuePointsPurchasePayment(Guid campaignId, PointsPurchaseOrder issue);
        Task<List<Entry<decimal, decimal>>> GetPointsOptionsToBuy(Guid campaignId);
    }
}