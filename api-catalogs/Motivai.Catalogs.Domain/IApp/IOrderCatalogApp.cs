﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Models.Orders;

namespace Motivai.Catalogo.Domain.IApp
{
	public interface IOrderCatalogApp
	{
		Task<Cart> CreateOrder(Cart cart);
		Task<Cart> CreateOrderSync(Cart cart);
		Task<Cart> GetOrderById(Guid orderId, Guid campaignId, Guid participantId);
		Task<MasterOrderResumed> FetchOrderResumed(Guid orderId);
		Task<bool?> PriceFactoryItems(FactoryOrderPrice order);
		Task<dynamic> ApproveOrder(Guid campaignId, Guid orderId);
		Task<dynamic> RefuseOrder(Guid campaignId, Guid orderId);
		Task<List<dynamic>> ConsultLinkVouchers(Guid campaignId, Guid orderId, Guid itemGrouperId);
	}
}
