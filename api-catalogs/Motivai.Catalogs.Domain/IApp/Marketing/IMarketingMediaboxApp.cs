using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Campaign;

namespace Motivai.Catalogs.Domain.IApp.Marketing
{
	public interface IMarketingMediaboxApp
	{
		Task<List<MediaBox>> GetCatalogMediaboxesForHome(Guid campaignId, Guid userId, Guid participantId);
		Task<List<MediaBox>> GetCatalogMediaboxesForDepartment(Guid campaignId, Guid userId, Guid participantId, string departamentId);
		Task<List<MediaBox>> GetActiveMediaBoxes(Guid campaignId, Guid userId, Guid participantId, bool catalog, bool site, string departmentId, string sitePages);
	}
}