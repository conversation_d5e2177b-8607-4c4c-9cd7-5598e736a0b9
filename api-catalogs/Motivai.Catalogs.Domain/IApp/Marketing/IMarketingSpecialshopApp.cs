using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.SharedKernel.Domain.Enums.Campaigns;

namespace Motivai.Catalogs.Domain.IApp.Marketing
{
	public interface IMarketingSpecialshopApp
	{
		Task<List<SpecialShopModel>> GetActiveSpecialShopsByCampaign(Guid campaignId, Guid participantId, CampaignParametrizationOrigin? origin);
		Task<SpecialShopModel> GetActiveSpecialShopById(Guid campaignId, Guid specialShopId, Guid userId, Guid participantId, CampaignParametrizationOrigin origin, bool? shouldConsultSkuAvailaibilityAtPartner);
		Task<SpecialShopModel> GetRandomActiveSpecialShopByCampaign(Guid campaignId, Guid participantId);
	}
}