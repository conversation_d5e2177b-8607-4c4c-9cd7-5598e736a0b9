using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Campaign;
using Newtonsoft.Json.Linq;

namespace Motivai.Catalogs.Domain.IApp.Marketing
{
	public interface IMarketingApp
	{
		Task<JArray> GetCampaignMenu(Guid campaignId);
		Task<Guid> GetThemeFolderByCampaign(Guid campaignId);
		Task<CampaignCustomization> GetCampaignCustomization(Guid campaignId);
		Task<MediaBox> GetCampaignCommunicationById(Guid campaignId, Guid communicationId, Guid participantId);
	}
}