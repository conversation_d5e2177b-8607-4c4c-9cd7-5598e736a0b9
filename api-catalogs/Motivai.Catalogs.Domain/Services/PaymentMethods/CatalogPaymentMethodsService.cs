using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.Catalogs.Domain.Services.PaymentMethods
{
    public class CatalogPaymentMethodsService
    {
        private readonly ICampaignRepository _campaignRepository;

        public CatalogPaymentMethodsService(ICampaignRepository campaignRepository)
        {
            _campaignRepository = campaignRepository;
        }

        public async Task<IEnumerable<CatalogPaymentMethod>> GetPaymentMethodsForCart(Guid campaignId, Cart cart)
        {
            this.ValidateCampaign(campaignId);
            if (cart == null)
                throw MotivaiException.ofValidation("Carrinho inválido");
            cart.Validate();

            var campaignPaymentMethods = await _campaignRepository.GetActivePaymentMethods(campaignId);

            if (campaignPaymentMethods.IsNullOrEmpty())
            {
                return null;
            }

            var items = cart.ChildrenCarts.SelectMany(childCart => childCart.Products)
                    .Select(product => product.ToItemCalculation())
                    .ToList();
            var totalAmount = cart.GetTotalCurrency();

            return campaignPaymentMethods.Where(paymentMethod => paymentMethod.Active)
                    .Select(paymentMethod =>
                        CatalogPaymentMethod.From(paymentMethod, paymentMethod.IsEligibleFor(items, totalAmount))
                    );
        }

        public async Task<IEnumerable<CatalogPaymentMethod>> GetPaymentMethodsForProduct(Guid campaignId, ResumedProduct resumedProduct)
        {
            this.ValidateCampaign(campaignId);

            var campaignPaymentMethods = await _campaignRepository.GetActivePaymentMethods(campaignId);

            if (campaignPaymentMethods.IsNullOrEmpty())
            {
                return null;
            }

            return campaignPaymentMethods.Where(paymentMethod => paymentMethod.Active)
                    .Select(paymentMethod =>
                        CatalogPaymentMethod.From(paymentMethod, paymentMethod.IsProdutcEligibleFor(new ItemCalculation()
                        {
                            ElasticsearchId = resumedProduct.ElasticsearchId,
                            PartnerId = resumedProduct.PartnerId,
                            SkuId = resumedProduct.SkuId,
                            SkuCode = resumedProduct.SkuCode,
                            ProcessType = resumedProduct.ProcessType,
                            DepartmentId = resumedProduct.DepartmentId,
                            CategoryId = resumedProduct.CategoryId,
                            SubcategoryId = resumedProduct.SubcategoryId,
                        }, resumedProduct.TotalAmount))
                    );
        }

        private void ValidateCampaign(Guid campaignId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
        }
    }
}