using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Validators;

namespace Motivai.Catalogs.Domain.Services.Shippings
{
    ///<summary>
    /// Classe responsável pelo cálculo de frete.
    /// Regras:
    /// - Produto (Online e Offline) com Frete Fixo:
    ///   - É utilizado o valor fixo setado no produto.
    /// - Produto Offline:
    ///   - Modalidade Importada: tabela de frete importada
    ///   - Modalidade Online: correios
    /// - Produto Online:
    ///   - Parceiro Online: feito o cálculo no parceiro.
    ///   - Parceiro Offline: feito o cálculo usando o frete importado ou o correios (se habilitado).
    ///
    /// Se a Campanha tiver entrega personalizada, então é somado a quantidade de dias setado.
    ///</summary>
    public class CatalogShippingService
    {
        private readonly ICompanyRepository _companyRepository;
        private readonly ICampaignRepository _campaignRepository;
        private readonly IOrderIntegrationRepository _orderIntegrationRepository;
        private readonly ICorreiosRepository _correiosRepository;

        private readonly bool _enablePartnerIntegration;

        public CatalogShippingService(ICompanyRepository companyRepository, ICampaignRepository campaignRepository,
            IOrderIntegrationRepository orderIntegrationRepository, ICorreiosRepository correiosRepository)
        {
            this._companyRepository = companyRepository;
            this._campaignRepository = campaignRepository;
            this._orderIntegrationRepository = orderIntegrationRepository;
            this._correiosRepository = correiosRepository;

            this._enablePartnerIntegration = ConfigurationHelper.GetValue("EnablePartnerIntegration") == "1";
        }

        public ShippingCostResult SetFixedCost(Guid campaignId)
        {
            LoggerFactory.GetLogger().Warn("Cmp {} - Integração com parceiro está desabilitada.", campaignId);

            return new ShippingCostResult()
            {
                Enable = true,
                Prices = Amount.Zero(),
                DetailedShippingCost = DetailedShippingCost.OfCost(Amount.Zero()),
                EstimatedDeliveryDays = 10
            };
        }

        ///<summary>
        /// Calcula o valor do frete para o produto recebido.
        /// - Produto com frete fixo
        /// - Produto online
        /// - Produto com modalidade de frete (default Correios ou outra selecionada)
        ///</summary>
        public Task<ShippingCostResult> CalculateShippingForItem(Guid campaignId, ProductDetailsModel product, CartItem item, string destinationCep)
        {
            return CalculateShippingForItem(campaignId, ShippingCostItemCalculation.OfProduct(product, item, destinationCep));
        }

        public Task<ShippingCostResult> CalculateShippingForItem(Guid campaignId, ProductDetailsModel product, int itemQuantity, string destinationCep)
        {
            return CalculateShippingForItem(campaignId, ShippingCostItemCalculation.OfProduct(product, itemQuantity, destinationCep));
        }

        public async Task<ShippingCostResult> CalculateShippingForItem(Guid campaignId, ShippingCostItemCalculation item)
        {
            if (item == null) return null;

            var campaignSettings = await _campaignRepository.GetSettings(campaignId);

            // Se estiver desabilitado o cálculo do frete e o frete embutido no produto
            if (!campaignSettings.Parametrizations.EnableAnyShippingCalculation())
            {
                return ShippingCostResult.OfDisable();
            }

            var campaignFees = await _campaignRepository.GetFees(campaignId);
            var conversor = AmountConversor.WithPointsConversionFactor(campaignFees.PointsConversionFactor);

            ShippingCostResult shippingResult;
            if (campaignSettings.Parametrizations.EnableShippingCalculation)
            {
                shippingResult = await CalculateShippingCostByProcessType(campaignId, conversor, item);
                if (shippingResult.OccurredError)
                    return shippingResult;
            }
            else
            {
                shippingResult = ShippingCostResult.OfDisable();
            }

            CustomShippingCost customShipping = null;
            var customShippingAmount = Amount.Zero();
            var additionalValue = Amount.Zero();

            // Verifica se está habilitado o frete customizado
            if (campaignSettings.Parametrizations.EnableEmbeddedShippingCost)
            {
                customShipping = await CalculateCustomShippingCost(campaignId, item);
                // Apenas exibe os fretes que não são embutidos no produto
                if (customShipping != null)
                {
                    customShippingAmount = conversor.ApplyPointsFactorAndCreateAmount(customShipping.Cost);
                    additionalValue = conversor.ApplyPointsFactorAndCreateAmount(customShipping.AdditionalValue);
                }

                // Se o cálculo estiver desabilitado entã retorna apenas o frete embutido no produto
                if (!campaignSettings.Parametrizations.EnableShippingCalculation)
                {
                    if (customShipping == null)
                    {
                        return ShippingCostResult.OfDisable(true);
                    }
                    else
                    {
                        // Valor padrão para cálculo desabilitado
                        var detailedShippingCost = DetailedShippingCost.Of(Amount.Zero(), customShipping.Type, customShippingAmount);
                        return new ShippingCostResult()
                        {
                            Enable = false,
                            EnableCustom = true,
                            Prices = detailedShippingCost.GetShippingCost(),
                            DetailedShippingCost = detailedShippingCost
                        };
                    }
                }
            }

            shippingResult.Enable = true;
            shippingResult.EnableCustom = campaignSettings.Parametrizations.EnableEmbeddedShippingCost;

            if (campaignSettings.Parametrizations.HasMinimumShippingTime())
            {
                if (shippingResult.EstimatedDeliveryDays < campaignSettings.Parametrizations.MinimumShippingTime.Value)
                {
                    shippingResult.EstimatedDeliveryDays = campaignSettings.Parametrizations.MinimumShippingTime.Value;
                }
            }
            LoggerFactory.GetLogger().Info("Cmp {} - ID {} - Tipo {} - Frete {} - Custom {}",
                campaignId, item.ElasticsearchId, customShipping?.Type,
                shippingResult.Prices.Points, customShippingAmount.Points);

            shippingResult.DetailedShippingCost = DetailedShippingCost.OfCost(shippingResult.Prices);
            if (customShipping != null)
            {
                shippingResult.DetailedShippingCost.AddCustomCost(customShipping.Type, customShippingAmount, additionalValue);
            }
            shippingResult.Prices = shippingResult.DetailedShippingCost.GetShippingCost();
            return shippingResult;
        }

        private async Task<ShippingCostResult> CalculateShippingCostByProcessType(Guid campaignId, AmountConversor conversor,
            ShippingCostItemCalculation item)
        {
            // Se tiver frete fixo então ignora a Integração ou cálculo
            if (item.SkuShipping != null && item.SkuShipping.HasFixedShippingCost())
            {
                return await CalculateItemSkuFixedShippingCost(campaignId, item);
            }

            var partnerProcessType = await _companyRepository.GetProcessTypeByPartner(item.PartnerId);
            item.ProcessType = partnerProcessType;

            // Se for produto de Integração e estiver a integração estiver desabilitada
            if ((ProcessTypeHelper.IsOnline(partnerProcessType) || ProcessTypeHelper.IsOnline(item.ProcessType) || !item.Offline) && !this._enablePartnerIntegration)
            {
                return SetFixedCost(campaignId);
            }

            var partnerSettings = await this._companyRepository.GetPartnerSettings(item.PartnerId);

            ShippingCostResult shippingResult;
            if (ProcessTypeHelper.IsOnline(partnerProcessType))
            {
                if (partnerSettings.DisableCartCalculation)
                {
                    shippingResult = ShippingCostResult.OfCalculated(Amount.Zero(), 0);
                }
                else
                {
                    shippingResult = await CalculateShippingCostUsingIntegration(campaignId, item);
                }
            }
            else
            {
                // Cálculo offline (Frete fixo, Frete importado ou Correios)
                shippingResult = await CalculateOfflineShippingCost(campaignId, item);
            }

            if (shippingResult == null)
                throw MotivaiException.ofValidation($"Não foi possível efetuar o cálculo do frete para o produto '{item.Name}'.");

            if (!shippingResult.OccurredError && shippingResult.Prices != null)
            {
                shippingResult.Prices.Points = conversor.ApplyPointsFactor(shippingResult.Prices.GetCurrencyOrZero());
            }

            return shippingResult;
        }

        private async Task<ShippingCostResult> CalculateItemSkuFixedShippingCost(Guid campaignId, ShippingCostItemCalculation item)
        {
            var shippingCost = await CalculateFixedShippingCost(campaignId, item.SkuShipping, item.Quantity);
            // tratamento de vale sem informação do prazo de entrega na integração
            if (shippingCost.EstimatedDeliveryDays <= 0)
            {
                switch (item.ProductType)
                {
                    case ProductType.ValeVirtual:
                        shippingCost.EstimatedDeliveryDays = 7;
                        break;
                    case ProductType.ValeFisico:
                        shippingCost.EstimatedDeliveryDays = 30;
                        break;
                }
            }
            return shippingCost;
        }

        ///<summary>
        /// Calcula o valor do frete para um produto que tenha Frete Fixo.
        ///</summary>
        private async Task<ShippingCostResult> CalculateFixedShippingCost(Guid campaignId, Shipping skuShipping, int itemQuantity)
        {
            skuShipping.ForNull("Configuração de frete fixo do SKU inválida.");
            if (!skuShipping.HasFixedShippingCost())
                throw MotivaiException.ofValidation("SKU não tem o valor de frete fixo.");

            decimal? shippingCostCurrency = null;
            decimal shippingCostPoints;
            decimal additionalCostCurrency = 0;
            decimal additionalCostPoints = 0;

            // Efetua a conversão de pontos se precisar
            if (skuShipping.IsShippingCostInCurrency())
            {
                var campaignFees = await _campaignRepository.GetFees(campaignId);
                var conversor = AmountConversor.WithPointsConversionFactor(campaignFees.PointsConversionFactor);

                shippingCostCurrency = skuShipping.CostCurrency;
                shippingCostPoints = conversor.ApplyPointsFactor(shippingCostCurrency.Value);

                // Valor adicional por item
                if (skuShipping.HasAdditionalCostPerItem())
                {
                    additionalCostCurrency = skuShipping.AdditionalCostPerItem.Value;
                    additionalCostPoints = conversor.ApplyPointsFactor(skuShipping.AdditionalCostPerItem.Value);
                }
            }
            else
            {
                shippingCostPoints = skuShipping.CostPoints.Value;
                // Valor adicional por item
                if (skuShipping.HasAdditionalCostPerItem())
                {
                    additionalCostPoints = skuShipping.AdditionalCostPerItem.Value;
                }
            }

            if (itemQuantity > 0)
            {
                shippingCostCurrency = shippingCostCurrency * itemQuantity;
                shippingCostPoints = shippingCostPoints * itemQuantity;
            }

            LoggerFactory.GetLogger().Info("Cmp {0} - Frete Fixo - Custo {1} - Qtde {2} - Frete {3}",
                campaignId, skuShipping.CostCurrency, itemQuantity, shippingCostCurrency);

            // Calcula o total adicional os itens
            if (skuShipping.HasAdditionalCostPerItem() && itemQuantity > 1)
            {
                int additionalItens = itemQuantity - 1;
                additionalCostCurrency = additionalCostCurrency * additionalItens;
                additionalCostPoints = additionalCostPoints * additionalItens;
            }

            return new ShippingCostResult()
            {
                Prices = Amount.Of(shippingCostCurrency + additionalCostCurrency, shippingCostPoints + additionalCostPoints),
                EstimatedDeliveryDays = skuShipping.EstimatedDeliveryDays
            };
        }

        private async Task<ShippingCostResult> CalculateShippingCostUsingIntegration(Guid campaignId, ShippingCostItemCalculation item)
        {
            if (!this._enablePartnerIntegration)
            {
                return SetFixedCost(campaignId);
            }
            if (item.Calculated)
            {
                return new ShippingCostResult()
                {
                    Enable = true,
                    Prices = item.ShippingCost,
                    EstimatedDeliveryDays = item.EstimatedDeliveryDays,
                    DetailedShippingCost = item.DetailedShippingCost
                };
            }

            // Se o produto for online, verifica o processo do parceiro
            var partnerProcessType = await _companyRepository.GetProcessTypeByPartner(item.PartnerId);
            if (!ProcessTypeHelper.IsOnline(partnerProcessType))
            {
                throw MotivaiException.of(ErrorType.Business, "O parceiro não está configurado corretamente.");
            }

            var partnerConf = await _campaignRepository.GetPartnerConfiguration(campaignId, item.PartnerId);
            try
            {
                return await _orderIntegrationRepository.CalculateShippingForItem(partnerConf, item.DestinationCep, item.SkuCode, item.Quantity);
            }
            catch (MotivaiException ex)
            {
                await ExceptionLogger.LogException(ex, "CatalogShippingService - Online Shipping", "Erro ao calcular o frete do item via integração");
                if (ex.ErrorType == ErrorType.Business || ex.ErrorType == ErrorType.Validation)
                    return ShippingCostResult.OfError(ex.Message);
                return ShippingCostResult.OfError("Não foi possível efetuar o cálculo do frete.");
            }
        }

        ///<summary>
        /// Calcula o valor do frete para um produto utilizando a modalidade configurada no Produto ou no Parceiro do produto.
        ///</summary>
        protected async Task<ShippingCostResult> CalculateOfflineShippingCost(Guid campaignId, ShippingCostItemCalculation item)
        {
            if (item.ProductType == ProductType.ValeVirtual)
            {
                return ShippingCostResult.OfDisable();
            }

            // Se não tiver uma modalidade setada então cálcula com o Correios
            return await CalculateShippingUsingCorreios(campaignId, item);
        }

        protected async Task<ShippingCostResult> CalculateShippingUsingCorreios(Guid campaignId, ShippingCostItemCalculation item)
        {
            var sourceCep = await _campaignRepository.GetSourceShippingCep(campaignId);
            if (string.IsNullOrEmpty(sourceCep))
                throw MotivaiException.of(ErrorType.Business, "CEP de origem não configurado na campanha.");

            var skuAttributes = item.SkuAttributes;

            ValidateSkuAttributesForShipping(skuAttributes);

            decimal totalWeigth = skuAttributes.Weigth.Value * item.Quantity;
            decimal totalHeight = skuAttributes.Height.Value * item.Quantity;
            var correiosShippignResultList = await _correiosRepository.CalculateShipping(sourceCep, item.DestinationCep,
                totalWeigth, totalHeight, skuAttributes.Width.Value, skuAttributes.Depth.Value);

            if (correiosShippignResultList == null || !correiosShippignResultList.Any())
                return ShippingCostResult.OfError("Não foi possível calcular o frete do produto.");

            var firstResult = correiosShippignResultList.FirstOrDefault();
            if (firstResult.OccurredError)
            {
                return ShippingCostResult.OfError(firstResult.ErrorMessage);
            }
            int.TryParse(firstResult.EstimatedDeliveryDays, out int estimatedDeliveryDays);
            return new ShippingCostResult()
            {
                Prices = Amount.OfCurrency(firstResult.Cost),
                EstimatedDeliveryDays = estimatedDeliveryDays,
                ErrorMessage = firstResult.ErrorMessage
            };
        }

        private void ValidateSkuAttributesForShipping(Attributes skuAttributes)
        {
            if (skuAttributes == null)
                throw MotivaiException.ofValidation("Atributos do SKU não definido.");
            if (!skuAttributes.Weigth.HasValue)
                skuAttributes.Weigth = 250;
                // throw MotivaiException.ofValidation("SKU não tem o peso definido.");
            if (!skuAttributes.Height.HasValue)
                skuAttributes.Height = 10;
                // throw MotivaiException.ofValidation("SKU não tem a altura definida.");
            if (!skuAttributes.Width.HasValue)
                skuAttributes.Width = 10;
                // throw MotivaiException.ofValidation("SKU não tem a largura definida.");
            if (!skuAttributes.Depth.HasValue)
                skuAttributes.Depth = 10;
                // throw MotivaiException.ofValidation("SKU não tem a profundidade definida.");
        }

        private async Task<CustomShippingCost> CalculateCustomShippingCost(Guid campaignId, ShippingCostItemCalculation item)
        {
            if (item == null || !item.Available)
                return null;

            if (item.Quantity <= 0) item.Quantity = 1;

            LoggerFactory.GetLogger().Info("Buscando frete custom - Cmp {} - ID {} - Sku {}",
            		campaignId, item.ElasticsearchId, item.SkuCode);
            var campaignShippings = await _campaignRepository.GetActiveShippings(campaignId);
            if (campaignShippings == null || campaignShippings.Count == 0)
                return null;
            var shipping = campaignShippings.FirstOrDefault(s => s.CanApply(item));
            return CalculateCustomShippingCost(shipping, item.GetUnitPriceInCurrency(), item.Quantity);
        }

        ///<summary>
        /// Verifica se a campanha tem frete customizado calculado.
        /// Se tiver algum frete customizado do tipo Embutido então atualiza o produto adicionando o preço do frete no valor do produto.
        ///</summary>
        public async Task<CustomShippingCost> CalculateCustomShippingCost(Guid campaignId, ProductItem item)
        {
            if (item == null) return null;

            var campaignSettings = await _campaignRepository.GetSettings(campaignId);
            if (!campaignSettings.Parametrizations.EnableEmbeddedShippingCost)
            {
                return null;
            }

            if (item.Quantity <= 0) item.Quantity = 1;

            var campaignShippings = await _campaignRepository.GetActiveShippings(campaignId);
            if (campaignShippings == null || campaignShippings.Count == 0)
                return null;
            var shipping = campaignShippings.FirstOrDefault(s => s.CanApply(item));
            return CalculateCustomShippingCost(shipping, item.SalePrice.GetCurrencyOrZero(), item.Quantity);
        }

        private CustomShippingCost CalculateCustomShippingCost(CampaignShipping shipping, decimal priceCurrency, int itemQuantity = 1)
        {
            if (shipping == null) return null;
            LoggerFactory.GetLogger().Info("Calculando frete custom - Tipo {} - Valor Produto {} - Valor Frete {}",
                    shipping.Type, priceCurrency, shipping.FixedCostValue);
            decimal valueToEmbed;
            decimal additionalCost = 0;

            if (shipping.FixedCostType.Value == FieldValueType.Percentage)
            {
                valueToEmbed = shipping.FixedCostValue.Value * priceCurrency * 0.01m;
            }
            else
            {
                valueToEmbed = shipping.FixedCostValue ?? 0;
            }

            if (itemQuantity > 1 && shipping.ApplyAdditionalValue)
            {
                if (shipping.AdditionalType == FieldValueType.Percentage)
                {
                    additionalCost = shipping.AdditionalValue.Value * priceCurrency * 0.01m;
                }
                else
                {
                    additionalCost = shipping.AdditionalValue ?? 0;
                }
            }
            return CustomShippingCost.Of(shipping.Type, valueToEmbed, additionalCost);
        }
    }
}