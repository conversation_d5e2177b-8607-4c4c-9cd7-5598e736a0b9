using System;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.Transactions;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Domain.Services.Refunds
{
    public class RefundProcessorService
    {
        private readonly ITransactionRepository transactionRepository;

        public RefundProcessorService(ITransactionRepository transactionRepository)
        {
            this.transactionRepository = transactionRepository;
        }

        public async Task RefundTransactionById(Guid campaignId, Guid userId, Guid debitTransactionId, Guid originId, TransactionOrigin origin, string refundReason)
        {
            try
            {
                await this.transactionRepository.RefundTransactionById(campaignId, userId, debitTransactionId, originId, origin, refundReason);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "RefundProcessorService - RefundTransactionById", "Erro ao estornar transação por I");
                throw MotivaiException.of("TRANSACTION_REFUND_ERROR", ex.Message, ex);
            }
        }
    }
}