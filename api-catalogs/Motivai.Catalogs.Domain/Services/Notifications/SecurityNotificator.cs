using System;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IRepository.Notifications;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Services.Notifications
{
    public class SecurityNotificator
    {
        private readonly ICampaignRepository campaignRepository;
        private readonly IUserParticipantRepository participantRepository;
        private readonly INotificationRepository notificationRepository;

        public SecurityNotificator(ICampaignRepository campaignRepository, IUserParticipantRepository participantRepository,
            INotificationRepository notificationRepository)
        {
            this.campaignRepository = campaignRepository;
            this.participantRepository = participantRepository;
            this.notificationRepository = notificationRepository;
        }

        public async Task<string> SendSecurityToken(Guid campaignId, Guid userId)
        {
            var cellphone = await GetParticipantCellphone(campaignId, userId);

            var verificationCode = new Random().Next(10000, 99999);
            var smsText = $"Confirme com o codigo de seguranca: {verificationCode}";
            string from = await GetCampaignSmsSenderContact(campaignId);
            var smsSent = await notificationRepository.SendSms(from, cellphone, smsText);

            return verificationCode.ToString();
        }

        private async Task<string> GetParticipantCellphone(Guid campaignId, Guid userId)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Usuário inválido.");

            var contact = await participantRepository.GetParticipantContact(userId, campaignId);

            if (contact == null || string.IsNullOrEmpty(contact.Cellphone))
                throw MotivaiException.ofValidation("Participante não possui um número de celular cadastrado.");
            return contact.Cellphone;
        }

        private async Task<string> GetCampaignSmsSenderContact(Guid campaignId)
        {
            var settings = await campaignRepository.GetSettings(campaignId);
            if (settings == null || settings.Parametrizations == null)
            {
                return null;
            }
            return settings.Parametrizations.FromForTransactionalSms;
        }
    }
}