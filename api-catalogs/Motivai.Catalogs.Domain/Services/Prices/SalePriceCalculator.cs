using System;

using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Services.Prices;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Domain.Services.Prices
{
    /// <summary>
    /// Classe para calcular o preço de venda em pontos do produto utilizando as configurações da campanha.
    ///
    /// > Producos Motivai e Parceiros (com Processo Integração ou Compra)
    /// Valor Produto Pontos Venda = Valor Produto Reais / Fator de Conversão de Pontos da Campanha
    ///                                 + Taxa GP (%) + Taxa Parceiro Geral + Taxa Parceiro Específica
    ///
    /// > Produtos Clientes
    /// Valor Produto Pontos Venda = Valor Produto Reais / Fator de Conversão de Pontos da Campanha
    ///                                 + Taxa Parceiro Geral + Taxa Parceiro Específica
    /// </summary>
    public class SalePriceCalculator
    {
        private readonly ProductPriceCalculator _priceCalculator;

        public CampaignFees CampaignFees { get; private set; }

        public SalePriceCalculator(CampaignFees campaignFees)
        {
            CampaignFees = campaignFees;
            _priceCalculator = ProductPriceCalculator.WithFees(campaignFees);
        }

        public static SalePriceCalculator WithFees(CampaignFees campaignFees)
        {
            return new SalePriceCalculator(campaignFees);
        }

        /// <summary>
        ///  Calcula os valores Promocional e de Venda do SKU em pontos com a taxa administrativa.
        /// </summary>
        public SkuPrice CalculateSkuPricesPoints(Guid campaignId, ProductItem product, SkuPrice prices)
        {
            if (prices.DynamicPrice && !prices.IsDynamicPriceDefinedByParticipant())
                return prices;

            var itemCalc = new ItemCalculation()
            {
                PartnerId = product.PartnerId,
                ProcessType = product.ProcessType,
                ElasticsearchId = product.ElasticsearchId,
                SkuId = product.SkuId,
                SkuCode = product.SkuCode,
                DepartmentId = product.DepartmentId,
                CategoryId = product.CategoryId,
                SubcategoryId = product.SubcategoryId,
            };

            // TODO: remover após a carga para corrigir flags
            if (product.ProcessType == SharedKernel.Domain.Enums.ProcessType.Online && !prices.ApplyFactorConversion)
            {
                prices.ApplyFactorConversion = true;
            }

            decimal? priceFrom = default;
            if (prices.Promotional)
            {
                var promotionalPrices = CalculatePricePoints(itemCalc, prices.PriceFrom.Value, prices.ApplyFactorConversion);
                priceFrom = promotionalPrices.GetSalePrice().GetPoints();
            }
            var detailedPrice = CalculatePricePoints(itemCalc, prices.Price, prices.ApplyFactorConversion);
            if (detailedPrice != null && prices.DetailedPrice != null)
            {
                detailedPrice.PartnerPrice = prices.DetailedPrice.PartnerPrice;
            }
            LoggerFactory.GetLogger().Debug("Catalogs - SalePriceCalculator - Cmp {0} - ID {1} - SKU {2} - Preço R$: {3} - Preço Parceiro: {4} - Aplicar Fator: {5} - Preço Pontos: {6}",
                campaignId, itemCalc.ElasticsearchId, itemCalc.SkuCode, prices.Price, detailedPrice.PartnerPrice?.Currency, prices.ApplyFactorConversion, detailedPrice.CostPrice.Points);


            if (prices.IsDynamicPriceDefinedByParticipant())
            {
                return SkuPrice.OfPriced(detailedPrice, priceFrom, prices.DynamicPrice, prices.DynamicPricingSetter,
                    prices.DynamicPriceMinimumValue, prices.DynamicPriceMaximumValue);
            }
            if (prices.EnableCustomPromotionalPrice)
            {
                return SkuPrice.OfCustomPromotionalPrice(prices, priceFrom, detailedPrice);
            }

            return SkuPrice.OfPriced(priceFrom, detailedPrice);
        }

        /// <summary>
        ///  Calcula o valor em pontos com a taxa administrativa.
        /// </summary>
        public DetailedPrice CalculatePricePoints(ItemCalculation item, decimal productValue, bool applyConversionFactor)
        {
            if (applyConversionFactor)
                return _priceCalculator.CalculatePriceUsingCurrency(item, productValue);
            return _priceCalculator.CalculatePriceUsingPoints(item, productValue);
        }
    }
}