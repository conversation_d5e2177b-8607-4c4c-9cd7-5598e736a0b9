using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IApp.PointsPurchase;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Domain.Services.Prices {
    public class ProductPointsPurchaseCalculator {
        private readonly ICampaignRepository campaignRepository;
        private readonly IPointsPurchaseApp pointsPurchaseApp;

        public ProductPointsPurchaseCalculator(ICampaignRepository campaignRepository, IPointsPurchaseApp pointsPurchaseApp) {
            this.campaignRepository = campaignRepository;
            this.pointsPurchaseApp = pointsPurchaseApp;
        }

        private async Task<bool> IsEnabled(Guid campaignId) {
            var campaignParams = await campaignRepository.GetSettings(campaignId);
            // ignora caso seja B2B ou não esteja ativado
            return campaignParams.Type.HasMarketplace() && campaignParams.Parametrizations.ShowProductsListingWithPointsPurchaseCost;
        }

        ///<summary>
        /// Calcula o custo da compra dos pontos necessários para completar o valor do produto
        ///</summary>
        public async Task CalculatePointsNeededCostForProduct(Guid campaignId, ProductDetailsModel product, decimal availableBalance) {
            if (availableBalance <= 0 || product.Prices.Price <= availableBalance || !(await IsEnabled(campaignId))) {
                return;
            }
            decimal pointsNeeded = product.Prices.Price - availableBalance;
            product.AvailableBalance = availableBalance;
            product.PointsNeededPurchaseCost = await pointsPurchaseApp.CalculatePurchaseCostFor(campaignId, pointsNeeded);
        }

        ///<summary>
        /// Calcula o custo da compra dos pontos necessários para completar o valor do produto
        ///</summary>
        public async Task CalculatePointsNeededCostForEachProduct(Guid campaignId, List<ProductShowcaseModel> products, decimal availableBalance) {
            if (availableBalance <= 0 || !(await IsEnabled(campaignId))) {
                return;
            }
            await products.Where(product => !(product.DynamicPrice ?? false) && product.Price > availableBalance)
                .ForEachAsync(async product => {
                    decimal pointsNeeded = product.Price - availableBalance;
                    product.AvailableBalance = availableBalance;
                    product.PointsNeededPurchaseCost = await pointsPurchaseApp.CalculatePurchaseCostFor(campaignId, pointsNeeded);
                });
        }
    }
}