using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Domain.Services.Prices {
    public class DiscountCalculator {
        private readonly ICampaignRepository campaignRepository;

        public DiscountCalculator(ICampaignRepository campaignRepository) {
            this.campaignRepository = campaignRepository;
        }

        public async Task ApplyDiscount(Guid campaignId, ProductItem item, SkuPrice price) {
            if (price.DynamicPrice) return;

            var matchedDiscounts = await GetMatchingDiscounts(campaignId, item);
            if (matchedDiscounts.IsNullOrEmpty()) return;

            var salePrice = price.GetCalculatedSalePrice();
            var totalDiscount = Amount.Zero();
            matchedDiscounts.ForEach(discount => {
                totalDiscount += discount.CalculateDiscountAmount(salePrice);
            });
            LoggerFactory.GetLogger().Info("Catalogs - DiscountCalculator - Cmp {0} - ID {1} - SKU {2} - Desconto: R$ {3}; Pontos: {4}",
                campaignId, item.ElasticsearchId, item.SkuId, totalDiscount.Currency, totalDiscount.Points);
            price.ApplyDiscount(totalDiscount);
        }

        private async Task<List<DiscountRules>> GetMatchingDiscounts(Guid campaignId, ProductItem item) {
            var discounts = await campaignRepository.GetDiscounts(campaignId);
            if (discounts.IsNullOrEmpty())
                return null;
            return discounts.Where(d => d.CanApplyTo(item)).ToList();
        }

        public async Task ApplyDiscountCoupon(Cart cart) {
            if (!cart.HasCouponCode()) return;

            var discount = await campaignRepository.GetDiscountByCouponCode(cart.CampaignId, cart.DiscountCoupon);
            if (discount == null) {
                throw MotivaiException.ofValidation("Cupom de desconto inválido.");
            }
            LoggerFactory.GetLogger().Info("DiscountCalculator - Cmp {0} - Usr {1} - Aplicando cupom de desconto: {2} de {3} ({4})",
                cart.CampaignId, cart.UserId, cart.DiscountCoupon, discount.DiscountValue, discount.DiscountType);

            // TODO: Verificar se o cupom não permite usar mais de uma vez

            if (discount.OnlyApplcableAtCart()) {
                ApplyDiscountCouponAtCart(discount, cart);
            } else {
                ApplyDiscountOnMatchingItems(discount, cart);
            }
        }

        private void ApplyDiscountCouponAtCart(DiscountRules discount, Cart cart) {
            var discountTotal = discount.CalculateDiscountAmount(cart.GetTotalAmount());
            LoggerFactory.GetLogger().Info("Catalogs - DiscountCalculator - Desconto - Cmp {0} - Usr {1} - Cupom {2} - Desconto: R$ {3}; Pontos: {4}",
                cart.CampaignId, cart.UserId, cart.DiscountCoupon, discountTotal.Currency, discountTotal.Points);
            cart.ApplyDiscount(discountTotal);
        }

        private void ApplyDiscountOnMatchingItems(DiscountRules discount, Cart cart) {
            bool hasApplied = false;
            cart.ChildrenCarts.ForEach(childCart => {
                childCart.Products.ForEach(product => {
                    if (product.DynamicPrice) return;
                    var item = ProductItem.Of(product);
                    if (discount.CanApplyTo(item)) {
                        var discountAmount = discount.CalculateDiscountAmount(product.GetTotalItems());
                        product.ApplyDiscount(discountAmount);
                        hasApplied = true;
                    }
                });
            });
            if (!hasApplied) {
                cart.DiscountCoupon = null;
                cart.Notification = "Nenhum item do carrinho se aplica nas condições do cupom de desconto.";
                // throw MotivaiException.ofValidation("Nenhum item do carrinho se aplica nas condições do cupom de desconto.");
            }
        }
    }
}