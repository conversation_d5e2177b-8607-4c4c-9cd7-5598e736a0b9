using System;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.IRepository.ShoppingCarts;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Services.ShoppingCarts
{
    public class ShoppingCartManager
    {
        private readonly IShoppingCartRepository _shopingCartRepository;
        private readonly CatalogProductService _catalogProductService;

        public ShoppingCartManager(IShoppingCartRepository shopingCartRepository, CatalogProductService catalogProductService)
        {
            this._shopingCartRepository = shopingCartRepository;
            this._catalogProductService = catalogProductService;
        }

        private async Task<ShoppingCart> GetOrCreateCart(Guid campaignId, Guid userId)
        {
            var cart = await _shopingCartRepository.GetParticipantShoppingCart(campaignId, userId);

            if (cart == null)
            {
                cart = ShoppingCart.New(campaignId, userId);
            }
            else
            {
                // TODO: Será removido quando fizer o carregamento do Carrinho (atualmente sempre está montando um novo)
                cart.ClearItems();
            }
            return cart;
        }

        public async Task AddSkuToParticipantCart(Guid campaignId, Guid userId, ProductDetailsModel product, int quantity = 1)
        {
            if (product == null || !product.Available)
                return;

            var shoppingCart = await GetOrCreateCart(campaignId, userId);
            var updatedItem = shoppingCart.AddItem(ShoppingCartItem.FromProduct(product, quantity));
            product.Quantity = updatedItem.Quantity;
            await _shopingCartRepository.Save(shoppingCart);

            // await catalogProductService.ReserveStock(campaignId, userId, product, 1);
        }

        public async Task UpdateParticipantCart(Cart cart)
        {
            var dbShoppingCart = await GetOrCreateCart(cart.CampaignId, cart.UserId);
            dbShoppingCart.UpdateWith(cart);
            await _shopingCartRepository.Save(dbShoppingCart);
        }

        public Task SetCreatingOrder(Guid campaignId, Guid userId)
        {
            return _shopingCartRepository.SetCreatingOrder(campaignId, userId);
        }

        public Task SetCartOrdered(Guid campaignId, Guid userId, Guid orderId, string orderNumber)
        {
            return _shopingCartRepository.SetCartOrdered(campaignId, userId, orderId, orderNumber);
        }
    }
}