using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Services.Shippings;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Newtonsoft.Json;

namespace Motivai.Catalogs.Domain.Services.ShoppingCarts
{
	public class OnlineShoppingCartCalculator
	{
		///<summary>
		/// Define se haverá integração com os parceiros online. Se estiver desligado será setado um frete fixo.
		///</summary>
		private readonly bool _enablePartnerIntegration;

		private readonly ICampaignRepository campaignRepository;
		private readonly IProductCatalogRepository productRepository;
		private readonly IOrderIntegrationRepository orderIntegrationRepository;

		private readonly CatalogProductService catalogProductService;
		private readonly CatalogShippingService shippingService;

		public OnlineShoppingCartCalculator(ICampaignRepository campaignRepository, IProductCatalogRepository productRepository,
			IOrderIntegrationRepository orderIntegrationRepository, CatalogProductService catalogProductService,
			CatalogShippingService shippingService)
		{
			this.campaignRepository = campaignRepository;
			this.productRepository = productRepository;
			this.orderIntegrationRepository = orderIntegrationRepository;
			this.catalogProductService = catalogProductService;
			this.shippingService = shippingService;
			this._enablePartnerIntegration = ConfigurationHelper.GetValue("EnablePartnerIntegration") == "1";
		}

		public async Task CalculateOnlineCart(Guid campaignId, Cart cart)
		{
			// Consulta o CEP para obter a UF de Entrega (alguns parceiros retorna uma lista de fretes por UF)
			if (string.IsNullOrEmpty(cart.Cep))
			{
				throw MotivaiException.ofValidation("CEP de entrega é obrigatório para cálculo no parceiro.");
			}

			// Carregar as configuracoes de integracao do parceiro da campanha (se nao tiver carregar default do cliente)
			var onlineChildrenCarts = cart.ChildrenCarts.Where(pc => pc.IsOnline()).ToList();

			if (this._enablePartnerIntegration)
			{
				await CalculateCartAtPartner(campaignId, cart, onlineChildrenCarts);
			}
			else
			{
				var fixedShippingCost = this.shippingService.SetFixedCost(campaignId);
				onlineChildrenCarts.ForEach(pc =>
				{
					pc.Products.ForEach(p =>
					{
						p.Available = true;
						p.DetailedShippingCost = fixedShippingCost.DetailedShippingCost;
						p.ShippingCost = fixedShippingCost.Prices;
						p.EstimatedDeliveryDays = fixedShippingCost.EstimatedDeliveryDays;
					});
				});
			}

			await ApplyCampaignFeesAndDiscounts(campaignId, cart, onlineChildrenCarts);
		}

		private async Task ApplyCampaignFeesAndDiscounts(Guid campaignId, Cart cart, List<ChildCart> onlineCarts)
		{
			// Consulta a campanha para obter as taxas e os descontos
			var campaignSetings = await campaignRepository.GetSettings(campaignId);
			var campaignFees = await campaignRepository.GetFees(campaignId);
			var conversor = AmountConversor.WithPointsConversionFactor(campaignFees.PointsConversionFactor);

			// ! TODO: Migrar para Orders-Integrations (os vales da DirectShop não consulta frete - usam o cadastrado no Product.Skus.Shipping)
			List<ProductDetailsModel> vouchers = null;
			if (onlineCarts.Exists(pc => pc.HasAnyVoucher()))
			{
				var partnersSkus = onlineCarts.SelectMany(pc => pc.Products.Where(p => p.IsVoucher()))
					.Select(i => PartnerSku.OfPartnerAndSkuCode(i.GetItemGrouperId(), i.SkuCode))
					.ToList();
				vouchers = await productRepository.GetProductsForCalculationByPartnersAndSkus(campaignId, cart.UserId, cart.ParticipantId, partnersSkus);
			}

			// Efetua a conversão e aplica as taxas nos valores dos frete
			await onlineCarts.ForEachAsync(async partnerCart =>
			{
				if (!partnerCart.IsOnlinePartnerAndWasCalculated())
					return;

				await partnerCart.Products.ForEachAsync(async item =>
				{
					SkuPrice skuPrice = new SkuPrice()
					{
						Price = item.UnitPrices.GetCurrencyOrZero(),
						ApplyFactorConversion = true
					};

					// ! TODO: Migrar para Orders-Integrations (os vales da DirectShop não consulta frete - usam o cadastrado no Product.Skus.Shipping)
					if (item.IsVoucher())
					{
						await CalculateOnlineVoucherPricing(campaignId, cart, vouchers, item);
					}

					if (!item.Available)
					{
						if (string.IsNullOrEmpty(item.ErrorMessage))
							item.SetError("Produto indisponível no parceiro.");
						else
							item.SetError(item.ErrorMessage);
						return;
					}

					// Aplicar Taxas nos valores dos produtos atualizados
					if (item.PriceUpdate && item.UnitPrices != null)
					{
						item.AddNotification("Houve alteração no preço do produto.");
					}

					if (cart.Type.HasMarketplace())
					{
						item.PartnerId = partnerCart.ItemGrouperId;
					}
					else
					{
						item.FactoryId = partnerCart.ItemGrouperId;
					}

					if (item.EnableCustomPromotionalPrice) {
						var product = await productRepository.GetProductByElasticId(campaignId, cart.UserId, cart.ParticipantId, item.ElasticId);
						if (product != null){
							var selectedSku = product.GetSkuByCode(item.SkuCode);
							if (selectedSku != null) {
								if (selectedSku.Price.EnableCustomPromotionalPrice) {
									skuPrice = SkuPrice.Of(selectedSku.Price);
								}
							}
						}
					}

					var skuPrices = await catalogProductService.CalculateItemPrice(campaignId, ProductItem.Of(item),
					skuPrice, campaignFees);
					item.SetPrice(skuPrices);


					// Calcula valor em pontos do frete do parceiro
					item.ShippingCost.Points = conversor.ApplyPointsFactor(item.ShippingCost.GetCurrencyOrZero());

					// TODO: Ignora os vouchers visto que foi calculado manualmente (remover if quando mover para Order-Integrations)
					if (!item.IsVoucher())
					{
						// Se estiver habilitado aplica o calculo customizado de frete
						if (campaignSetings.Parametrizations.EnableEmbeddedShippingCost)
						{
							await SaveShippingCostDetails(campaignId, cart, item);
						}
						else
						{ // Não atualiza o frete do voucher
							item.DetailedShippingCost = DetailedShippingCost.OfCost(item.ShippingCost);
						}
					}
				});
				// Totaliza o frete
				partnerCart.TotalizeShippingCost();
				cart.VerifyErrors();
			});
		}

		private async Task CalculateCartAtPartner(Guid campaignId, Cart cart, List<ChildCart> onlineCarts)
		{
			// Para cada parceiro do carrinho chamar a integração
			await onlineCarts.ForEachAsync(async childCart =>
			{
				try
				{
					// Carrega as configurações de integração do parceiro na campanha
					if (!childCart.HasIntegrationData())
					{
						await LoadPartnerIntegrationData(campaignId, childCart);
					}

					await orderIntegrationRepository.CalculateIntegrationCart(cart.Cep, childCart);
				}
				catch (Exception ex)
				{
					await ExceptionLogger.LogException(ex, "Cart - Online", "Erro ao calcular carrinho no parceiro", true);
					childCart.SetError("Não foi possível calcular o carrinho no parceiro");
				}
			});
		}

		private async Task LoadPartnerIntegrationData(Guid campaignId, ChildCart childCart)
		{
			var partnerConf = await campaignRepository.GetPartnerConfiguration(campaignId, childCart.ItemGrouperId);
			if (partnerConf == null)
				return;
			childCart.CnpjIntegration = partnerConf.CnpjIntegration;
			childCart.CampaignIdIntegration = partnerConf.CampaignIntegrationId;
			childCart.PartnerIdentifier = partnerConf.PartnerIdentifier;
		}

		///<summary>
		/// ! TODO: Migrar para Orders-Integrations (os vales da DirectShop não consulta frete - usam o cadastrado no Product.Skus.Shipping)
		///</summary>
		private async Task CalculateOnlineVoucherPricing(Guid campaignId, Cart cart, List<ProductDetailsModel> products, CartItem item)
		{
            if (products == null)
                return;
			var product = products.FirstOrDefault(p => p.Id == item.ProductId && p.SkuId == item.SkuId);
			if (product == null)
				return;
			item.Available = product.Available;

			if (product.Available)
			{
				item.UnitPrices = Amount.Of(product.Prices.PriceCurrency, product.Prices.Price);
				item.SetShippingCostResult(await shippingService.CalculateShippingForItem(campaignId, product, item, cart.Cep));
			}

			/*
			if (product.Shipping != null && product.Shipping.HasFixedShippingCost()) {
				if (product.Shipping.IsShippingCostInCurrency()) {
					var costInCurrency = product.Shipping.CurrencyValue ?? 0;
					item.ShippingCost = Amount.Of(costInCurrency, conversor.ApplyPointsFactor(costInCurrency));
				} else {
					var costInPoints = product.Shipping.PointsValue ?? 0;
					item.ShippingCost = Amount.Of(conversor.RevertPointsFactorApplied(costInPoints), costInPoints);
				}
			}
			*/
		}

		private async Task SaveShippingCostDetails(Guid campaignId, Cart cart, CartItem item)
		{
			var customShippingCost = await shippingService.CalculateShippingForItem(campaignId, ShippingCostItemCalculation.OfCartItem(cart, item));
			if (customShippingCost == null)
			{
				// não atualiza o preço do voucher que foi calculado anteriormente
				if (!item.IsVoucher())
				{
					item.DetailedShippingCost = DetailedShippingCost.OfCost(item.ShippingCost);
				}
				return;
			}

			// Mantem o custo do frete do parceiro
            customShippingCost.DetailedShippingCost.CostPrice = item.ShippingCost;
			item.SetShippingCostResult(customShippingCost);
		}
	}
}