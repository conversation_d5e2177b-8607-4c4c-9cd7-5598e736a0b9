using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.IRepository.GeneralSettings;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Models.Shippings;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Services.Shippings;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Domain.Services.ShoppingCarts
{
    public class ProductShippingCostCalculator
    {
        private readonly IPlatformGeneralSettingsRepository _generalSettingsRepository;
        private readonly ICampaignRepository _campaignRepository;
        private readonly IUserParticipantRepository _participantRepository;
        private readonly IProductCatalogRepository _productRepository;
        private readonly CatalogShippingService _shippingService;
        private readonly ProductFactorySelector _factorySelector;

        public ProductShippingCostCalculator(IPlatformGeneralSettingsRepository generalSettingsRepository,
            ICampaignRepository campaignRepository,
            IUserParticipantRepository participantRepository, IProductCatalogRepository productRepository,
            CatalogShippingService shippingService, ProductFactorySelector factorySelector)
        {
            _generalSettingsRepository = generalSettingsRepository;
            _campaignRepository = campaignRepository;
            _participantRepository = participantRepository;
            _productRepository = productRepository;
            _shippingService = shippingService;
            _factorySelector = factorySelector;
        }

        public async Task<ShippingCostResult> CalculateItemShippingCost(Guid campaignId, Guid userId, Guid participantId, ItemShippingModel item)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");
            if (participantId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");
            if (item == null)
                throw MotivaiException.ofValidation("Informe o produto e o CEP para efetuar o cálculo.");
            item.Validate();

            if (!await _participantRepository.IsActiveUserInCampaign(userId, campaignId))
            {
                throw MotivaiException.ofValidation("Usuário não participa da campanha informada.");
            }
            var product = await _productRepository.GetProductForCalculationByElasticId(campaignId, userId, participantId, item.ElasticsearchId, item.SkuCode);
            if (product == null) return null;

            var shippingResult = await _shippingService.CalculateShippingForItem(campaignId, product, item.Quantity, item.Cep);

            if (product.ProductType != ProductType.ValeVirtual)
            {
                var campaignFees = await _campaignRepository.GetFees(campaignId);
                var conversor = AmountConversor.WithPointsConversionFactor(campaignFees.PointsConversionFactor);

                var riskAssessmentSettings = await _generalSettingsRepository.GetActiveRiskAssessmentSettings();
                var platformFeeDetails = PlatformFeeDetails.Of(conversor, riskAssessmentSettings);
                shippingResult.AddFee(platformFeeDetails);
            }

            // Se não ocorreu erro então carrega os fabricantes
            if (!shippingResult.OccurredError)
            {
                var campaignSettings = await _campaignRepository.GetSettings(campaignId);
                // Carrega as fábricas do produto do parceiro caso esteja habilitado a seleção
                if (campaignSettings.Type.IsB2B() && campaignSettings.Parametrizations.EnableSelectProductFactory)
                {
                    shippingResult.Factories = await _factorySelector.LoadProductFactories(campaignId, userId, participantId,
                        product.PartnerId, product.Id, null, item.Cep);
                }
            }
            return shippingResult;
        }

        public async Task<List<ProductFactory>> GetAvailableFactories(Guid campaignId, Guid userId, Guid participantId, ItemShippingModel item)
        {
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");
            if (userId == Guid.Empty || participantId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");
            if (item == null)
                throw MotivaiException.ofValidation("Informe o produto e o CEP para efetuar o cálculo.");
            item.Validate();

            var campaignSettings = await _campaignRepository.GetSettings(campaignId);
            // Carrega as fábricas do produto do parceiro caso esteja habilitado a seleção
            if (!campaignSettings.Parametrizations.EnableSelectProductFactory)
                throw MotivaiException.ofValidation("Campanha não está configurada para seleção de fábrica ou distribuidora.");

            var product = await _productRepository.GetProductForCalculationByElasticId(campaignId, userId, participantId, item.ElasticsearchId, item.SkuCode);
            if (product == null) return null;

            return await _factorySelector.LoadProductFactories(campaignId, userId, participantId, product.PartnerId,
                product.Id, item.ShippingAddressId, item.Cep);
        }
    }
}