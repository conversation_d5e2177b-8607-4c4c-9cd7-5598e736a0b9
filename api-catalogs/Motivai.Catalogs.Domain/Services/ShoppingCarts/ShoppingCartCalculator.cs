using System;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.Catalogs.Domain.IRepository.GeneralSettings;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Prices;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.Catalogs.Domain.Services.Security;

namespace Motivai.Catalogs.Domain.Services.ShoppingCarts
{
    public class ShoppingCartCalculator
    {
        private readonly IPlatformGeneralSettingsRepository _generalSettingsRepository;
        private readonly ICampaignRepository _campaignRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly OnlineShoppingCartCalculator _onlineCartCalculator;
        private readonly OfflineShoppingCartCalculator _offlineCartCalculator;
        private readonly DiscountCalculator _discountCalculator;
        private readonly ShoppingCartManager _shoppingCartManager;

        public ShoppingCartCalculator(IPlatformGeneralSettingsRepository generalSettingsRepository,
                ICampaignRepository campaignRepository, ICompanyRepository companyRepository,
                OnlineShoppingCartCalculator onlineCartCalculator, OfflineShoppingCartCalculator offlineCartCalculator,
                DiscountCalculator discountCalculator, ShoppingCartManager shoppingCartManager)
        {
            _generalSettingsRepository = generalSettingsRepository;
            this._campaignRepository = campaignRepository;
            this._companyRepository = companyRepository;
            this._onlineCartCalculator = onlineCartCalculator;
            this._offlineCartCalculator = offlineCartCalculator;
            this._discountCalculator = discountCalculator;
            this._shoppingCartManager = shoppingCartManager;
        }

        public async Task<Cart> CalculateParticipantCart(Guid campaignId, Guid userId, Cart cart)
        {
            if (cart == null)
                throw MotivaiException.ofValidation("Carrinho inválido.");
            if (campaignId == Guid.Empty)
                throw MotivaiException.ofValidation("Campanha inválida.");

            await ValidateAllowedPeriod(campaignId, userId);

            cart.Validate();
            await UpdatePartnersProcessType(cart);

            // Calcula os carrinhos de integração
            if (cart.IsMarketplaceWithOnlineCart())
            {
                await _onlineCartCalculator.CalculateOnlineCart(campaignId, cart);
            }

            await _offlineCartCalculator.CalculateOfflineCart(campaignId, cart);
            await ApplyPlatformFees(cart);

            // Aplica descontos do cupom
            if (cart.HasCouponCode())
            {
                await _discountCalculator.ApplyDiscountCoupon(cart);
            }

            // Se ocorrer erro em algum dos filhos
            cart.VerifyErrors();

            // Totaliza o prazo do carrinho
            TotalizeShippingForecast(cart);

            await _shoppingCartManager.UpdateParticipantCart(cart);

            return cart;
        }

        private async Task ApplyPlatformFees(Cart cart)
        {
            var campaignFees = await _campaignRepository.GetFees(cart.CampaignId);
            var conversor = AmountConversor.WithPointsConversionFactor(campaignFees.PointsConversionFactor);

            var riskAssessmentSettings = await _generalSettingsRepository.GetActiveRiskAssessmentSettings();
            var platformFeeDetails = PlatformFeeDetails.Of(conversor, riskAssessmentSettings);
            cart.ApplyPlatformFees(platformFeeDetails);
        }

        private async Task ValidateAllowedPeriod(Guid campaignId, Guid userId)
        {
            var campaignSettings = await this._campaignRepository.GetSettings(campaignId);
            CampaignOrderRestrictedPeriodVerifier.ValidateCampaignPlaceOrderRestriction(campaignId, userId, campaignSettings);
        }

        private async Task UpdatePartnersProcessType(Cart cart)
        {
            await cart.ChildrenCarts.ForEachAsync(async partnerCart =>
            {
                if (partnerCart.ItemGrouperId == Guid.Empty) return;
                // Carrega o tipo de processo do parceiro
                partnerCart.ProcessType = await _companyRepository.GetProcessTypeByPartner(partnerCart.ItemGrouperId);
                var partnerSettingsDto = await _companyRepository.GetPartnerSettings(partnerCart.ItemGrouperId);
                var partnerSettings = new PartnerSettingsModel(partnerSettingsDto);

                partnerCart.Products.ForEach(item =>
                {
                    item.PartnerSettings = partnerSettings;
                    if (cart.Type.HasMarketplace())
                    {
                        item.PartnerId = partnerCart.ItemGrouperId;
                    }
                    else
                    {
                        item.FactoryId = partnerCart.ItemGrouperId;
                    }
                });
            });
        }

        private static void TotalizeShippingForecast(Cart cart)
        {
            var cartEstimatedDeliveryDays = 0;

            cart.ChildrenCarts.ForEach(childCart =>
            {
                childCart.EstimatedDeliveryDays = childCart.Products
                    .Select(p => p.EstimatedDeliveryDays ?? 0)
                    .Sum();
                if (childCart.EstimatedDeliveryDays.HasValue && childCart.EstimatedDeliveryDays > 0)
                {
                    cartEstimatedDeliveryDays += childCart.EstimatedDeliveryDays.Value;
                }
            });

            if (cartEstimatedDeliveryDays > 0)
                cart.EstimatedDeliveryDays = cartEstimatedDeliveryDays;
        }
    }
}