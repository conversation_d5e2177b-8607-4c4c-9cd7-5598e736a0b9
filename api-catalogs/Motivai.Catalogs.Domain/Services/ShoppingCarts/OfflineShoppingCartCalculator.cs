using System;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Services.Shippings;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Domain.Services.ShoppingCarts
{
    public class OfflineShoppingCartCalculator
	{
		private readonly IProductCatalogRepository _productRepository;
		private readonly CatalogProductService _catalogProductService;
		private readonly CatalogShippingService _shippingService;
		private readonly ICompanyRepository companyRepository;

		public OfflineShoppingCartCalculator(IProductCatalogRepository productRepository,
			CatalogProductService catalogProductService, CatalogShippingService shippingService, ICompanyRepository companyRepository)
		{
			this._productRepository = productRepository;
			this._catalogProductService = catalogProductService;
			this._shippingService = shippingService;
			this.companyRepository = companyRepository;
		}

		private bool IsOnlinePartnerAndOnlineProduct(ChildCart cart, CartItem item)
		{
			return ProcessTypeHelper.IsOnline(cart.ProcessType) && !item.Offline && !item.PartnerSettings.DisableCartCalculation;
		}

		public async Task CalculateOfflineCart(Guid campaignId, Cart cart)
		{
			// Calcula os carrinhos OffLine
			await cart.ChildrenCarts.ForEachAsync(async childCart =>
			{
				if (childCart.IsOfflinePartnerOrHasAnOfflineItem())
					await CalculateOfflinePartnerCart(campaignId, cart.UserId, cart.ParticipantId, cart.Cep, childCart);
			});
		}

		public async Task CalculateOfflinePartnerCart(Guid campaignId, Guid userId, Guid participantId, string destinationCep, ChildCart partnerCart)
		{
			// Calcula os fretes do produtos
			await partnerCart.Products.ForEachAsync(async item =>
			{
				if (IsOnlinePartnerAndOnlineProduct(partnerCart, item)) return;

				// Consulta os detalhes do produto no Elastic
				var product = await _productRepository.GetProductForCalculationByElasticId(campaignId, userId, participantId, item.ElasticId, item.SkuCode);
				if (product == null)
				{
					item.SetError("Produto não encontrado.");
					item.Available = false;
					return;
				}

				product.SetPartnerSettings(item.PartnerSettings);
                item.ProcessType = product.ProcessType;
                item.ProductType = product.ProductType;
                item.Offline = product.Offline;
				if (product.IsDynamicPriceDefinedByParticipant()) {
					product.SetDynamicCartItemPriceDefinedByParticipant(item);
				}

				// TODO: Verificar se existe o produto no estoque
				try
				{
					await CalculatePricing(campaignId, userId, item, product);
					if (!item.Available)
					{
						return;
					}

					if (item.DynamicPrice)
					{
						// Alterar para considerar nova flag de setter e se for voucher não calcular frete
						item.SetShippingCostResult(ShippingCostResult.OfDisable());
					}
					else
					{
						await ApplyEmbeddedShippingCost(campaignId, destinationCep, item, product);
					}
				}
				catch (MotivaiException ex)
				{
					item.SetError(ex.Message);
				}
				catch (Exception ex)
				{
					await ExceptionLogger.LogException(ex, "Cart - Offline", "Erro ao calcular carrinho offline");
					item.SetError("Ocorreu erro ao efetuar o cálculo do frete.");
				}
			});
			// Totaliza o frete
			partnerCart.TotalizeShippingCost();
		}

		// Mapear calculo de preço para considerar produto com preço dinâmico setado pelo participante
		// Validar range de valor do preço dinâmico do produto
		private async Task CalculatePricing(Guid campaignId, Guid userId, CartItem item, Models.Product.ProductDetailsModel product)
		{
			await _catalogProductService.ApplyDisponibilityAndPrices(campaignId, userId, product);

			item.Available = product.Available;
			if (product.Available)
			{
				item.SetPrice(product.Prices);
			}
			else
			{
				item.SetError("Produto indisponível.");
			}
		}

		private async Task ApplyEmbeddedShippingCost(Guid campaignId, string destinationCep, CartItem item, Models.Product.ProductDetailsModel product)
		{
			// Seta o valor de unit. calculado (pode conter o frete embutido)
			item.UnitPrices = product.Prices.GetSalePrice();

			// Restaura o preço para o cálculo do frete (frete embutido utilizar valor original do produto)
			var salePrice = product.Prices.DetailedPrice.GetSalePrice();
			product.Prices.PriceCurrency = salePrice.GetCurrencyOrZero();
			product.Prices.Price = salePrice.GetPointsOrZero();

			item.SetShippingCostResult(await _shippingService.CalculateShippingForItem(campaignId, product, item, destinationCep));
		}
	}
}