using System;

using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Domain.Services.Security
{
    public static class CampaignOrderRestrictedPeriodVerifier
    {
        public static void ValidateCampaignPlaceOrderRestriction(Guid campaignId, Guid userId,
                CampaignSettingsModel campaignSettings)
        {
            if (campaignSettings.Parametrizations.LimitRedemptionPeriod
                && !DateTime.UtcNow.Between(campaignSettings.Parametrizations.RedemptionPeriodStart, campaignSettings.Parametrizations.RedemptionPeriodEnd))
            {
                throw MotivaiException.ofValidation("Não é permitido resgate fora do período.");
            }

            if (CampaignOrderRestrictedPeriodVerifier.ShouldRejectPlaceOrderDuringRestrictedPeriod(campaignSettings))
            {
                LoggerFactory.GetLogger().Info("Cmp {1} - Usr {2} - Tentativa de pedido de marketplace",
                        campaignId, userId);
                throw MotivaiException.ofValidation("Funcionalidade indisponível no momento.");
            }
        }

        public static bool ShouldRejectPlaceOrderDuringRestrictedPeriod(CampaignSettingsModel campaignSettings)
        {
            if (campaignSettings.Parametrizations.AllowPlaceOrderDuringRestrictedPeriod.HasValue
                && campaignSettings.Parametrizations.AllowPlaceOrderDuringRestrictedPeriod.Value)
            {
                return false;
            }
            return IsPeriodWithPlaceOrderRestricted();
        }

        public static bool IsPeriodWithPlaceOrderRestricted()
        {
            var dateTimeNowInSp = DateTime.UtcNow.ToTimeZoneSaoPaulo();
            return dateTimeNowInSp.Hour < 8 || dateTimeNowInSp.Hour >= 20
                || dateTimeNowInSp.DayOfWeek == DayOfWeek.Saturday
                || dateTimeNowInSp.DayOfWeek == DayOfWeek.Sunday;
        }
    }
}