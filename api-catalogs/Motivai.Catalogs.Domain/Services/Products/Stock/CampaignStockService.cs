using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Dtos;
using Motivai.Catalogs.Domain.Entities.Stocks;
using Motivai.Catalogs.Domain.IRepository.ShoppingCarts;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Products.Stock;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Extensions;
using StackExchange.Redis;

namespace Motivai.Catalogs.Domain.Services.Products.Stocks
{
	public class CampaignStockService
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly IShoppingCartRepository shoppingCartRepository;
		private readonly ICompanyRepository companyRepository;
		private readonly PartnerOnlineStockService onlineStockService;
		private readonly IDatabase redisDatabase;

		public CampaignStockService(ICampaignRepository campaignRepository, ICompanyRepository companyRepository,
			IShoppingCartRepository shopingCartRepository, PartnerOnlineStockService onlineStockService)
		{
			this.campaignRepository = campaignRepository;
			this.companyRepository = companyRepository;
			this.shoppingCartRepository = shopingCartRepository;
			this.onlineStockService = onlineStockService;

			// var redisUri = ConfigurationHelper.GetValue("REDIS_CONNECTION_STRING");
			// if (!string.IsNullOrEmpty(redisUri))
			// {
			// 	var redisConnection = ConnectionMultiplexer.Connect(redisUri);
			// 	this.redisDatabase = redisConnection.GetDatabase();
			// }
		}

		///<summary>
		/// Verifica se o produto está disponível.
		///</summary>
		public async Task CheckProductAvailability(Guid campaignId, Guid userId, ProductDetailsModel product)
		{
			var partnerProcessType = await companyRepository.GetProcessTypeByPartner(product.PartnerId);
			product.ProcessType = partnerProcessType;

			// Checa a disponibilidade do produto no parceiro
			if (ProcessTypeHelper.IsOffline(partnerProcessType) || (product.PartnerSettings != null && product.PartnerSettings.DisableAvailabilityPriceQuery))
			{
				product.Available = true;
			}
			else
			{
				await onlineStockService.CheckOnlineProductAvailability(campaignId, product);
			}
		}

		public async Task UpdateProductsAvailability(Guid campaignId, Guid userId, List<ProductShowcaseModel> products)
		{
			var userGroups = await GetUserGroups(campaignId, userId);

			await products.ForEachAsync(async product =>
			{
				var stockAvailability = await CheckSkuAvailability(campaignId, product.PartnerId, product.SkuId, userGroups);
				product.Available = stockAvailability.Available;
				product.ParticipantGroupdIdWithStock = stockAvailability.ParticipantGroupId;
			});
		}

		public async Task ReserveStock(Guid campaignId, Guid userId, Guid groupId, Guid skuId, int quantity)
		{
			await ReserveSkuGroupStockInCache(campaignId, groupId, skuId, quantity);
		}

		private async Task<List<Guid>> GetUserGroups(Guid campaignId, Guid userId)
		{
			var userGroups = await campaignRepository.GetUserGroups(campaignId, userId);
			if (userGroups == null)
			{
				userGroups = new List<Guid>();
			}
			// Representando o estoque padrão da campanha
			userGroups.Add(Guid.Empty);
			return userGroups;
		}

		private async Task CheckCampaignCustomStock(Guid campaignId, Guid userId, ProductDetailsModel product)
		{
			var skuId = Guid.Parse(product.Sku.Id);
			var userGroups = await GetUserGroups(campaignId, userId);

			var stockAvailability = await CheckSkuAvailability(campaignId, product.PartnerId, skuId, userGroups);
			product.Available = stockAvailability.Available;
			product.StockParticipantGroupId = stockAvailability.ParticipantGroupId;
		}

		private async Task<StockAvailability> CheckSkuAvailability(Guid campaignId, Guid partnerId, Guid skuId, List<Guid> userGroups)
		{
			var available = true;

			foreach (var groupId in userGroups)
			{
				var stockQnty = await GetGroupSkuStock(campaignId, groupId, partnerId, skuId);
				if (stockQnty == null || !stockQnty.HasValue) continue;

				available = stockQnty.Value > 0;
				if (available)
				{
					return StockAvailability.OfGroupStockAvailability(available, groupId);
				}
			}

			return StockAvailability.OfAvailability(available);
		}

		private async Task<int?> GetGroupSkuStock(Guid campaignId, Guid participantGroupId, Guid partnerId, Guid skuId)
		{
			int? stockQnty = await GetSkuGroupStockCache(campaignId, participantGroupId, skuId);
			if (!stockQnty.HasValue)
			{
				var stock = await LoadCampaignStocksBySku(campaignId, participantGroupId, partnerId, skuId);
				if (stock == null)
					return stockQnty;
				stockQnty = stock.CurrentQuantity;
			}
			return stockQnty;
		}

		private async Task<CampaignGroupSkuStock> LoadCampaignStocksBySku(Guid campaignId, Guid participantGroupId, Guid partnerId, Guid skuId)
		{
			var campaignStocks = await campaignRepository.GetCampaignSkuStocks(campaignId, partnerId, skuId);
			if (campaignStocks.IsNullOrEmpty())
				return null;

			// Preenche o cache
			await campaignStocks.ForEachAsync(async stock =>
			{
				await FillSkuGroupStockCache(campaignId, stock.ParticipantGroupId ?? Guid.Empty, stock.SkuId, stock.CurrentQuantity);
			});

			return campaignStocks.FirstOrDefault(s => s.ParticipantGroupId == participantGroupId);
		}

		private async Task<int?> GetSkuGroupStockCache(Guid campaignId, Guid participantGroupId, Guid skuId)
		{
			var key = $"campaign-{campaignId}-group-{participantGroupId}";
			var value = await this.redisDatabase.HashGetAsync(key, "sku-" + skuId);

			int currentStock;
			if (value.IsNullOrEmpty || !value.TryParse(out currentStock))
			{
				return null;
			}

			int reservedStock = await GetReservedStockInCache(campaignId, participantGroupId, skuId);
			return currentStock - reservedStock;
		}

		private async Task FillSkuGroupStockCache(Guid campaignId, Guid participantGroupId, Guid skuId, int stockQnty)
		{
			var key = $"campaign-{campaignId}-group-{participantGroupId}";
			await this.redisDatabase.HashSetAsync(key, "sku-" + skuId, stockQnty);
		}

		private async Task<long> ReserveSkuGroupStockInCache(Guid campaignId, Guid participantGroupId, Guid skuId, int stockQnty)
		{
			var key = $"campaign-{campaignId}-group-{participantGroupId}-reserves";
			return await this.redisDatabase.HashIncrementAsync(key, "sku-" + skuId, stockQnty);
		}

		private async Task<int> GetReservedStockInCache(Guid campaignId, Guid participantGroupId, Guid skuId)
		{
			// var key = $"campaign-{campaignId}-group-{participantGroupId}-reserves";
			// var reserveValue = await this.redisDatabase.HashGetAsync(key, "sku-" + skuId);

			// int reservedStock = 0;
			// if (reserveValue.IsNullOrEmpty || !reserveValue.TryParse(out reservedStock)) {
			//     return 0;
			// }
			// return reservedStock;
			return await shoppingCartRepository.CountSkuGroupStock(campaignId, participantGroupId, skuId);
		}
	}
}