using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.IRepository.Products;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Domain.Services.Products.Stock
{
	public class PartnerOnlineStockService
	{
		private readonly ICampaignRepository campaignRepository;
		private readonly ICompanyRepository companyRepository;
		private readonly IProductIntegrationRepository productIntegrationRepository;
		private readonly IProductRepository productRepository;
		private readonly bool _enablePartnerIntegration;

		public PartnerOnlineStockService(ICampaignRepository campaignRepository, ICompanyRepository companyRepository,
			IProductIntegrationRepository productIntegrationRepository, IProductRepository productRepository)
		{
			this.campaignRepository = campaignRepository;
			this.companyRepository = companyRepository;
			this.productIntegrationRepository = productIntegrationRepository;
			this.productRepository = productRepository;
			this._enablePartnerIntegration = ConfigurationHelper.GetValue("EnablePartnerIntegration") == "1";
		}

		public async Task CheckOnlineProductAvailability(Guid campaignId, ProductDetailsModel product)
		{
			if (!this._enablePartnerIntegration)
			{
				LoggerFactory.GetLogger().Warn("Cmp {} - Integração desligada", campaignId);
				product.Available = true;
				return;
			}

			// Consulta o tipo de processo da empresa
			var processType = await companyRepository.GetProcessTypeByPartner(product.PartnerId);
			if (!ProcessTypeHelper.IsOnline(processType))
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Parceiro {1} - SKU {2} - Configuração inválida no parceiro para integração.",
					campaignId, product.Partner, product.SkuCode);
				product.Available = false;
				return;
			}

			try
			{
				var availabilityResult = await VerifyAvailabilityByIntegration(campaignId, product);
				if (availabilityResult == null)
				{
					product.Available = false;
					return;
				}

				// Se houver alteração da disponibilidade ou do preço atualiza interno (apenas produtos)
				if (VerifyIfNeedUpdate(product, availabilityResult))
				{
					await SendProductAvailabilityUpdate(product, availabilityResult);
				}

				if (product.ProductType == ProductType.Produto)
				{
					product.Available = availabilityResult.Available && availabilityResult.Price > 0;
					if (availabilityResult.Available)
					{
						decimal? priceFrom = null;
						if (availabilityResult.HasPromotionalPrice())
						{
							priceFrom = availabilityResult.PriceFrom;
						}
						product.Prices.UpdatePartnerCosts(availabilityResult.Price, priceFrom);
					}
				}
				else
				{
					product.Available = availabilityResult.Available;
				}

				if (product.Available)
				{
					product.Quantity = availabilityResult.Quantity;
				}
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Cmp {0} - Parceiro {1} - SKU {2} - Erro na disponibilidade do SKU: {3}",
					campaignId, product.Partner, product.SkuCode, ex.Message);
				await ExceptionLogger.LogException(ex, "Stock", "Ocorreu um erro a integração de disponibilidade do produto no parceiro.", true);

				product.Available = false;
				throw MotivaiException.ofException("Não foi possível a consultar disponibilidade de produto.", ex);
			}
		}

		private async Task<AvailabilityModel> VerifyAvailabilityByIntegration(Guid campaignId, ProductDetailsModel product)
		{
			var partnerConf = await campaignRepository.GetPartnerConfiguration(campaignId, product.PartnerId);
			return await productIntegrationRepository.VerifyAvailability(partnerConf, product.SkuCode);
		}

		///<summary>
		/// Verifica se tem diferença na disponibilidade ou preço dos Produtos.
		///</summary>
		private static bool VerifyIfNeedUpdate(ProductDetailsModel product, AvailabilityModel availabilityResult)
		{
			return product.Available != availabilityResult.Available || product.Prices.PriceCurrency != availabilityResult.Price;
		}

		private async Task SendProductAvailabilityUpdate(ProductDetailsModel product, AvailabilityModel availabilityResult)
		{
			LoggerFactory.GetLogger().Info("Parceiro {0} - SKU {1} - Atualizando interno - Antes: {2}, {3} - Depois: {4}, {5}",
				product.Partner, product.SkuCode, product.Available, product.Prices.PriceCurrency,
				availabilityResult.Available, availabilityResult.Price);
			try
			{
				await productRepository.SendProductAvailabilityUpdate(product, availabilityResult);
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Parceiro {0} - SKU {1} - Erro na atualização interna: {2}",
						product.Partner, product.SkuCode, ex.Message);
				if (ex is MotivaiException gpex && gpex.ErrorType == ErrorType.Timeout)
					return;
			}
		}
	}
}