using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;

namespace Motivai.Catalogs.Domain.Services.Products {
    public class ProductFactorySelector {
        private readonly ICorreiosRepository _correiosRepository;
        private readonly IUserParticipantRepository _participantRepository;
        private readonly IRegionsRepository _regionsRepository;
        private readonly IFactoriesRepository _factoriesRepository;

        public ProductFactorySelector(ICorreiosRepository correiosRepository, IUserParticipantRepository participantRepository,
            IRegionsRepository regionsRepository, IFactoriesRepository factoriesRepository) {
            this._correiosRepository = correiosRepository;
            this._participantRepository = participantRepository;
            this._regionsRepository = regionsRepository;
            this._factoriesRepository = factoriesRepository;
        }

        public async Task<List<ProductFactory>> LoadProductFactories(Guid campaignId, Guid userId, Guid participantId,
            Guid partnerId, Guid productId, Guid? shippingAddressId = null, string shippingCep = null) {
            string cep = null, neighborhood = null, city = null, state = null;

            if (!string.IsNullOrEmpty(shippingCep)) {
                var address = await _correiosRepository.QueryCep(shippingCep);
                if (address != null) {
                    cep = shippingCep;
                    neighborhood = address.Neighborhood;
                    city = address.City;
                    state = address.State;
                }
            }
            if (string.IsNullOrEmpty(cep) && shippingAddressId != null && shippingAddressId.HasValue) {
                var address = await _participantRepository.GetAddressById(userId, campaignId, shippingAddressId.Value);
                if (address != null) {
                    cep = address.Cep;
                    neighborhood = address.Neighborhood;
                    city = address.City;
                    state = address.State;
                }
            }
            if (cep == null && neighborhood == null && city == null && state == null) return null;
            var regionsIds = await _regionsRepository.FindRegions(cep, neighborhood, city, state);
            if (regionsIds == null || regionsIds.Count == 0) return null;
            return await _factoriesRepository.FindFactoriesForRegions(partnerId, regionsIds);
        }
    }
}