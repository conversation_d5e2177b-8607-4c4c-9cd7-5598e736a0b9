using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.Marketplace;
using Motivai.Catalogs.Domain.IRepository.GeneralSettings;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.Prices;
using Motivai.Catalogs.Domain.Services.Products.Stock;
using Motivai.Catalogs.Domain.Services.Products.Stocks;
using Motivai.Catalogs.Domain.Services.Shippings;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Services;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

using Newtonsoft.Json;

namespace Motivai.Catalogs.Domain.Services.Products
{
    ///<summary>
    /// Classe responsável por determinar a disponibilidade e preços dos produtos.
    ///</summary>
    public class CatalogProductService
    {
        private readonly IPlatformGeneralSettingsRepository _platformSettingsRepository;
        private readonly DiscountCalculator _discountCalculator;
        private readonly CampaignStockService _stockService;
        private readonly CatalogShippingService _shippingService;
        private readonly ICampaignRepository _campaignRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly ProductPointsPurchaseCalculator _productPointsPurchaseCalculator;
        private readonly PartnerOnlineStockService _onlineStockService;
        private readonly IProductIntegrationRepository _productIntegrationRepository;

        public CatalogProductService(IPlatformGeneralSettingsRepository platformSettingsRepository,
            CatalogShippingService shippingService, DiscountCalculator discountCalculator,
            CampaignStockService campaignStockService, ProductPointsPurchaseCalculator productPointsPurchaseCalculator,
            ICampaignRepository campaignRepository, ICompanyRepository companyRepository,
            PartnerOnlineStockService onlineStockService, IProductIntegrationRepository productIntegrationRepository)
        {
            _platformSettingsRepository = platformSettingsRepository;
            this._discountCalculator = discountCalculator;
            this._stockService = campaignStockService;
            this._shippingService = shippingService;
            this._campaignRepository = campaignRepository;
            this._companyRepository = companyRepository;
            this._productPointsPurchaseCalculator = productPointsPurchaseCalculator;
            this._onlineStockService = onlineStockService;
            this._productIntegrationRepository = productIntegrationRepository;
        }

        ///<summary>
        /// Verifica a disponibilidade do produto e calcula os valores do Preço De e Preço de Venda em Pontos.
        /// Se o produto for Online e não tiver com preço, será feito uma integração
        /// para atualizar o preço e disponibilidade.
        ///</summary>
        public async Task ApplyDisponibilityAndPrices(Guid campaignId, Guid userId, ProductDetailsModel product,
            decimal? availableBalance = null)
        {
            if (product == null) return;
            // Verifica se o produto está disponível para efetuar o cálculo de preço

            await _stockService.CheckProductAvailability(campaignId, userId, product);
            if (product.Available)
                await CalculateSkuPrice(campaignId, product, availableBalance);
        }

        ///<summary>
        /// Cálcula os preços da lista de produtos.
        /// Se os preços estiverem desatualizados não será feito atualização.
        ///</summary>
        public async Task CalculateOutdatedPrices(Guid campaignId, Guid userId, List<ProductShowcaseModel> products,
            decimal? availableBalance = null, bool? shouldConsultSkuAvailaibilityAtPartner = null)
        {
            if (products == null || products.Count == 0) return;
            // Carrega os fatores de pontos e taxas da campanha
            var campaignFees = await _campaignRepository.GetFees(campaignId);

            var coinName = await _campaignRepository.GetCoinName(campaignId);

            // TODO: ler param para habilitar estoque especifico na campanha
            // var campaignSettings = await _campaignRepository.GetSettings(campaignId);
            // if (campaignSettings.Parametrizations.EnableCampaignStock) {
            // await stockService.UpdateProductsAvailability(campaignId, userId, products);
            // }

            var priceCalculator = SalePriceCalculator.WithFees(campaignFees);

            // CARREGA A LISTA DE CONFIGURAÇÕES DOS PARCEIROS PARA OBTER ATRAVÉS DA LEITURA DOS PRODUTOS
            var partnersConfigurations = await _campaignRepository.GetPartnersConfigurations(campaignId);

            await products.ForEachAsync(async product =>
            {
                if (shouldConsultSkuAvailaibilityAtPartner != null && shouldConsultSkuAvailaibilityAtPartner == true)
                {
                    var partnerConfiguration =
                        partnersConfigurations.FirstOrDefault(p => p.PartnerId == product.PartnerId);

                    if (partnerConfiguration == null)
                    {
                        throw MotivaiException.of(ErrorType.Business,
                            "Configurações do parceiro não encontradas pelo ID informado através do Produto.");
                    }

                    await CalculateUpdatedPrice(campaignId, priceCalculator, product, partnerConfiguration);
                }
                else
                {
                    await CalculateOutdatedPrice(campaignId, priceCalculator, product);
                }

                product.CoinName = coinName;
            });

            // Calcula o custo da compra dos pontos necessários para completar o valor do produto
            if (availableBalance != null && availableBalance.HasValue && availableBalance.Value > 0)
            {
                await _productPointsPurchaseCalculator.CalculatePointsNeededCostForEachProduct(campaignId, products,
                    availableBalance.Value);
            }
        }

        private async Task CalculateUpdatedPrice(Guid campaignId, SalePriceCalculator priceCalculator,
            ProductShowcaseModel product, PartnerConfiguration partnerConfigurations)
        {
            var skuAvailability =
                await _productIntegrationRepository.VerifyAvailability(partnerConfigurations, product.SkuCode);

            var item = ProductItem.Of(product);

            var skuPrice = SkuPrice.Of(skuAvailability, product.PartnerPrices.IsApplyConversionFactor());

            var priceWithFees = await CalculateItemPrice(campaignId, priceCalculator, item,
                skuPrice);

            product.SetPrices(priceWithFees);
        }

        private async Task CalculateOutdatedPrice(Guid campaignId, SalePriceCalculator priceCalculator,
            ProductShowcaseModel product)
        {
            if (!product.Available) return;

            // Calcula os valores De e de Venda do Produto
            var item = ProductItem.Of(product);

            var priceWithFees =
                await CalculateItemPrice(campaignId, priceCalculator, item, SkuPrice.Of(product.PartnerPrices));

            product.SetPrices(priceWithFees);
        }

        ///<summary>
        /// Aplica os fatores de conversão e taxa do parceiro produto recebido.
        /// Se os preços estiverem desatualizados não será feito atualização.
        ///</summary>
        private async Task CalculateSkuPrice(Guid campaignId, ProductDetailsModel product,
            decimal? availableBalance = null)
        {
            // Se não tiver o preço atualizado então verifica no parceiro
            if (!product.HasUpdatedSalePrice())
            {
                await UpdateProductPrice(campaignId, product);
            }

            // Se o produto não estiver disponível então não calcula
            if (!product.Available)
            {
                return;
            }

            // Carrega os fatores de pontos e taxas da campanha
            var campaignFees = await _campaignRepository.GetFees(campaignId);

            // Calcula os valores De e de Venda do Produto
            var item = ProductItem.Of(product);
            var skuPrice = await CalculateItemPrice(campaignId, item, product.Prices, campaignFees);

            skuPrice.CoinName = await _campaignRepository.GetCoinName(campaignId);
            product.Prices = skuPrice;

            // Calcula o custo da compra dos pontos necessários para completar o valor do produto
            if (availableBalance != null && availableBalance.HasValue && availableBalance.Value > 0)
            {
                await _productPointsPurchaseCalculator.CalculatePointsNeededCostForProduct(campaignId, product,
                    availableBalance.Value);
            }
        }

        private async Task UpdateProductPrice(Guid campaignId, ProductDetailsModel product)
        {
            var partnerProcessType = await _companyRepository.GetProcessTypeByPartner(product.PartnerId);
            product.ProcessType = partnerProcessType;
            if (ProcessTypeHelper.IsOnline(partnerProcessType) &&
                !product.PartnerSettings.DisableAvailabilityPriceQuery)
            {
                await _onlineStockService.CheckOnlineProductAvailability(campaignId, product);
            }
            else
            {
                throw MotivaiException.of(ErrorType.Business, "SKU está sem preço.");
            }
        }

        ///<summary>
        /// Aplica os fatores de conversão e taxa do parceiro no preço do SKU recebido.
        ///</summary>
        public Task<SkuPrice> CalculateItemPrice(Guid campaignId, ProductItem item, SkuPrice prices,
            CampaignFees campaignFees)
        {
            // Calcula os valores De e de Venda do Produto
            var priceCalculator = SalePriceCalculator.WithFees(campaignFees);
            return CalculateItemPrice(campaignId, priceCalculator, item, prices);
        }

        ///<summary>
        /// disableDynamicPriceValidation: controle se precisa validar o preço dinâmico
        ///</summary>
        private async Task<SkuPrice> CalculateItemPrice(Guid campaignId, SalePriceCalculator priceCalculator,
            ProductItem item, SkuPrice prices)
        {
            if (prices.DynamicPrice)
            {
                if (!prices.ShouldValidateDynamicPriceRange())
                {
                    LoggerFactory.GetLogger().Warn(
                        "Catalogs - ProductService - Cmp {0} - ID {1} - SKU {2} - Preço Dinâmico",
                        campaignId, item.ElasticsearchId, item.SkuCode);
                    return prices;
                }

                prices.ValidateDynamicPriceDefinedByParticipant();
            }

            if (prices.HasCustomPromotionalValue())
            {
                // LoggerFactory.GetLogger().Warn("Catalogs - ProductService - Cmp {0} - ID {1} - SKU {2} - Preço Promocional Customizado",
                //     campaignId, item.ElasticsearchId, item.SkuCode);
                prices.ValidateCustomPromotionalValueAndSetPrice();
            }

            var price = priceCalculator.CalculateSkuPricesPoints(campaignId, item, prices);

            await ApplyEmbeddedCosts(campaignId, priceCalculator.CampaignFees, item, price);
            // Aplica o preço fixo do produto caso tenha fixado na campanha
            await ApplyFixedPrice(campaignId, item, price);

            // seta o preço calculado
            item.SalePrice = price.GetCalculatedSalePrice();

            // Aplica desconto geral utilizando os dados do produto
            await _discountCalculator.ApplyDiscount(campaignId, item, price);

            return price;
        }

        ///<summary>
        /// Verifica se o produto tem preço fixo configurado na campanha então o utiliza.
        ///</summary>
        private async Task ApplyFixedPrice(Guid campaignId, ProductItem item, SkuPrice prices)
        {
            var fixedPrices = await _campaignRepository.GetProductsFixedPrices(campaignId);
            if (fixedPrices == null || fixedPrices.Count == 0)
                return;
            var productPrice = fixedPrices.FirstOrDefault(fp => fp.Matches(item.PartnerId, item.ProductId, item.SkuId));
            if (productPrice == null)
                return;
            LoggerFactory.GetLogger().Info(
                "Catalogs - ProductService - Preço Fixo - Cmp {0} - ID {1} - SKU {2} - Valor Embutido: {3}",
                campaignId, item.ElasticsearchId, item.SkuId, productPrice.FixedSalePrice);
            prices.SetFixedPrice(productPrice.FixedSalePrice);
        }

        ///<summary>
        /// Aplica os custos embutidos no valor do produto.
        /// - custo da análise de risco em Vale Virtual;
        /// - custo do frete embutido configurado na campanha.
        ///</summary>
        private async Task ApplyEmbeddedCosts(Guid campaignId, CampaignFees campaignFees, ProductItem item,
            SkuPrice skuPrice)
        {
            var conversor = AmountConversor.WithPointsConversionFactor(campaignFees.PointsConversionFactor);
            // aplica a taxa de análise de risco somente no Vale Virtual (não tem frete)
            if (item.ProductType == ProductType.ValeVirtual)
            {
                var riskAssessmentSettings = await _platformSettingsRepository.GetActiveRiskAssessmentSettings();
                if (riskAssessmentSettings.HasFee())
                {
                    var platformFeeDetails = PlatformFeeDetails.Of(conversor, riskAssessmentSettings);
                    skuPrice.AddFee(platformFeeDetails);
                }
            }

            await ApplyEmbeddedShippingCost(campaignId, conversor, item, skuPrice);
        }

        ///<summary>
        /// Se o produto tiver frete embutido configurado então calcula, aplica os fatores e soma nos preços.
        ///</summary>
        private async Task ApplyEmbeddedShippingCost(Guid campaignId, AmountConversor conversor, ProductItem item,
            SkuPrice skuPrice)
        {
            // Fretes da campanha que tenham custo embutido no produto
            var embeddedShipping = await _shippingService.CalculateCustomShippingCost(campaignId, item);
            if (embeddedShipping == null) return;

            // Se tiver frete embutido então soma no Preço De e Preço Por
            if (embeddedShipping.IsEmbeddedAndHasCost())
            {
                var detailedShipingCost = embeddedShipping.ToDetailedShippingCost(conversor);
                LoggerFactory.GetLogger().Info(
                    "CatalogProductService - Frete Embutido - Cmp {0} - ID {1} - SKU {2} - Valor Embutido: {3}",
                    campaignId, item.ElasticsearchId, item.SkuId,
                    detailedShipingCost.CustomCostPrice.GetPointsOrZero());
                skuPrice.AddEmbeddedShippingCost(detailedShipingCost);
            }
        }

        public async Task ReserveStock(Guid campaignId, Guid userId, ProductDetailsModel product, int quantity)
        {
            if (product.StockParticipantGroupId.HasValue)
            {
                await _stockService.ReserveStock(campaignId, userId, product.StockParticipantGroupId.Value,
                    product.SkuId, quantity);
            }
        }
    }
}