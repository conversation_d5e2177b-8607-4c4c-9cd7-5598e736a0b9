name: Build Check

on:
  push:
    branches:
      - feature/**
      - develop
      - master
  pull_request:
    branches:
      - master
    types:
      - opened
      - reopened
  workflow_dispatch:
    inputs:
      reason:
        description: 'Motivo'
        required: false

env:
  GH_USERNAME: ${{ secrets.GH_USERNAME }}
  GH_TOKEN: ${{ secrets.GH_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Build
        uses: docker/build-push-action@v2
        with:
          context: .
          build-args: |
            GH_USERNAME=${{ env.GH_USERNAME }}
            GH_TOKEN=${{ env.GH_TOKEN }}
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max
