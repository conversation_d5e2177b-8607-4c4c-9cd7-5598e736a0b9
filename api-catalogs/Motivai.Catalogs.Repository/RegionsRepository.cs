using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Repository
{
    public class RegionsRepository : IRegionsRepository
    {
        public async Task<List<Guid>> FindRegions(string cep, string neighborhood, string city, string state) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Regions)
                .Path("regions/matching")
                .RootBu()
                .Entity(new {
                    cep = cep,
                    neighborhood = neighborhood,
                    city = city,
                    state = state
                })
                .AsyncPost()
                .GetApiReturn<List<Guid>>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }
    }
}