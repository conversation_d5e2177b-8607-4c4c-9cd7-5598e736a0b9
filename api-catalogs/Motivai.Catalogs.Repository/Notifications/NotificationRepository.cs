using System;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.IRepository.Notifications;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Repository.Notifications {
    public class NotificationRepository : INotificationRepository {
        public async Task<bool> NotifyMobileRecharge(Guid campaignId, MobileRechargeTicket ticket) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
                .Path("notifications/mobilerecharges/orders/received")
                .Entity(new {
                    campaignId = campaignId,
                    userId = ticket.UserId,
                    cellphoneDdd = ticket.CellphoneDdd,
                    cellphoneNumber = ticket.CellphoneNumber,
                    protocol = ticket.Protocol,
                    proofPayment = ticket.ProofPayment,
                    operatorName = ticket.OperatorName,
                    rechargeValue = ticket.RechargeValue,
                    totalCost = ticket.ParticipantCost.GetPointsOrZero()
                })
                .AsyncPost()
                .GetApiReturn<bool>();
            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> NotifyBillPayment(Guid campaignId, BillPaymentTicket ticket) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
                .Path("notifications/billpayments/orders/received")
                .Entity(new {
                    campaignId = campaignId,
                    userId = ticket.UserId,
                    assignor = ticket.Assignor,
                    barCode = ticket.BarCode,
                    protocol = ticket.Protocol,
                    proofPayment = ticket.ProofPayment,
                    billingAmount = ticket.BillingAmount,
                    totalCost = ticket.ParticipantCost.GetPointsOrZero()
                })
                .AsyncPost()
                .GetApiReturn<bool>();
            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> SendPurchaseNotification(PointsPurchaseOrder purchaseOrder) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
                .Path("notifications/pointspurchase/orders/received")
                .Entity(new {
                    campaignId = purchaseOrder.CampaignId,
                    userId = purchaseOrder.UserId,
                    pointsAmount = purchaseOrder.PointsToBuy,
                    purchaseOrder.Name,
                    purchaseOrder.Email,
                    purchaseOrder.Cpf,
                    purchaseOrder.Phone,
                    Installments = purchaseOrder.Installment.Count,
                    InstallmentsAmount = purchaseOrder.Installment.Value,
                    Total = purchaseOrder.Installment.Total,
                })
                .AsyncPost()
                .GetApiReturn<bool>();
            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> SendSms(string from, string cellphone, string smsText) {
            var response = await HttpClient.Create(MotivaiApi.SmsService)
                .Path("sms/send")
                .Entity(new {
                    from = from,
                    to = cellphone,
                    text = smsText
                })
                .AsyncPost()
                .GetApiReturn<bool>();
            if (response == null || response.HasNullReturn())
                return false;
            return response.GetReturnOrError();
        }
    }
}