﻿using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Dtos;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;

namespace Motivai.Catalogo.Repository {
    public class CompanyRepository : ICompanyRepository {
        private readonly ICache _cache;

        public CompanyRepository(ICache cache) {
            this._cache = cache;
        }

        public async ValueTask<ProcessType> GetProcessTypeByPartner(Guid partnerId) {
            return await _cache.GetOrCreate("campaign-partner-process-type-" + partnerId, async() => {
                var apiReturn = await HttpClient.Create(MotivaiApi.Companies)
                    .Path(partnerId).Path("processtype")
                    .AsyncGet()
                    .GetApiReturn<ProcessType>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o tipo de processo do parceiro.");
                return apiReturn.GetReturnOrError();
            }, 120);
        }

        public async Task<ClientConfiguration> GetClientConfiguration(Guid clientId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Companies)
                .Path(clientId).Path("client/configuration")
                .AsyncGet()
                .GetApiReturn<ClientConfiguration>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações do cliente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<PartnerSettingsDto> GetPartnerSettings(Guid partnerId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_PARTNET_SETTINGS + partnerId, async() => {
                var apiReturn = await HttpClient.Create(MotivaiApi.Companies)
                    .Path(partnerId)
                    .Path("partnersettings")
                    .AsyncGet()
                    .LogPayloadToConsole()
                    .LogResponseToConsole()
                    .GetApiReturn<PartnerSettingsDto>();

                return apiReturn == null || apiReturn.HasNullReturn() ? null : apiReturn.GetReturnOrError();
            });
        }
    }
}