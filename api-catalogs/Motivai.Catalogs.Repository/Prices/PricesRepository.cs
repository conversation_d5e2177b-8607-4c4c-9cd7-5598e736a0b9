using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.Prices;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.Prices {
    public class PricesRepository : IPricesRepository {
        public async Task<dynamic> CalculateCardOrderFees(dynamic order) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Prices)
                .Path("cards/prepaid/orders/fees")
                .Entity(order)
                .AsyncPost()
                .GetApiReturn<dynamic>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.ofValidation("Não foi possível efetuar o cálculo, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> CalculateBankTransferOrderFees(dynamic order) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Prices)
                .Path("banktransfer/orders/fees")
                .Entity(order)
                .AsyncPost()
                .GetApiReturn<dynamic>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.ofValidation("Não foi possível efetuar o cálculo, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }
    }
}