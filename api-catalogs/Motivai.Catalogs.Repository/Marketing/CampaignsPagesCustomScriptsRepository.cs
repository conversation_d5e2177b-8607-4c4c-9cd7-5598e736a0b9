using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.Marketing;
using Motivai.Catalogs.Domain.Models.Marketing;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;

namespace Motivai.Catalogs.Repository.Marketing
{
    public class CampaignsPagesCustomScriptsRepository : ICampaignsPagesCustomScriptsRepository
    {
		private readonly ICache _cache;

        public CampaignsPagesCustomScriptsRepository(ICache cache)
        {
            this._cache = cache;
        }

        public async Task<List<CampaignPagesCustomScriptModel>> GetCampaignScripts(string campaignId)
        {
            return await this._cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_PAGES_CUSTOM_SCRIPTS + campaignId, async () =>
            {
                var apiReturn = HttpClient.Create(MotivaiApi.Campaigns)
					.Path("campaigns").Path(campaignId).Path("customscripts")
                    .Query("onlyActive", true)
					.AsyncGet();

                var response = await apiReturn.GetApiReturn<List<CampaignPagesCustomScriptModel>>();
				if (response == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível buscar os scripts.");
                return response.GetReturnOrError();
            });
        }
    }
}