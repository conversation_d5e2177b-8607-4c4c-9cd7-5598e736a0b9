using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.Products;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Repository.Products {
    public class ProductRepository : IProductRepository {
        public async Task<bool> SendProductAvailabilityUpdate(ProductDetailsModel product, AvailabilityModel availabilityResult) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Products)
                .Path("products").Path(product.Id).Path("skus").Path(product.SkuId).Path("availability")
                .Entity(new {
                    elasticsearchId = product.ElasticsearchId,
                    skuCode = product.SkuCode,
                    available = availabilityResult.Available,
                    promotionalValue = availabilityResult.PriceFrom,
                    saleValue = availabilityResult.Price
                })
                .Timeout(2_000)
                .AsyncPut()
                .LogPayloadToLogger()
                .GetApiReturn<bool>();
            return apiReturn.GetReturnOrError();
        }
    }
}