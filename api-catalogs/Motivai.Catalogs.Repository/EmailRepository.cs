﻿using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogo.Repository
{
	public class EmailRepository : IEmailRepository
	{
		public async Task<bool> SendOrderEmail(Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("emails/orders").Path(orderId)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de pedido para o participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SendOrderServiceEmail(Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Notifications)
				.Path("emails/orders/service").Path(orderId)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o e-mail de pedido para o participante.");
			return apiReturn.GetReturnOrError();
		}
	}
}
