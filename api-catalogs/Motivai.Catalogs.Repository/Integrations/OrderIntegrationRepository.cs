using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.Models.Integrations;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Strings;

namespace Motivai.Catalogs.Repository.Integrations
{
	public class OrderIntegrationRepository : IOrderIntegrationRepository
	{
		public async Task CalculateIntegrationCart(string shippingAddressZipcode, ChildCart partnerCart)
		{
			if (!partnerCart.Products.Any(p => p.ProductType == ProductType.Produto && !p.Offline && !p.PartnerSettings.DisableCartCalculation))
			{
				return;
			}

			var products = partnerCart.Products.Where(p => p.ProductType == ProductType.Produto && !p.Offline)
					.Select(p => (dynamic) new
					{
						skuCode = p.SkuCode,
						amount = p.Quantity
					})
					.ToList();
			var result = await SendCart(partnerCart.ItemGrouperId, partnerCart.CnpjIntegration,
				partnerCart.CampaignIdIntegration, shippingAddressZipcode, products);

			if (result.IsError)
			{
				if (string.IsNullOrEmpty(result.Error))
				{
					partnerCart.SetError("Não foi possível calcular o carrinho no parceiro");
				}
				else
				{
					partnerCart.SetError(result.Error);
				}
				if (result.HasProducts())
				{
					partnerCart.Products.ForEach(item =>
					{
						var itemCalc = result.GetItemBySkuCode(item.SkuCode);
						if (itemCalc != null)
						{
							if (itemCalc.IsError)
							{
								item.SetError(itemCalc.Error);
							}
						}
					});
				}
			}
			else
			{
				if (!result.HasProducts())
				{
					partnerCart.Products.ForEach(p => p.Available = false);
				}
				else
				{
					partnerCart.ShippingCost = Amount.OfCurrency(result.ShippingCost);
					partnerCart.Products.ForEach(item =>
					{
						var itemCalc = result.GetItemBySkuCode(item.SkuCode);
						if (itemCalc == null)
						{
							item.Available = false;
						}
						else
						{
							item.SetCalculationResult(itemCalc);
						}
					});
				}
			}
		}

		public async Task<ShippingCostResult> CalculateShippingForItem(PartnerConfiguration partnerConf,
			string shippingAddressZipcode, string skuCode, int quantity)
		{
			var products = new List<dynamic>(1) {
				new {
					SkuCode = skuCode,
					Amount = quantity
				}
			};
			var result = await SendCart(partnerConf.PartnerId, partnerConf.CnpjIntegration,
					partnerConf.CampaignIntegrationId, shippingAddressZipcode, products);
			if (result.IsError)
			{
				var errorMessage = result.Error;
				if (string.IsNullOrEmpty(errorMessage))
				{
					if (result.HasProducts())
					{
						var item = result.GetItemBySkuCode(skuCode);
						errorMessage = item.Error;
					}
				}
				throw MotivaiException.of(ErrorType.Business, errorMessage);
			}
			if (!result.HasProducts())
			{
				return ShippingCostResult.OfError("Produto indisponível.");
			}
			var itemCalc = result.GetItemBySkuCode(skuCode);
			if (itemCalc == null)
				return ShippingCostResult.OfError("Produto indisponível.");
			if (itemCalc.IsError)
				return ShippingCostResult.OfError(itemCalc.Error);

			var estimatedForecast = Extractor.ExtractNumberFrom(itemCalc.DeliveryForecast);
			return new ShippingCostResult()
			{
				Prices = Amount.OfCurrency(itemCalc.TotalShippingCost),
				EstimatedDeliveryDays = estimatedForecast ?? 0
			};
		}

		private async Task<CartCalculationResult> SendCart(Guid partnerId, string partnerCnpj,
			string partnerCampaignId, string shippingAddressZipcode, List<dynamic> products)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.MarketplaceOrdersIntegrations)
				.Path("store-integrations/marketplace/orders/carts/calculate")
				.Entity(new
				{
					storeId = partnerId,
					cnpj = partnerCnpj,
					campaignId = partnerCampaignId,
					zipcode = shippingAddressZipcode,
					products
				})
				.AddXApiKeyFromProperties()
				.Timeout(30_000)
				.AsyncPost()
				.AcceptStatusCodes(400, 401, 403)
				.LogPayloadToConsole()
				.LogResponseToConsole()
				.GetApiReturn<CartCalculationResult>();

			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "CATALOGS_INTEGRATIONS_CART_CALCULATION",
					"Não foi possível calcular o carrinho no parceiro.");

			return apiReturn.GetReturnOrError();
		}
	}
}