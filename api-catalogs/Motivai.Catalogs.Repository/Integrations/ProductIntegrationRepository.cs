using System;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.Models.Integrations;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Repository.Integrations
{
	public class ProductIntegrationRepository : IProductIntegrationRepository
	{
		public async Task<AvailabilityModel> VerifyAvailability(PartnerConfiguration partnerConfig, string skuCode)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.MarketplaceOrdersIntegrations)
				.Path("store-integrations/marketplace/products/availability/verify")
				.Entity(new
				{
					storeId = partnerConfig.PartnerId,
					cnpj = partnerConfig.CnpjIntegration,
					campaignId = partnerConfig.CampaignIntegrationId,
					skuCode = skuCode
				})
				.AddXApiKeyFromProperties()
				.Timeout(30_000)
				.AsyncPost()
				.LogPayloadToConsole()
				.LogResponseToConsole()
				.AcceptStatusCodes(400, 401, 403)
				.GetApiReturn<DisponibilityResponse>();

			if (apiReturn == null || apiReturn.HasNullReturn())
			{
				LoggerFactory.GetLogger().Error("Parceiro {0} - SKU {1} - Não foi possível consultar a disponibilidade do SKU.",
						partnerConfig.PartnerIdentifier, skuCode);
				throw MotivaiException.of(ErrorType.ApiException, "CATALOGS_INTEGRATIONS_PRODUCT_AVAILABILITY",
					"Não foi possível consultar a disponibilidade do produto no parceiro.");
			}

			var result = apiReturn.GetReturnOrError();
			if (result.IsError)
			{
				LoggerFactory.GetLogger().Error("Parceiro {0} - SKU {1} - Parceiro retornou erro na disponibilidade do SKU.",
					partnerConfig.PartnerIdentifier, skuCode);
				return null;
			}

			return new AvailabilityModel()
			{
				Available = result.Product.Availability,
				PriceFrom = result.Product.GetPromotionalPrice(),
				Price = result.Product.Cost,
				Quantity = result.Product.Quantity,
			};
		}
	}
}