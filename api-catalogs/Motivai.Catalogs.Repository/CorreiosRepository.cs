﻿using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogo.Domain.Models.Correios;
using Motivai.SharedKernel.Domain.Entities.References.Correios;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogo.Repository
{
    public class CorreiosRepository : ICorreiosRepository
    {
        public async Task<CorreiosAddress> QueryCep(string cep)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Correios)
                .Path("addresses").Path(cep)
                .AsyncGet()
                .GetApiReturn<CorreiosAddress>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<CorreiosShippingResult>> CalculateShipping(string fromCep, string toCep, decimal weight, decimal height, decimal width, decimal depth)
        {
            return await CalculateShipping(fromCep, toCep, weight, height, width, depth, 0);
        }

        public Task<List<CorreiosShippingResult>> CalculateShipping(string fromCep, string toCep, decimal weight, decimal height, decimal width, decimal depth, decimal diameter)
        {
            return Task.FromResult(new List<CorreiosShippingResult>(1)
            {
                new CorreiosShippingResult()
                {
                    ServiceName = "Sedex",
                    Cost = 16,
                    EstimatedDeliveryDays = "5",
                    OccurredError = false
                }
            });
            // var apiReturn = await HttpClient.Create(MotivaiApi.Correios).Path("shipping/calculate")
            //     .Entity(new {
            //         sourceCep = fromCep,
            //         destinationCep = toCep,
            //         weight = weight,
            //         height = height,
            //         width = width,
            //         depth = depth,
            //         diameter = diameter
            //     })
            //     .AsyncPost()
            //     .GetApiReturn<List<CorreiosShippingResult>>();
            // if (apiReturn == null)
            //     throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar o cálculo de frete.");
            // return apiReturn.GetReturnOrError();
        }
    }
}
