using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Repository
{
    public class FactoriesRepository : IFactoriesRepository
    {
        public async Task<List<ProductFactory>> FindFactoriesForRegions(Guid partnerId, List<Guid> regionsIds) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Factories)
                .Path("factories/regions/match")
                .RootBu()
                .Entity(new {
                    CompaniesIds = new Guid[] { partnerId },
                    RegionsIds = regionsIds
                })
                .AsyncPost()
                .GetApiReturn<List<ProductFactory>>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<Guid>> FindFactoriesIdsForRegions(List<Guid> regionsIds) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Factories)
                .Path("factories/regions/match/ids")
                .RootBu()
                .Entity(new {
                    RegionsIds = regionsIds
                })
                .AsyncPost()
                .GetApiReturn<List<Guid>>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }
    }
}