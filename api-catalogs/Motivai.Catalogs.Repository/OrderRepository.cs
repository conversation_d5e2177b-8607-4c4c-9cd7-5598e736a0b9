﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogo.Repository
{
	public class OrderRepository : IOrderRepository
	{
		public async Task<CreatedOrder> CreateOrder(MasterOrder masterOrder)
		{
			if (masterOrder == null)
				throw MotivaiException.ofValidation("Carrinho inválido.");
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/orders")
				.Entity(masterOrder, HttpClient.JSON_SETTINGS_JAVA)
				.Timeout(60_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.GetApiReturn<CreatedOrder>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível criar o pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<MasterOrder> CreateOrderSync(MasterOrder masterOrder)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/orders/sync")
				.Entity(masterOrder, HttpClient.JSON_SETTINGS_JAVA)
				.Timeout(120_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.GetApiReturn<MasterOrder>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível criar o pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<MasterOrder> GetOrderById(Guid masterOrderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/orders").Path(masterOrderId)
				.AsyncGet()
				.GetApiReturn<MasterOrder>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<MasterOrderResumed> FetchOrderResumed(Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/orders").Path(orderId)
				.Path("paymentmethods/resumed")
				.AsyncGet()
				.GetApiReturn<MasterOrderResumed>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool?> PriceFactoryItems(FactoryOrderPrice order)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/orders").Path(order.OrderId).Path("childrenorders").Path(order.ItemGrouperId).Path("items/price")
				.Entity(new
				{
					items = order.Items
				})
				.AsyncPut()
				.GetApiReturn<bool?>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível precificar os itens do pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> ApproveOrder(Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/b2b/orders").Path(orderId).Path("approve")
				.AsyncPut()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível aprovar o pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> RefuseOrder(Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/b2b/orders").Path(orderId).Path("refuse")
				.AsyncPut()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível reprovar o pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<dynamic>> ConsultLinkVouchers(Guid orderId, Guid itemGrouperId)
		{
			var response = await HttpClient.Create(MotivaiApi.Orders)
				.Path("api/orders").Path(orderId)
				.Path("childrenorders").Path(itemGrouperId)
				.Path("vouchers")
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();

			return response.GetReturnOrError();
		}
	}
}
