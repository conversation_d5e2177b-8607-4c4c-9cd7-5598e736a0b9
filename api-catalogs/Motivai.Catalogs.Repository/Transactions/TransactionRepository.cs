using System;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.IRepository.Transactions;
using Motivai.Catalogs.Domain.Models.Transactions;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.Transactions
{
    public class TransactionRepository : ITransactionRepository
    {
        public async Task<decimal> GetBalance(Guid campaignId, Guid userId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
                .Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("balance")
                .Timeout(30_000)
                .AsyncGet()
                .GetApiReturn<decimal>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "USER_BALANCE_ERROR",
                    "Não foi possível consultar o saldo do participante.");
            if (apiReturn.HasNullReturn())
                return 0;
            return apiReturn.GetReturnOrError();
        }

        public async Task<Guid> CreateCreditTransaction(Guid campaignId, Guid userId, string mechanicDescription,
                string transactionDescrition, PointsPurchaseOrder purchaseOrder)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
                .Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("credits")
                .Entity(new
                {
                    ParticipantId = purchaseOrder.ParticipantId,
                    Origin = TransactionOrigin.BuyPoints,
                    MechanicDescription = mechanicDescription,
                    TransactionDescription = transactionDescrition,
                    PointsConversionFactor = purchaseOrder.PointsConversionFactor,
                    TotalAmount = new
                    {
                        Currency = purchaseOrder.CurrencyValue,
                        Points = purchaseOrder.PointsToBuy,
                    },
                    ExpirationDate = DateTime.Now.AddDays(720),
                    PaymentId = purchaseOrder.PaymentId
                })
                .Timeout(60_000)
                .AsyncPost()
                .GetApiReturn<Guid>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "USER_BALANCE_CREDIT_ERROR",
                    "Não foi possível efetuar o crédito do saldo.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<Guid> CreateDebitTransaction(Guid campaignId, Guid userId, Guid participantId,
                TransactionOrigin transactionOrigin, Guid originId, string transactionDescrition, Amount amountToDebt)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
                .Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("debits")
                .Entity(new
                {
                    CampaignId = campaignId,
                    UserId = userId,
                    ParticipantId = participantId,
                    Origin = transactionOrigin,
                    OriginId = originId,
                    Description = transactionDescrition,
                    Total = amountToDebt,
                })
                .Timeout(60_000)
                .AsyncPost()
                .GetApiReturn<Guid>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.of(ErrorType.ApiException, "USER_BALANCE_DEBIT_ERROR",
                    "Não foi possível efetuar o débito do saldo.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<TransactionRefundReceipt> RefundTransaction(Guid campaignId, Guid userId, Guid debitTransactionId,
                TransactionOrigin transactionOrigin, Guid originId, string refundReason, Guid? traceId = null)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
                .Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("refunds")
                .Entity(new
                {
                    traceId = traceId ?? originId,
                    transactionId = debitTransactionId,
                    Origin = transactionOrigin,
                    OriginId = originId,
                    refundReason = refundReason,
                })
                .AsyncPost()
                .GetApiReturn<TransactionRefundReceipt>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.of(ErrorType.ApiException, "USER_BALANCE_REFUND_ERROR",
                    "Não foi possível efetuar o estorno do saldo.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<TransactionRefundReceipt> RefundTransactionById(Guid campaignId, Guid userId,
                Guid debitTransactionId, Guid originId, TransactionOrigin origin, string refundReason)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
                .Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("refunds").Path(debitTransactionId)
                .Entity(new
                {
                    userId = userId,
                    transactionId = debitTransactionId,
                    originId = originId,
                    refundReason = refundReason,
                    origin = origin
                })
                .AsyncPost()
                .LogPayloadToLogger()
                .LogResponseToLogger()
                .GetApiReturn<TransactionRefundReceipt>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.of(ErrorType.ApiException, "TRANSCATION_REFUND_ERROR",
                    "Não foi possível efetuar o estornar a transção.");
            return apiReturn.GetReturnOrError();
        }
    }
}
