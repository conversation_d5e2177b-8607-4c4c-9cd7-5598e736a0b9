using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices.BillPayments;
using Motivai.Catalogs.Domain.Models.Integrations.BillPayment;
using Motivai.Catalogs.Domain.Models.Integrations.MobileRecharge;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.ExtraServices
{
	public class CatalogExtraServiceRepository : ICatalogExtraServiceRepository
	{
		#region Recarga de Celular

		private async Task<BaseResult<List<PartnerOperator>>> GetAvailableOperatorsForDdd(Guid campaignId, MobilePartner mobilePartner, string ddd)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/credits/campaigns").Path(campaignId)
				.Path("partners").Path(mobilePartner.ToString()).Path("providers/ddds").Path(ddd)
				.AddXApiKeyFromProperties()
				.Timeout(20_000)
				.AsyncGet()
				.GetApiReturn<BaseResult<List<PartnerOperator>>>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "MOBILE_RECHARGE_DDD_OPERATORS_QUERY_ERROR",
					"Não foi possível consultar as operadoras para recarga, por favor, tente novamente.");
			}
			if (apiReturn.HasNullReturn()) return null;
			var operatorResult = apiReturn.GetReturnOrError();
			if (!operatorResult.IsSuccessful())
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_DDD_OPERATORS_QUERY_ERROR", operatorResult.ErrorMessage);
			return operatorResult;
		}

		public async Task<List<PartnerOperator>> GetOperatorsForMobilePartner(Guid campaignId, MobilePartner mobilePartner, string ddd)
		{
			var operatorResult = await GetAvailableOperatorsForDdd(campaignId, mobilePartner, ddd);
			if (!operatorResult.HasReturn()) return null;
			return operatorResult.PartnerReturn.Where(r => r.OnlyRechargeMobile()).ToList();
		}

		public async Task<List<PartnerOperationCost>> GetPartnerOptionsCostsForDdd(Guid campaignId, MobilePartner mobilePartner, string ddd, string operatorId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/credits/campaigns").Path(campaignId)
				.Path("partners").Path(mobilePartner.ToString())
				.Path("providers").Path(operatorId).Path("costs/ddds").Path(ddd)
				.AddXApiKeyFromProperties()
				.Timeout(20_000)
				.AsyncGet()
				.GetApiReturn<BaseResult<List<PartnerOperationCost>>>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "MOBILE_RECHARGE_OPERATOR_OPTIONS_QUERY_ERROR",
					"Não foi possível consultar opções de recarga, por favor, tente novamente.");
			}
			if (apiReturn.HasNullReturn()) return null;
			var operatorResult = apiReturn.GetReturnOrError();
			if (!operatorResult.IsSuccessful())
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_OPERATOR_OPTIONS_QUERY_ERROR", operatorResult.ErrorMessage);
			return operatorResult.PartnerReturn;
		}

		public async Task<ExtraServiceOperationResult> StartMobileRecharge(Guid campaignId, Guid userId, MobilePartner mobilePartner, MobileRechargeIssue rechargeIssue)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/credits/users").Path(userId).Path("campaigns").Path(campaignId)
				.Path("partners").Path(mobilePartner.ToString()).Path("providers").Path(rechargeIssue.OperatorId).Path("recharge")
				.Entity(PartnerMobileRechargeIssue.Of(rechargeIssue))
				.AddXApiKeyFromProperties()
				.Timeout(60_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<BaseResult<ExtraServiceOperationResult>>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "MOBILE_RECHARGE_CREATION_ERROR",
					"Não foi possível efetuar a operação, por favor, tente novamente.");
			}
			if (apiReturn.HasNullReturn()) return null;
			var operatorResult = apiReturn.GetReturnOrError();
			if (!operatorResult.IsSuccessful())
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_CREATION_ERROR", operatorResult.ErrorMessage);
			operatorResult.PartnerReturn.RequiresConfirmation = operatorResult.RequiresConfirmation;
			return operatorResult.PartnerReturn;
		}

		public async Task<ExtraServiceConfirmationResult> ConfirmMobileRecharge(Guid campaignId, Guid userId, MobilePartner mobilePartner, MobileRechargeTicket ticket)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/credits/users").Path(userId).Path("campaigns").Path(campaignId)
				.Path("partners").Path(mobilePartner.ToString()).Path("recharge/confirm")
				.Entity(new
				{
					confirm = ticket.Confirm,
					protocol = ticket.Protocol,
					catalogExtraServiceId = ticket.CatalogExtraServiceId,
					locationInfo = ticket.LocationInfo
				})
				.AddXApiKeyFromProperties()
				.Timeout(60_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<BaseResult<ExtraServiceConfirmationResult>>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "MOBILE_RECHARGE_CONFIRMATION_ERROR",
						"Não foi possível confirmar a recarga, por favor, tente novamente.");
			}
			if (apiReturn.HasNullReturn()) return null;
			var operatorResult = apiReturn.GetReturnOrError();
			if (!operatorResult.IsSuccessful())
				throw MotivaiException.ofValidation("MOBILE_RECHARGE_CONFIRMATION_ERROR", operatorResult.ErrorMessage);
			return operatorResult.PartnerReturn;
		}

		public async Task<dynamic> GetRechargeOrderById(Guid campaignId, Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CampaignsExtraServices)
				.Path("campaigns").Path(campaignId).Path("services/orders").Path(orderId)
				.AddXApiKeyFromProperties()
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
			{
				throw MotivaiException.ofValidation("Não foi possivel encontrar o pedido especificado");
			}

			return apiReturn.GetReturnOrError();
		}

		#endregion

		#region Pague Contas

		public async Task<BillDetails> GetBillDetails(Guid campaignId, BillPaymentPartner partner, string barCode)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/billpayments/campaigns").Path(campaignId)
				.Path("partners").Path(partner.ToString()).Path("barcodes").Path(barCode).Path("validate")
				.AddXApiKeyFromProperties()
				.Timeout(40_000)
				.AsyncGet()
				.LogResponseToLogger()
				.GetApiReturn<BaseResult<dynamic>>();

			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "BILL_PAYMENT_BARCODE_QUERY_ERROR",
					"Não foi possível carregar os detalhes da conta para pagamento.");
			}

			if (apiReturn.HasNullReturn())
				return null;

			var operatorResult = apiReturn.GetReturnOrError();

			if (!operatorResult.IsSuccessful())
			{
				throw MotivaiException.ofValidation("BILL_PAYMENT_BARCODE_QUERY_ERROR", operatorResult.ErrorMessage);
			}

			var bill = operatorResult.PartnerReturn;
			if (bill == null) return null;
			return new BillDetails()
			{
				Assignor = bill.assignor,
				BarCode = bill.barcode,
				BillingAmount = bill.paymentCost,
				DueDate = bill.maturityDate,
				ServiceType = bill.serviceType,
				RegisterData = bill.registerData != null ? new RegisterData(bill.registerData) : null,
				SettleDate = bill.settleDate,
				NextSettle = bill.nextSettle,
				PartnerCorrelationId = bill.partnerCorrelationId ?? 0
			};
		}

		public async Task<ExtraServiceOperationResult> StartBillPayment(Guid campaignId, Guid userId, BillPaymentPartner partner, BillDetails bill)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/billpayments/users").Path(userId).Path("campaigns").Path(campaignId)
				.Path("partners").Path(partner.ToString()).Path("payment")
				.Entity(PartnerBillPaymentIssue.Of(bill))
				.AddXApiKeyFromProperties()
				.AsyncPost()
				.Timeout(60_000)
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<BaseResult<ExtraServiceOperationResult>>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "BILL_PAYMENT_CREATION_ERROR",
					"Não foi possível efetuar a operação, por favor, tente novamente.");
			}
			if (apiReturn.HasNullReturn()) return null;
			var operatorResult = apiReturn.GetReturnOrError();
			if (operatorResult == null)
				throw MotivaiException.ofValidation("BILL_PAYMENT_CREATION_ERROR", "Não foi possível iniciar o pagamento da conta, por favor, tente novamente.");
			if (!operatorResult.IsSuccessful())
				throw MotivaiException.ofValidation("BILL_PAYMENT_CREATION_ERROR", operatorResult.ErrorMessage);
			if (operatorResult.PartnerReturn != null)
			{
				operatorResult.PartnerReturn.RequiresConfirmation = operatorResult.RequiresConfirmation;
			}
			return operatorResult.PartnerReturn;
		}

		public async Task<ExtraServiceConfirmationResult> ConfirmBillPayment(Guid campaignId, Guid userId, BillPaymentPartner partner, BillPaymentTicket ticket)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CatalogServicesIntegrations)
				.Path("integrations/billpayments/users").Path(userId).Path("campaigns").Path(campaignId)
				.Path("partners").Path(partner.ToString()).Path("payment/confirm")
				.Entity(new
				{
					confirm = ticket.Confirm,
					protocol = ticket.Protocol,
					catalogExtraServiceId = ticket.CatalogExtraServiceId,
					locationInfo = ticket.LocationInfo
				})
				.AddXApiKeyFromProperties()
				.AsyncPost()
				.Timeout(60_000)
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<BaseResult<ExtraServiceConfirmationResult>>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "BILL_PAYMENT_CONFIRMATION_ERROR",
					"Não foi possível efetuar a operação, por favor, tente novamente.");
			}
			if (apiReturn.HasNullReturn()) return null;
			var operatorResult = apiReturn.GetReturnOrError();
			if (!operatorResult.IsSuccessful())
				throw MotivaiException.ofValidation("BILL_PAYMENT_CONFIRMATION_ERROR", operatorResult.ErrorMessage);
			return operatorResult.PartnerReturn;
		}

		public async Task<ScheduledPaymentReceipt> ScheduleBillPayment(Guid campaignId, BillDetails billDetails)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CampaignsExtraServices)
				.Path("campaigns").Path(campaignId).Path("services/billpayments/schedulings")
				.Entity(BillPaymentScheduling.Of(billDetails))
				.AddXApiKeyFromProperties()
				.AsyncPost()
				.Timeout(60_000)
				.LogPayloadToLogger()
				.GetApiReturn<ScheduledPaymentReceipt>();
			if (apiReturn == null)
			{
				throw MotivaiException.of(ErrorType.ApiException, "BILL_PAYMENT_SCHEDULING_ERROR",
					"Não foi possível efetuar o agendamento, por favor, tente novamente.");
			}
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetBillPaymentsOrderById(Guid campaignId, Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.CampaignsExtraServices)
				.Path("campaigns").Path(campaignId).Path("services/orders").Path(orderId)
				.AddXApiKeyFromProperties()
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
			{
				throw MotivaiException.ofValidation("Não foi possivel encontrar o pedido especificado");
			}

			return apiReturn.GetReturnOrError();
		}

		#endregion
	}
}