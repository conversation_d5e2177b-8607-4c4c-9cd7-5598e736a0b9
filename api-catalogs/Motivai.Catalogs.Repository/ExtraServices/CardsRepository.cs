using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.ExtraServices {
    public class CardsRepository : ICardsRepository {
        public async Task<List<dynamic>> GetParticipantActiveCards(Guid campaignId, Guid userId, PrepaidCardType? cardType) {
            var apiReturn = await HttpClient.Create(MotivaiApi.PrepaidCards)
                .Path("cards/prepaid/participants").Path(userId)
                .Path("active")
                .Query("campaignId", campaignId)
                .Query("type", cardType.HasValue ? cardType.Value.ToString() : null)
                .AsyncGet()
                .GetApiReturn<List<dynamic>>();

            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível carregar os cartões pré-pagos, por favor, tente novamente.");

            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> CreateCardOrder(dynamic order) {
            var apiReturn = await HttpClient.Create(MotivaiApi.PrepaidCards)
                .Path("cards/prepaid/orders")
                .Entity(order)
                .AsyncPost()
                .LogPayloadToLogger()
                .GetApiReturn<dynamic>();
            if (apiReturn == null || apiReturn.HasNullReturn())
                throw MotivaiException.ofValidation("Não foi possível efetuar o pedido, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<CardOrder> GetCardOrderById(Guid orderId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.PrepaidCards)
                .Path("cards/prepaid/orders").Path(orderId)
                .AsyncGet()
                .GetApiReturn<CardOrder>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível carregar o pedido, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }
    }
}