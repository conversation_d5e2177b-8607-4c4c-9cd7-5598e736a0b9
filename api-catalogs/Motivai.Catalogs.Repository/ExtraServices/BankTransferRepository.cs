using System;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.ExtraServices
{
	public class BankTransferRepository : IBankTransferRepository
	{
		public async Task<dynamic> CreateBankTransferOrder(dynamic order)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.BankTransfers)
				.Path("banktransfer/orders")
				.Entity(order)
				.AsyncPost()
				.LogPayloadToLogger()
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível efetuar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetBankTransferOrderById(Guid orderId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.BankTransfers)
				.Path("banktransfer/orders").Path(orderId)
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível carregar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}
	}
}