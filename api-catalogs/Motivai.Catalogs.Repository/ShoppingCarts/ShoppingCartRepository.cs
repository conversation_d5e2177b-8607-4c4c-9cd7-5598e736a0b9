using System;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.IRepository.ShoppingCarts;
using Motivai.SharedKernel.Helpers;

using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace Motivai.Catalogs.Repository.ShoppingCarts
{
    public class ShoppingCartRepository : IShoppingCartRepository
    {
        private readonly IMongoCollection<ShoppingCart> cartsCollection;

        public ShoppingCartRepository()
        {
            this.cartsCollection = MongoDataBaseHelper.GetMongoDatabase()
                .GetCollection<ShoppingCart>("ShoppingCarts");
        }

        private async Task Create(ShoppingCart shoppingCart)
        {
            shoppingCart.Id = Guid.NewGuid();
            await cartsCollection.InsertOneAsync(shoppingCart);
        }

        private async Task Update(ShoppingCart shoppingCart)
        {
            await cartsCollection.ReplaceOneAsync(c => c.Id == shoppingCart.Id && c.Status == ShoppingCartStatus.OPEN, shoppingCart);
        }

        public async Task<ShoppingCart> GetParticipantShoppingCart(Guid campaignId, Guid userId)
        {
            return await cartsCollection.AsQueryable()
                .Where(c => c.CampaignId == campaignId && c.UserId == userId && c.Status == ShoppingCartStatus.OPEN)
                .FirstOrDefaultAsync();
        }

        public async Task Save(ShoppingCart shoppingCart)
        {
            shoppingCart.LastAccessDate = DateTime.UtcNow;

            if (shoppingCart.Id == Guid.Empty)
            {
                await Create(shoppingCart);
            }
            else
            {
                await Update(shoppingCart);
            }
        }

        public async Task<bool> SetCreatingOrder(Guid campaignId, Guid userId)
        {
            var filter = Builders<ShoppingCart>.Filter
                .Where(c => c.CampaignId == campaignId && c.UserId == userId && c.Status == ShoppingCartStatus.OPEN);
            var update = Builders<ShoppingCart>.Update
                .Set(c => c.Status, ShoppingCartStatus.CREATING_ORDER)
                .CurrentDate(c => c.OrderDate);
            var updateResult = await cartsCollection.UpdateOneAsync(filter, update);
            return MongoDataBaseHelper.WasUpdated(updateResult);
        }

        public async Task<bool> SetCartOrdered(Guid campaignId, Guid userId, Guid orderId, string orderNumber)
        {
            var filter = Builders<ShoppingCart>.Filter
                .Where(c => c.CampaignId == campaignId && c.UserId == userId && c.Status == ShoppingCartStatus.OPEN);
            var update = Builders<ShoppingCart>.Update
                .Set(c => c.Status, ShoppingCartStatus.ORDERED)
                .Set(c => c.OrderId, orderId)
                .Set(c => c.OrderNumber, orderNumber)
                .CurrentDate(c => c.OrderDate);
            var updateResult = await cartsCollection.UpdateOneAsync(filter, update);
            return MongoDataBaseHelper.WasUpdated(updateResult);
        }

        public async Task<int> CountSkuGroupStock(Guid campaignId, Guid participantGroupId, Guid skuId)
        {
            var lowerDate = DateTime.UtcNow.AddMinutes(-15);

            return await cartsCollection.AsQueryable()
                .Where(c => c.CampaignId == campaignId && c.Status == ShoppingCartStatus.OPEN && c.LastAccessDate > lowerDate
                        && c.Items.Any(i => i.StockParticipantGroupId == participantGroupId && i.SkuId == skuId))
                .SelectMany(c => c.Items)
                .Where(i => i.StockParticipantGroupId == participantGroupId && i.SkuId == skuId)
                .SumAsync(c => c.Quantity);
        }
    }
}