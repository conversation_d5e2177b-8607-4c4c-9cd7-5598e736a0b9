using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.IRepository.PaymentGateway;
using Motivai.Catalogs.Domain.Models.PaymentGateway;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Enums.Payments;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.PaymentGateway
{
    public class PaymentGatewayRepository : IPaymentGatewayRepository
    {
        public async Task<GatewayOperationResult> ChargePaymentOrder(PaymentGatewaySettings paymentGateway, PointsPurchaseOrder purchaseOrder, string issueDescription,
                StoreAmount amountSplit, DetailedPaymentInstallment details)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.ApiPaymentsGateway)
                .Path("payments/orders/credit-cards/charge")
                .Entity(CreateModelToPayment(paymentGateway, purchaseOrder, issueDescription, amountSplit, details))
                .Timeout(60_000)
                .AsyncPost()
                .LogPayloadToConsole()
                .LogResponseToConsole()
                .GetApiReturn<GatewayOperationResult>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar o pedido de compra de pontos para processamento.");
            return apiReturn.GetReturnOrError();
        }

        private dynamic CreateModelToPayment(PaymentGatewaySettings paymentGateway, PointsPurchaseOrder purchaseOrder, string issueDescription,
                StoreAmount amountSplit, DetailedPaymentInstallment details)
        {
            return new
            {
                referenceId = purchaseOrder.ReferenceId,
                orderNumber = purchaseOrder.OrderNumber,
                items = new List<dynamic>(1)
                {
                    new
                    {
                        code = "001",
                        description = "Compra de Pontos",
                        quantity = 1,
                        unitPrice = (int)(amountSplit.Total * 100m)
                    }
                },
                totalAmount = (int)(amountSplit.Total * 100m),
                payment = new
                {
                    paymentMethod = PaymentMethod.CREDIT_CARD,
                    invoiceDescription = issueDescription,
                    creditCard = new
                    {
                        cardToken = purchaseOrder.CardToken,
                        installments = purchaseOrder.Installment.Count
                    },
                    splitsConfiguration = amountSplit.Splits,
                    gateway = new
                    {
                        gateway = "PAGARME",
                        privateKey = paymentGateway.PrivateKey,
                        enableAntifraud = purchaseOrder.EnableAntiFraud,
                    }
                },
                customer = new
                {
                    id = purchaseOrder.UserId,
                    document = purchaseOrder.Cpf,
                    name = purchaseOrder.Name,
                    email = purchaseOrder.Email,
                    mobilePhone = purchaseOrder.Phone,
                    address = new
                    {
                        cep = purchaseOrder.BillingAddress.Cep,
                        street = purchaseOrder.BillingAddress.Street,
                        number = purchaseOrder.BillingAddress.Number,
                        complement = purchaseOrder.BillingAddress.Complement,
                        neighborhood = purchaseOrder.BillingAddress.Neighborhood,
                        city = purchaseOrder.BillingAddress.City,
                        reference = purchaseOrder.BillingAddress.Reference,
                        state = purchaseOrder.BillingAddress.State,
                        uf = purchaseOrder.BillingAddress.Uf
                    }
                },
                metadata = new
                {
                    campaignId = purchaseOrder.CampaignId,
                    paymentNature = "Compra de Pontos",
                    userId = purchaseOrder.UserId,
                    participantId = purchaseOrder.ParticipantId,
                    accountOperatorId = purchaseOrder.AccountOperator?.AccountOperatorId,
                    accountOperatorLoginId = purchaseOrder.AccountOperator?.AccountOperatorLoginId,
                    accountOperatorDocument = purchaseOrder.AccountOperator?.AccountOperatorDocument,
                    accountOperatorEmail = purchaseOrder.AccountOperator?.AccountOperatorEmail,
                    conversionFactor = purchaseOrder.PointsConversionFactor.ToString(),
                    transactionFee = paymentGateway.TransactionCurrencyValue.ToString(),
                    antifraudFee = paymentGateway.AntiFraudTransactionCurrencyValue.ToString(),
                    installmentFee = paymentGateway.GetInstallmentFee(purchaseOrder.Installment.Count).ToString(),
                    governmentFee = paymentGateway.GovernmentFee.ToString(),
                    bankTransferFee = paymentGateway.BankTransferFee.ToString(),
                    anticipationFee = paymentGateway.AntiFraudFee.ToString(),
                    additionalFee = paymentGateway.AdditionalFee.ToString(),
                },
                locationInfo = purchaseOrder.LocationInfo
            };
        }
    }
}