using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Elasticsearch;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.Model.Structures;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogo.Repository
{
    public class ProductCatalogRepository : IProductCatalogRepository
    {
        private static async Task<ElasticsearchReturn<ElasticsearchProduct>> SearchProducts(ElasticsearchProductsParameters parameters)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Elasticsearch)
                .Path("products")
                .Entity(parameters)
                .AsyncPost()
                .Timeout(60_000)
                // .LogPayloadToLogger()
                // .LogResponseToConsole()
                .GetApiReturn<ElasticsearchReturn<ElasticsearchProduct>>();

            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar a pesquisa de produtos.");

            return apiReturn.GetReturnOrError();
        }

        private static ProductShowcaseModel CreateProductShowcaseModelFromMainSku(ElasticsearchProduct product)
        {
            return CreateProductShowcaseModelFromSku(product, product.GetMasterActiveOrFirstActive());
        }

        private static ProductShowcaseModel CreateProductShowcaseModelFromMainSku(ElasticsearchProduct product, bool? asc)
        {
            return CreateProductShowcaseModelFromSku(product, product.GetMasterActiveOrFirstActiveSortingByPrice(asc));
        }

        private static ProductShowcaseModel CreateProductShowcaseModelFromMainSku(ElasticsearchProduct product, List<Guid> skusIds = null)
        {
            var sku = product.Skus.FirstOrDefault(s => skusIds.Any(id => id.ToString() == s.Id));
            return CreateProductShowcaseModelFromSku(product, sku);
        }

        private static ProductShowcaseModel CreateProductShowcaseModelFromMainSku(ElasticsearchProduct product, List<PartnerSku> partnersSkus = null)
        {
            var sku = product.Skus.FirstOrDefault(s => partnersSkus.Any(id => id.ToString() == s.Id));
            return CreateProductShowcaseModelFromSku(product, sku);
        }

        private static ProductShowcaseModel CreateProductShowcaseModelFromSku(ElasticsearchProduct p, Sku sku)
        {
            if (sku == null)
            {
                sku = p.GetMasterActiveOrFirstActive();
            }
            var prod = new ProductShowcaseModel
            {
                Id = p.ElasticsearchId,
                ProductId = Guid.Parse(p.Id),
                Name = p.Name.FixChars(),
                ProcessType = EnumHelper<ProcessType>.Parse(p.ProcessType),
                ProductType = EnumHelper<ProductType>.Parse(p.ProductType),
                PartnerId = string.IsNullOrEmpty(p.PartnerId) ? Guid.Empty : Guid.Parse(p.PartnerId),
                PartnerName = p.Partner,
                PartnerPrices = sku.Price,
                Rankings = p.Rankings,
                DepartmentId = string.IsNullOrEmpty(p.DepartmentId) ? Guid.Empty : Guid.Parse(p.DepartmentId),
                CategoryId = string.IsNullOrEmpty(p.CategoryId) ? Guid.Empty : Guid.Parse(p.CategoryId),
                SubcategoryId = string.IsNullOrEmpty(p.SubcategoryId) ? null : (Guid?)Guid.Parse(p.SubcategoryId),
                SkuId = Guid.Parse(sku.Id),
                SkuCode = sku.Code,
                DynamicPriceDescription = sku.Price.DynamicPrice ? (p.CatalogSettings?.DynamicPriceDescription ?? "Preço variável") : null,
                ProductModelLabel = p.CatalogSettings?.ProductModelLabel,
                ProductColorLabel = p.CatalogSettings?.ProductColorLabel,
                ProductInformationTabTitle = p.CatalogSettings?.ProductInformationTabTitle,
                ProductSizeLabel = p.CatalogSettings?.ProductSizeLabel,
                ProductTechnicalSpecificationsTabTitle = p.CatalogSettings?.ProductTechnicalSpecificationsTabTitle,
                ProductVoltageLabel = p.CatalogSettings?.ProductVoltageLabel,
                Available = p.IsActive(sku),
                CustomAttributes = sku.CustomAttributes,
            };
            if (sku.Attributes != null)
            {
                prod.SkuModel = sku.Attributes.Model;
                prod.SkuSize = sku.Attributes.Size;
                prod.SkuColor = sku.Attributes.Color;
                prod.SkuVoltage = sku.Attributes.Voltage;
            };

            var principalImage = sku.GetMasterImage();
            if (principalImage != null)
            {
                prod.ImageUrl = principalImage.GetLargest();
            }
            if (prod.ImageUrl == null && sku.HasImages())
            {
                prod.ImageUrl = sku.Images.Where(i => i != null && !i.IsMasterImage()).Select(i => i.GetLargest()).FirstOrDefault();
            }
            return prod;
        }

        private static List<Item> CreateItemFromAggregation(dynamic aggregations)
        {
            return (ElasticsearchHelper.AggregationToList(aggregations) as List<Entry<string, int>>)
                .Where(entry => !string.IsNullOrEmpty(entry.Key))
                .Select(entry => Item.Of(entry.Key, entry.Value))
                .ToList();
        }

        public async Task<List<ProductShowcaseModel>> GetProducts(Guid campaignId, Guid userId, Guid participantId,
                Guid? departmentId, RegionFilter regionFilter, int from, int size,
                decimal? fromCurrency = default(decimal?), decimal? toCurrency = default(decimal?))
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                KD = departmentId != null && departmentId.HasValue ? departmentId.Value.ToString() : null,
                FR = from,
                TK = size,
                FI = ElasticsearchProductsParameters.PRODUCTS_LIST_FIELDS,
                SF = fromCurrency.HasValue ? fromCurrency.Value.ToString() : null,
                ST = toCurrency.HasValue ? toCurrency.Value.ToString() : null
            };
            if (regionFilter != null)
            {
                parameters.LR = ElasticsearchProductsParameters.JoinValues(regionFilter.RegionsIds);
                parameters.LFD = ElasticsearchProductsParameters.JoinValues(regionFilter.FactoriesIds);
            }
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult != null && elasticResult.HasHits())
            {
                return elasticResult.Hits.Select(CreateProductShowcaseModelFromMainSku).ToList();
            }
            return null;
        }

        public async Task<List<ProductShowcaseModel>> GetProductsByCategory(Guid campaignId, Guid userId, Guid participantId,
                Guid categoryId, int from = 0, int size = 8)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                KT = categoryId.ToString(),
                FR = from,
                TK = size,
                FI = ElasticsearchProductsParameters.PRODUCTS_LIST_FIELDS
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult != null && elasticResult.HasHits())
            {
                return elasticResult.Hits.Select(CreateProductShowcaseModelFromMainSku).ToList();
            }
            return null;
        }

        public async Task<List<ProductShowcaseModel>> GetProductsBySkusIds(Guid campaignId, Guid userId, Guid participantId,
                List<Guid> skusIds, RegionFilter regionFilter = null, bool evenInactives = false)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId == Guid.Empty ? "" : participantId.ToString(),
                LKS = ElasticsearchProductsParameters.JoinValues(skusIds),
                FI = ElasticsearchProductsParameters.PRODUCTS_LIST_FIELDS,
                FR = 0,
                ALL = evenInactives,
                TK = skusIds.Count
            };
            if (regionFilter != null)
            {
                parameters.LR = ElasticsearchProductsParameters.JoinValues(regionFilter.RegionsIds);
                parameters.LFD = ElasticsearchProductsParameters.JoinValues(regionFilter.FactoriesIds);
            }
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult != null && elasticResult.HasHits())
            {
                return elasticResult.Hits.Select(p => CreateProductShowcaseModelFromMainSku(p, skusIds)).ToList();
            }
            return null;
        }

        public async Task<List<ProductShowcaseModel>> GetProductsByPartnersAndSkuCodes(Guid campaignId, Guid userId, Guid participantId,
            List<PartnerSku> featuredSkus, RegionFilter regionFilter, bool evenInactives = false)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId == Guid.Empty ? "" : participantId.ToString(),
                LPSC = featuredSkus.Select(ps => ElasticsearchPartnerSkuParameter.OfPartnerAndSkuCode(ps.PartnerId, ps.SkuCode)).ToList(),
                FI = ElasticsearchProductsParameters.PRODUCTS_LIST_FIELDS,
                FR = 0,
                ALL = evenInactives,
                // TK = featuredSkus.Count
            };
            if (regionFilter != null)
            {
                parameters.LR = ElasticsearchProductsParameters.JoinValues(regionFilter.RegionsIds);
                parameters.LFD = ElasticsearchProductsParameters.JoinValues(regionFilter.FactoriesIds);
            }
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult != null && elasticResult.HasHits())
            {
                return elasticResult.Hits
                    .Select(p =>
                    {
                        var sku = p.Skus.FirstOrDefault(s => featuredSkus.Any(ps => ps.PartnerId.ToString() == p.PartnerId && ps.SkuCode == s.Code));
                        return CreateProductShowcaseModelFromSku(p, sku);
                    })
                    .ToList();
            }
            return null;
        }

        public async Task<SearchModel> SearchProducts(Guid campaignId, Guid userId, Guid participantId, Guid? departmentId, string query, string[] partners = null,
                string[] departments = null, string[] categories = null, string[] subcategories = null, string[] manufacturers = null,
                string[] colors = null, string[] voltagens = null, string sortBy = null, bool? asc = null, string nestedPath = null, decimal? fromSaleValue = null, decimal? toSaleValue = null,
                RegionFilter regionFilter = null, int from = 0, int size = 8, string[] productType = null)
        {

            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                KD = departmentId.HasValue ? departmentId.Value.ToString() : null,
                Q = query,
                SF = fromSaleValue.HasValue && fromSaleValue.Value >= 0 ? fromSaleValue.Value.ToString() : null,
                ST = toSaleValue.HasValue && toSaleValue.Value > 0 ? toSaleValue.Value.ToString() : null,
                FI = ElasticsearchProductsParameters.PRODUCTS_LIST_FIELDS,
                OB = sortBy,
                FR = from,
                TK = size,
                ASC = asc ?? false,
                NOB = nestedPath
            };
            if (regionFilter != null)
            {
                parameters.LR = ElasticsearchProductsParameters.JoinValues(regionFilter.RegionsIds);
                parameters.LFD = ElasticsearchProductsParameters.JoinValues(regionFilter.FactoriesIds);
            }
            if (partners != null && partners.Length > 0)
                parameters.P = ElasticsearchProductsParameters.JoinValues(partners);
            if (departments != null && departments.Length > 0)
                parameters.D = ElasticsearchProductsParameters.JoinValues(departments);
            if (categories != null && categories.Length > 0)
                parameters.C = ElasticsearchProductsParameters.JoinValues(categories);
            if (subcategories != null && subcategories.Length > 0)
                parameters.S = ElasticsearchProductsParameters.JoinValues(subcategories);
            if (manufacturers != null && manufacturers.Length > 0)
                parameters.M = ElasticsearchProductsParameters.JoinValues(manufacturers);
            if (colors != null && colors.Length > 0)
                parameters.AC = ElasticsearchProductsParameters.JoinValues(colors);
            if (voltagens != null && voltagens.Length > 0)
                parameters.AV = ElasticsearchProductsParameters.JoinValues(voltagens);
            if (productType != null && productType.Length > 0)
            {
                parameters.PRT = ElasticsearchProductsParameters.JoinValues(productType);
            }

            var elasticResult = await SearchProducts(parameters);

            if (elasticResult != null && elasticResult.HasHits())
            {
                return new SearchModel()
                {
                    Result = elasticResult.Hits.Select(p => CreateProductShowcaseModelFromMainSku(p, asc)).ToList(),
                    Total = (int)elasticResult.Total,
                    Partners = CreateItemFromAggregation(elasticResult.Aggregations.partners),
                    Departments = CreateItemFromAggregation(elasticResult.Aggregations.departments),
                    Categories = CreateItemFromAggregation(elasticResult.Aggregations.categories),
                    Subcategories = CreateItemFromAggregation(elasticResult.Aggregations.subcategories),
                    Manufacturers = CreateItemFromAggregation(elasticResult.Aggregations.manufacturers),
                    Colors = CreateItemFromAggregation(elasticResult.Aggregations.colors),
                    Voltages = CreateItemFromAggregation(elasticResult.Aggregations.voltages)
                };
            }
            return new SearchModel();
        }

        public async Task<List<ProductShowcaseModel>> SearchProductsByTermOrSkuCode(Guid campaignId, Guid userId, Guid participantId, string skuCode, string term,
                Guid? factoryId, RegionFilter regionFilter, int from = 0, int size = 8)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                Q = term,
                SC = skuCode,
                LFD = factoryId?.ToString(),
                FI = ElasticsearchProductsParameters.PRODUCTS_LIST_FIELDS,
                FR = from,
                TK = size
            };
            if (regionFilter != null)
            {
                parameters.LR = ElasticsearchProductsParameters.JoinValues(regionFilter.RegionsIds);
                if (regionFilter.HasFactories() && !factoryId.HasValue)
                {
                    parameters.LFD = ElasticsearchProductsParameters.JoinValues(regionFilter.FactoriesIds);
                }
            }
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult == null || !elasticResult.HasHits() || elasticResult.Hits.Count == 0)
                return null;
            return elasticResult.Hits
                .SelectMany(product =>
                {
                    if (!string.IsNullOrEmpty(skuCode))
                    {
                        product.Skus = product.Skus.Where(k => k.Code == skuCode).ToList();
                    }
                    return product.Skus.Select(sku => CreateProductShowcaseModelFromSku(product, sku));
                })
                .ToList();
        }

        public async Task<ElasticsearchProduct> GetProductByElasticId(Guid campaignId, Guid userId, Guid participantId, string elasticId,
                bool? evenInactive = false)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                EI = elasticId,
                TK = 1,
                ALL = evenInactive != null && evenInactive.HasValue & evenInactive.Value
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult == null || !elasticResult.HasHits() || elasticResult.Hits.Count == 0)
                return null;
            return elasticResult.Hits.FirstOrDefault();
        }

        public async Task<List<ElasticsearchProduct>> GetAllProductsBySkuCode(Guid campaignId, Guid userId, Guid participantId,
                string skuCode, bool takeOnlySameSku = true)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                SC = skuCode,
                FI = "id|elasticsearchId|buid|name|productType|registerType|processType|layoutType|active|partnerId|partner|partnerIdentifierCode|manufacturerId|departmentId" +
                "|categoryId|subcategoryId|skus.id|skus.code|skus.ean|skus.price|skus.active|skus.activePartner|skus.stock|skus.isMaster|skus.attributes|catalogSettings",
                TK = 100,
                ALL = true
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult == null || !elasticResult.HasHits() || elasticResult.Hits.Count == 0)
                return null;
            var query = elasticResult.Hits
                .Where(s => s.Skus != null && s.Skus.Any(k => k.Code == skuCode));
            if (takeOnlySameSku)
            {
                query = query.Select(s =>
                {
                    s.Skus = s.Skus.Where(k => k.Code == skuCode).ToList();
                    return s;
                });
            }

            return query.ToList();
        }

        public async Task<ProductDetailsModel> GetProductForCalculationByElasticId(Guid campaignId, Guid userId, Guid participantId,
                string elasticId, string skuCode)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                EI = elasticId,
                SC = skuCode,
                FI = "id|buid|name|productType|registerType|processType|layoutType|active|partnerId|manufacturerId|departmentId|categoryId|subcategoryId" +
                    "|skus.id|skus.code|skus.price|skus.shipping|skus.active|skus.activePartner|skus.stock|skus.isMaster|skus.attributes|catalogSettings",
                TK = 1
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult == null || !elasticResult.HasHits() || elasticResult.Hits.Count == 0)
                return null;
            var elasticProd = elasticResult.Hits.FirstOrDefault();
            if (elasticProd == null)
                throw MotivaiException.ofValidation("Produto não encontrado.");
            var sku = elasticProd.GetSkuByCode(skuCode);
            if (sku == null)
                throw MotivaiException.ofValidation("SKU não encontrado.");
            return new ProductDetailsModel(elasticProd, sku);
        }

        public async Task<ElasticsearchProduct> GetProductForCartItemByElasticIdAndSkuCode(Guid campaignId, Guid userId, Guid participantId,
                string elasticId, string skuCode)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                EI = elasticId,
                SC = skuCode,
                FI = "id|elasticsearchId|buId|name|productType|registerType|processType|layoutType|active|partnerId|partner|manufacturerId|departmentId|categoryId|subcategoryId" +
                    "|skus.id|skus.code|skus.price|skus.shipping|skus.active|skus.activePartner|skus.stock|skus.isMaster|skus.attributes|skus.customAttributes|skus.images|catalogSettings",
                TK = 1
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult == null || !elasticResult.HasHits() || elasticResult.Hits.Count == 0)
                return null;
            var elasticProd = elasticResult.Hits.FirstOrDefault();
            if (elasticProd == null)
                throw MotivaiException.ofValidation("Produto não encontrado.");

            return elasticProd;
        }

        public async Task<List<ProductDetailsModel>> GetProductsForCalculationByPartnersAndSkus(Guid campaignId, Guid userId, Guid participantId, List<PartnerSku> partnersSkus)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                FI = "id|buid|name|productType|registerType|processType|layoutType|active|partner|partnerId|manufacturerId|departmentId" +
                "|categoryId|subcategoryId|skus.id|skus.code|skus.price|skus.shipping|skus.active|skus.activePartner|skus.stock|skus.isMaster|skus.attributes",
                LPSC = partnersSkus.Select(ps => ElasticsearchPartnerSkuParameter.OfPartnerAndSkuCode(ps.PartnerId, ps.SkuCode)).ToList(),
                TK = partnersSkus.Count
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult == null || !elasticResult.HasHits() || elasticResult.Hits.Count == 0)
                return null;
            return elasticResult.Hits
                .Select(elasticProd =>
                {
                    var sku = elasticProd.Skus.FirstOrDefault(s => partnersSkus.Any(ps => ps.PartnerId.ToString() == elasticProd.PartnerId && ps.SkuCode == s.Code));
                    return new ProductDetailsModel(elasticProd, sku);
                })
                .ToList();
        }

        public async Task<List<ProductShowcaseModel>> GetProductsByEan(Guid campaignId, Guid userId, Guid participantId, string ean)
        {
            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                KU = participantId.ToString(),
                SE = ean,
                TK = 8
            };
            var elasticResult = await SearchProducts(parameters);
            if (elasticResult != null && elasticResult.HasHits())
            {
                return elasticResult.Hits.Select(CreateProductShowcaseModelFromMainSku).ToList();
            }
            return null;
        }

        public async Task<ProductAttributes> GetSkusAttributes(Guid campaignId, Guid userId, Guid participantId, string elasticId,
            string model = null, string voltagem = null, string color = null, string size = null)
        {

            var parameters = new ElasticsearchProductsParameters
            {
                KC = campaignId.ToString(),
                TKPA = userId.ToString(),
                EI = elasticId,
                AM = model,
                AC = color,
                AV = voltagem,
                AS = size,
                FI = "id|code|ean|customAttributes"
            };
            var apiReturn = await HttpClient.Create(MotivaiApi.Elasticsearch).Path("products/skus")
                .Entity(parameters)
                .AsyncPost()
                .GetApiReturn<ElasticsearchReturn<Sku>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar a pesquisa de atributos.");
            var elasticResult = apiReturn.GetReturnOrError();
            if (elasticResult != null && elasticResult.HasHits())
            {
                Sku sku = null;
                if (elasticResult.Hits.Count == 1)
                {
                    sku = elasticResult.Hits.FirstOrDefault();
                }
                return new ProductAttributes()
                {
                    SkuId = sku != null ? sku.Id : null,
                    SkuCode = sku != null ? sku.Code : null,
                    Models = (CreateItemFromAggregation(elasticResult.Aggregations.models) as List<Item>)
                        .Select(i => i.Description).ToArray(),
                    Colors = (CreateItemFromAggregation(elasticResult.Aggregations.colors) as List<Item>)
                        .Select(i => i.Description).ToArray(),
                    ColorsHashes = (CreateItemFromAggregation(elasticResult.Aggregations.colorHashes) as List<Item>)
                        .Select(i => i.Description).ToArray(),
                    Voltages = (CreateItemFromAggregation(elasticResult.Aggregations.voltages) as List<Item>)
                        .Select(i => i.Description).ToArray(),
                    Sizes = (CreateItemFromAggregation(elasticResult.Aggregations.sizes) as List<Item>)
                        .Select(i => i.Description).ToArray(),
                    CustomAttributes = sku != null ? sku.CustomAttributes : null,
                    DynamicPrice = sku != null ? sku.Price.DynamicPrice : false,
                    DynamicPricingSetter = sku != null ? sku.Price.DynamicPricingSetter : null,
                    DynamicPriceMinimumValue = sku != null ? sku.Price.DynamicPriceMinimumValue : null,
                    DynamicPriceMaximumValue = sku != null ? sku.Price.DynamicPriceMaximumValue : null,
                };
            }
            return null;
        }

        public async Task<bool> RegisterAvailableNotification(Guid productId, Guid participantId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Products)
                .Path(productId).Path("notifications").Path(participantId)
                .AsyncPost()
                .GetApiReturn<bool>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível registrar o aviso de disponibilidade do produto.");
            return apiReturn.GetReturnOrError();
        }
    }
}
