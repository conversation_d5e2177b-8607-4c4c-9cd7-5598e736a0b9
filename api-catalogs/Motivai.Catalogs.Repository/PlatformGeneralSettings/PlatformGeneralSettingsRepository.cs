using System.Threading.Tasks;

using Motivai.Catalogs.Domain.IRepository.GeneralSettings;
using Motivai.Catalogs.Domain.Models.GeneralSettings;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogs.Repository.PlatformGeneralSettings
{
    public class PlatformGeneralSettingsRepository : IPlatformGeneralSettingsRepository
    {
        private readonly ICache _cache;

        public PlatformGeneralSettingsRepository(ICache cache)
        {
            _cache = cache;
        }

        public async Task<RiskAssessmentSettings> GetActiveRiskAssessmentSettings()
        {
            return await _cache.GetOrCreate("plt-setts-risk-assessment", async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.GeneralSettings)
                    .Path("generalsettings/marketplace/riskassessment/active")
                    .AsyncGet()
                    .GetApiReturn<RiskAssessmentSettings>();
                if (apiReturn.HasNullReturn())
                    throw MotivaiException.ofValidation("Nenhuma configuração de análise de risco disponível.");
                return apiReturn.GetReturnOrError();
            }, 60);
        }
    }
}