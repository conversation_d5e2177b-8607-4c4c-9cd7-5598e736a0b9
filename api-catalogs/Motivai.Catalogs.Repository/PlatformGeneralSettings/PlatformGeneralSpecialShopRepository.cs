using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.Catalogs.Repository
{
    public class PlatformGeneralSpecialShopRepository : IPlatformGeneralSpecialShopRepository
    {
        public async Task<List<SpecialShopCatalogModel>> GetSpecialShopActives()
        {
            try
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.GeneralSettings)
                    .Path("marketing/specialshops/active")
                    .AsyncGet()
                    .GetApiReturn<List<SpecialShopCatalogModel>>();
                if (apiReturn == null)
                    return null;
                return apiReturn.GetReturnOrError();
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Erro ao carregar lojas especiais da plataforma.");
                throw MotivaiException.ofException("Ocorreu um erro ao carregar as lojas especiais da plataforma.", ex);
            }
        }

        public async Task<SpecialShopCatalogModel> GetSpecialShopActiveById(Guid specialShopId)
        {
            try
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.GeneralSettings)
                    .Path("marketing/specialshops/active").Path(specialShopId)
                    .AsyncGet()
                    .GetApiReturn<SpecialShopCatalogModel>();

                if (apiReturn == null)
                    return null;

                return apiReturn.GetReturnOrError();
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Erro ao carregar loja especial da plataforma.");
                throw MotivaiException.ofException("Erro ao carregar loja especial da plataforma.", ex);
            }
        }
    }
}