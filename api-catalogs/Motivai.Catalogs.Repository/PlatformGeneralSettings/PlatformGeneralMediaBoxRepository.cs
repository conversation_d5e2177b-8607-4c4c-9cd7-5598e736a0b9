using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;

namespace Motivai.Catalogs.Repository
{
    public class PlatformGeneralMediaBoxRepository : IPlatformGeneralMediaBoxRepository
    {
        public async Task<List<MediaBoxCatalogModel>> GetMediaBoxActives(MediaboxVisibleOn visibleOn, string page)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.GeneralSettings)
                .Path("marketing/mediaboxes/active")
                .Query("visibleOn", visibleOn.ToString())
                .Query("page", page)
                .AsyncGet()
                .GetApiReturn<List<MediaBoxCatalogModel>>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }
    }
}