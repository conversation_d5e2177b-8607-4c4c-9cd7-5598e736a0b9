﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Elasticsearch;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Domain.Model.Structures;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;

namespace Motivai.Catalogo.Repository
{
    public class CategoryRepository : ICategoryRepository
    {
        public async Task<ProductCategoryModel> GetCategory(Guid categoryId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Categories)
                .Path(categoryId)
                .AsyncGet()
                .GetApiReturn<ProductCategoryModel>();
            if (apiReturn == null)
                return null;
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<Guid>> GetDepartmentsWithProducts(Guid campaignId, Guid participantId) {
            var parameters = new ElasticsearchProductsParameters {
                KC = campaignId.ToString(),
                KU = participantId.ToString(),
                TK = 0
            };
            var apiReturn = await HttpClient.Create(MotivaiApi.Elasticsearch).Path("products")
                .Entity(parameters)
                .AsyncPost()
                .GetApiReturn<ElasticsearchReturn<ElasticsearchProduct>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as categorias com produtos.");
            var elasticResult = apiReturn.GetReturnOrError();
            if (elasticResult != null && elasticResult.Aggregations != null) {
                return (ElasticsearchHelper.AggregationToList(elasticResult.Aggregations.departmentIds) as List<Entry<string, int>>)
                    .Select(entry => Guid.Parse(entry.Key)).ToList();
            }
            return null;
        }

        public async Task<List<Guid>> GetCategoriesWithProducts(Guid campaignId, Guid participantId) {
            var parameters = new ElasticsearchProductsParameters {
                KC = campaignId.ToString(),
                KU = participantId.ToString(),
                TK = 0
            };
            var apiReturn = await HttpClient.Create(MotivaiApi.Elasticsearch).Path("products")
                .Entity(parameters)
                .AsyncPost()
                .GetApiReturn<ElasticsearchReturn<ElasticsearchProduct>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as categorias com produtos.");
            var elasticResult = apiReturn.GetReturnOrError();
            if (elasticResult != null && elasticResult.Aggregations != null) {
                return (ElasticsearchHelper.AggregationToList(elasticResult.Aggregations.categoryIds) as List<Entry<string, int>>)
                    .Union(ElasticsearchHelper.AggregationToList(elasticResult.Aggregations.subcategoryIds) as List<Entry<string, int>>)
                    .Select(entry => Guid.Parse(entry.Key)).ToList();
            }
            return null;
        }
    }
}