using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.Entities.Stocks;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Partners;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;
using Motivai.SharedKernel.Domain.Entities.References.Elasticsearch;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

using Newtonsoft.Json.Linq;

namespace Motivai.Catalogo.Repository
{
    public class CampaignRepository : ICampaignRepository
    {
        private readonly ICache _cache;

        public CampaignRepository(ICache cache)
        {
            this._cache = cache;
        }

        public async Task<bool> IsActive(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_STATUS + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("status")
                    .AsyncGet()
                    .GetApiReturn<bool>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível verificar a situação da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<Guid> GetCampaignIdByDomain(string domain)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_ID_BY_CATALOG_DOMAIN + domain, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns).Path("campaigns/id")
                    .Query("domain", domain)
                    .AsyncGet()
                    .GetApiReturn<Guid>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível verificar a campanha a partir da URL.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<CampaignSettingsModel> GetSettings(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_SETTINGS + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("settings")
                    .AsyncGet()
                    .GetApiReturn<CampaignSettingsModel>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar as configurações da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<CampaignCatalogSettings> GetPagesSettings(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_CATALOG_PAGES_SETTINGS + campaignId,
                async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("settings/pages")
                        .AsyncGet()
                        .GetApiReturn<CampaignCatalogSettings>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar as configurações da campanha.");
                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<CampaignFees> GetFees(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_FEES + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("fees")
                    .AsyncGet()
                    .GetApiReturn<CampaignFees>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar os fatores da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<PartnerConfiguration> GetPartnerConfiguration(Guid campaignId, Guid partnerId)
        {
            return await _cache.GetOrCreate(
                SharedCacheKeysPrefix.CAMPAIGN_PARTNER_CONFIGURATION + campaignId + partnerId, async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("partners").Path(partnerId)
                        .AsyncGet()
                        .GetApiReturn<PartnerConfiguration>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar as configurações do parceiro na campanha.");
                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<List<PartnerConfiguration>> GetPartnersConfigurations(Guid campaignId)
        {
            return await _cache.GetOrCreate(
                SharedCacheKeysPrefix.CAMPAIGN_PARTNER_CONFIGURATION + campaignId, async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("partners")
                        .AsyncGet()
                        .GetApiReturn<List<PartnerConfiguration>>();

                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar as configurações do parceiro na campanha.");

                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<string> GetSourceShippingCep(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("shippings/sourcecep")
                .AsyncGet()
                .GetApiReturn<string>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar CEP de origem da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<Guid>> GetTargetAudiencesByParticipant(Guid campaignId, Guid participantId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("targetaudiences/participants").Path(participantId)
                .AsyncGet()
                .GetApiReturn<List<Guid>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar os públicos alvos do participante.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<Guid> GetTheme(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_CATALOG_THEME + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("theme")
                    .AsyncGet()
                    .GetApiReturn<Guid>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar o tema da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<CoinName> GetCoinName(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_COINNAME + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("coinname")
                    .AsyncGet()
                    .GetApiReturn<CoinName>();
                if (apiReturn == null)
                {
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar moeda da campanha.");
                }

                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<JArray> GetMenu(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_CATALOG_MENU + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Elasticsearch)
                    .Path("campaigns").Path(campaignId).Path("marketing/menu")
                    .AsyncGet()
                    .GetApiReturn<ElasticsearchReturn<dynamic>>();
                if (apiReturn == null)
                {
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar o menu da campanha.");
                }

                var elasticResult = apiReturn.GetReturnOrError();
                if (!elasticResult.HasHits())
                    throw MotivaiException.of(ErrorType.Business, "Nenhum menu configurado para o catálogo.");
                var campaign = elasticResult.Hits.FirstOrDefault();
                if (campaign.menus == null || !(campaign.menus is JArray))
                    throw MotivaiException.of(ErrorType.Business, "Nenhum menu configurado para o catálogo.");
                return campaign.menus as JArray;
            });
        }

        public async Task<dynamic> GetActiveRegulation(Guid campaignId, Guid? userId = null)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("institutional/regulations/active")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o regulamento.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetActivePrivacyPolicy(Guid campaignId, Guid? userId = null)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("institutional/privacypolicies/active")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar a política de privacidade.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetActiveShippingPolicy(Guid campaignId, Guid? userId = null)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("institutional/shippingpolicies/active")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar a política de entrega.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<dynamic>> GetFaqs(Guid campaignId, Guid? userId = null, string term = null)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("institutional/faqs/items/active")
                .Query("userId", userId)
                .Query("term", term)
                .AsyncGet()
                .GetApiReturn<List<dynamic>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o FAQ da campanha.");
            return apiReturn.GetReturnOrError();
        }

        #region Fale Conosco

        public async Task<List<CampaignContactUsSubject>> GetContactSubjects(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("contactus/subjects")
                .AsyncGet()
                .GetApiReturn<List<CampaignContactUsSubject>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o FAQ da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> SendContactUsFormMessage(Guid campaignId, ContactUsModel model)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("contactus/form/registration")
                .Entity(model)
                .AsyncPost()
                .GetApiReturn<bool>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível enviar a mensagem de contato.");
            return apiReturn.GetReturnOrError();
        }

        #endregion

        public async Task<CommunicationModel> GetCommunicationById(Guid campaignId, Guid communicationId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("marketing/communications").Path(communicationId)
                .AsyncGet()
                .GetApiReturn<CommunicationModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar a comunicação da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<MediaBox>> GetActiveMediaBoxesForDepartment(Guid campaignId, Guid userId,
            string departmentId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("marketing/mediaboxes/communications")
                .Query("department", departmentId)
                .Query("active", "true")
                .Query("catalog", "true")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<List<MediaBox>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar os media boxes da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<MediaBox>> GetActiveMediaBoxesForSitePage(Guid campaignId, Guid userId, string page)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("marketing/mediaboxes/communications")
                .Query("page", page)
                .Query("active", "true")
                .Query("site", "true")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<List<MediaBox>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar os media boxes da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<SpecialShopModel>> GetActiveSpecialShopsByCampaign(Guid campaignId)
        {

            return await _cache.GetOrCreate(
                SharedCacheKeysPrefix.CAMPAIGN_ACTIVE_SPECIALSHOPS + campaignId, async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("marketing/specialshops")
                        .Query("active", "true")
                        .AsyncGet()
                        .Timeout(15_000)
                        .GetApiReturn<List<SpecialShopModel>>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar as lojas especiais da campanha.");
                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<SpecialShopModel> GetActiveSpecialShopById(Guid campaignId, Guid specialShopId)
        {
            return await _cache.GetOrCreate(
            SharedCacheKeysPrefix.CAMPAIGN_SPECIALSHOP + campaignId + specialShopId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("marketing/specialshops").Path(specialShopId)
                    .AsyncGet()
                    .GetApiReturn<SpecialShopModel>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar a loja especial da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<SpecialShopModel> GetRandomActiveSpecialShopByCampaign(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("marketing/specialshops/random")
                .AsyncGet()
                .Timeout(15_000)
                .GetApiReturn<SpecialShopModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar a loja especial da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<PartnerSku>> GetHomeFeaturedProducts(Guid campaignId)
        {
            return await _cache.GetOrCreate(
                SharedCacheKeysPrefix.CAMPAIGN_DEPT_FEATURED_PRODUCTS + campaignId + Guid.Empty, async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("marketing/departments/home/<USER>")
                        .AsyncGet()
                        .GetApiReturn<List<PartnerSku>>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar os produtos da campanha.");
                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<List<PartnerSku>> GetDepartmentFeaturedProducts(Guid campaignId, Guid departmentId)
        {
            return await _cache.GetOrCreate(
                SharedCacheKeysPrefix.CAMPAIGN_DEPT_FEATURED_PRODUCTS + campaignId + departmentId, async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("marketing/departments").Path(departmentId)
                        .Path("products")
                        .AsyncGet()
                        .GetApiReturn<List<PartnerSku>>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar os produtos da campanha.");
                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<PaymentGatewaySettings> GetActivePaymentGateway(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_POINTS_PURCHASE_GATEWAY + campaignId,
                async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("paymentgateways")
                        .Query("master", "true")
                        .AsyncGet()
                        .GetApiReturn<List<PaymentGatewaySettings>>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar o gateway de pagamento.");
                    var gateway = apiReturn.GetReturnOrError();
                    if (gateway == null || gateway.Count == 0)
                    {
                        return null;
                    }

                    return gateway.FirstOrDefault();
                }, 30);
        }

        public async Task<List<CampaignShipping>> GetActiveShippings(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_SHIPPINGS + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("shippings")
                    .Query("active", "true")
                    .AsyncGet()
                    .GetApiReturn<List<CampaignShipping>>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar o gateway de pagamento.");
                return apiReturn.GetReturnOrError();
            });
        }

        #region Parceiros Extra Service (Recarga, Pague Contas, Cartões, Cashback)

        public async Task<MobilePartnerParametrization> GetActiveMobilePartner(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("partners/services/mobile/master")
                .AsyncGet()
                .GetApiReturn<MobilePartnerParametrization>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar o parceiro de recarga.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<BillPaymentPartnerParametrization> GetActiveBillPaymentPartner(Guid campaignId,
            string serviceType = null)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("partners/services/billpayment/master")
                .Query("serviceType", serviceType)
                .AsyncGet()
                .GetApiReturn<BillPaymentPartnerParametrization>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar o parceiro de pague contas.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> HasBillPaymentPartnerWithFilters(Guid campaignId,
            BillsPaymentPartnerFilters billsPaymentPartnerFilters)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("partners/services/billpayment/filters")
                .Entity(billsPaymentPartnerFilters)
                .AsyncPost()
                .LogPayloadToConsole()
                .LogResponseToConsole()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível consultar se existe parceiro para o tipo de serviço.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetCardsPageConfiguration(Guid campaignId, string cardType)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("cards/prepaid/configuration")
                .Query("cardType", cardType)
                .AsyncGet()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetBankTransferConfiguration(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("partners/services/banktransfer")
                .AsyncGet()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações.");
            return apiReturn.GetReturnOrError();
        }

        #endregion

        public async Task<List<ProductFixedPrice>> GetProductsFixedPrices(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_FIXED_PRICES + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("products/fixedprices")
                    .AsyncGet()
                    .GetApiReturn<List<ProductFixedPrice>>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar os preços fixos da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<List<DiscountRules>> GetDiscounts(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_DISCOUNTS_RULES + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("prices/discounts/catalog")
                    .AsyncGet()
                    .GetApiReturn<List<DiscountRules>>();
                if (apiReturn == null)
                {
                    await ExceptionLogger.LogException(
                        MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar os descontos da campanha."), "discounts", true);
                    return null;
                }

                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<DiscountRules> GetDiscountByCouponCode(Guid campaignId, string couponCode)
        {
            if (string.IsNullOrEmpty(couponCode))
                throw MotivaiException.ofValidation("Cupom de desconto inválido.");

            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("prices/discounts/coupons/code").Path(couponCode)
                .AsyncGet()
                .GetApiReturn<DiscountRules>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o desconto.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<Guid>> GetUserGroups(Guid campaignId, Guid userId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("participantsgroups/ids")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<List<Guid>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar os grupos do participante.");
            return apiReturn.GetReturnOrError();
        }

        #region Estoque Customizado

        public async Task<List<CampaignGroupSkuStock>> GetCampaignSkuStocks(Guid campaignId, Guid partnerId, Guid skuId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("stocks/search")
                .Query("partnerId", partnerId)
                .Query("skuId", skuId)
                .AsyncGet()
                .GetApiReturn<List<CampaignGroupSkuStock>>();
            if (apiReturn == null)
            {
                await ExceptionLogger.LogException(
                    MotivaiException.of(ErrorType.ApiException,
                        "Não foi possível carregar os estoques da campanha."), "stocks", true);
                return null;
            }

            return apiReturn.GetReturnOrError();
        }

        public async Task<CampaignCatalogSettingsFooter> GetCatalogFooterSettingsByCampaign(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_CATALOG_FOOTER_SETTINGS + campaignId,
                async () =>
                {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                        .Path("campaigns").Path(campaignId).Path("settings/footer")
                        .AsyncGet()
                        .GetApiReturn<CampaignCatalogSettingsFooter>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException,
                            "Não foi possível carregar as configurações do rodapé da campanha.");
                    return apiReturn.GetReturnOrError();
                });
        }

        public async Task<BillPaymentPartnerParametrization> GetActiveBillPaymentPartnerByServiceType(Guid campaignId,
            string serviceType)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("partners/services/type")
                .Path(serviceType)
                .AsyncGet()
                .GetApiReturn<BillPaymentPartnerParametrization>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar as configurações do parceiro de pagamento de conta pelo tipo informado.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetCardParametrizations(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("cards/prepaid/parametrizations")
                .AsyncGet()
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException,
                    "Não foi possível carregar as parametrizacoes do cartão.");
            return apiReturn.GetReturnOrError();
        }

        #endregion

        #region Formas de Pagamentos

        public async Task<List<MarketplacePaymentMethodConfiguration>> GetActivePaymentMethods(Guid campaignId)
        {
            return await _cache.GetOrCreate(SharedCacheKeysPrefix.CAMPAIGN_PAYMENTMETHODS + campaignId, async () =>
            {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("paymentmethods/settings")
                    .AsyncGet()
                    .GetApiReturn<List<MarketplacePaymentMethodConfiguration>>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os métodos de pagamentos da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        #endregion
    }
}