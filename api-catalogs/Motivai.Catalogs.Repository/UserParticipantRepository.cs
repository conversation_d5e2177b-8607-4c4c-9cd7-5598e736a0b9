﻿using System;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.Catalogo.Repository
{
	public class UserParticipantRepository : IUserParticipantRepository
	{
		public async Task<decimal> GetAvailableBalance(Guid campaignId, Guid userId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Transactions)
				.Path("transactions/campaigns").Path(campaignId).Path("users").Path(userId).Path("balance")
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o saldo do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> IsActiveUserInCampaign(Guid userId, Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("status")
				.AsyncGet()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível verificar se o usuário participa da campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Guid> GetParticipantIdByUserAndCampaign(Guid userId, Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("participant/id")
				.AsyncGet()
				.GetApiReturn<Guid>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "USER_INFO_ERROR", "Não foi possível carregar o participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Address> GetMainAddress(Guid userId, Guid campaignId)
		{
			if (userId == Guid.Empty) return null;
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses/main")
				.AsyncGet()
				.GetApiReturn<Address>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o endereço do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Address> GetAddressById(Guid userId, Guid campaignId, Guid shippingAddressId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses").Path(shippingAddressId)
				.AsyncGet()
				.GetApiReturn<Address>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o endereço do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<ParticipantContact> GetParticipantContact(Guid userId, Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("contact")
				.AsyncGet()
				.GetApiReturn<ParticipantContact>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o contato do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<ParticipantInfo> GetParticipantInfo(Guid userId, Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("info")
				.AsyncGet()
				.GetApiReturn<ParticipantInfo>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os dados do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<AccountRepresentative> GetAccountRepresentative(Guid userId, Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("accountrepresentative")
				.AsyncGet()
				.GetApiReturn<AccountRepresentative>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os dados do representante da conta.");
			return apiReturn.GetReturnOrError();
		}
	}
}
