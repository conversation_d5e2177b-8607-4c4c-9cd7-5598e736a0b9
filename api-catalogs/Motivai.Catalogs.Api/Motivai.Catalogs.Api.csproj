﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>netcoreapp2.1</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <PublishWithAspNetCoreTargetManifest>false</PublishWithAspNetCoreTargetManifest>
  </PropertyGroup>
  <ItemGroup>
    <Content Update="nlog.config" CopyToOutputDirectory="Always" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.All" Version="2.1.6" />

    <!-- https://github.com/PrometheusClientNet/Prometheus.Client -->
    <!--
    <PackageReference Include="Prometheus.Client.AspNetCore" Version="2.1.0" />
    <PackageReference Include="Prometheus.Client.HttpRequestDurations" Version="1.2.0" />
    -->

    <ProjectReference Include="..\Motivai.Catalogs.App\Motivai.Catalogs.App.csproj" />
    <ProjectReference Include="..\Motivai.Catalogs.Domain\Motivai.Catalogs.Domain.csproj" />
    <ProjectReference Include="..\Motivai.Catalogs.Repository\Motivai.Catalogs.Repository.csproj" />
  </ItemGroup>
</Project>