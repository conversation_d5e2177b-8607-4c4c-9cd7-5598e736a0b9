﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Motivai.Catalogo.App;
using Motivai.Catalogo.App.CampaignsCatalog;
using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogo.Domain.IApp.CampaignCatalog;
using Motivai.Catalogo.Domain.IRepository;
using Motivai.Catalogo.Repository;
using Motivai.Catalogs.App.ContactUs;
using Motivai.Catalogs.App.ExtraServices;
using Motivai.Catalogs.App.Marketing;
using Motivai.Catalogs.App.PointsPurchase;
using Motivai.Catalogs.App.Regions;
using Motivai.Catalogs.Domain.Configs.PaymentGateways;
using Motivai.Catalogs.Domain.IApp.ContactUs;
using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.IApp.PointsPurchase;
using Motivai.Catalogs.Domain.IApp.Regions;
using Motivai.Catalogs.Domain.IRepository;
using Motivai.Catalogs.Domain.IRepository.ExtraServices;
using Motivai.Catalogs.Domain.IRepository.Integrations;
using Motivai.Catalogs.Domain.IRepository.Notifications;
using Motivai.Catalogs.Domain.IRepository.PaymentGateway;
using Motivai.Catalogs.Domain.IRepository.Prices;
using Motivai.Catalogs.Domain.IRepository.Transactions;
using Motivai.Catalogs.Domain.Services.Notifications;
using Motivai.Catalogs.Domain.Services.Prices;
using Motivai.Catalogs.Domain.Services.Products;
using Motivai.Catalogs.Domain.Services.Products.Stock;
using Motivai.Catalogs.Domain.Services.Products.Stocks;
using Motivai.Catalogs.Domain.Services.Shippings;
using Motivai.Catalogs.Repository;
using Motivai.Catalogs.Repository.ExtraServices;
using Motivai.Catalogs.Repository.Integrations;
using Motivai.Catalogs.Repository.Notifications;
using Motivai.Catalogs.Repository.PaymentGateway;
using Motivai.Catalogs.Repository.Prices;
using Motivai.Catalogs.Repository.Transactions;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.Catalogs.Domain.Services.ShoppingCarts;
using Motivai.Catalogs.Domain.IRepository.ShoppingCarts;
using Motivai.Catalogs.Repository.ShoppingCarts;
using Motivai.Catalogs.Repository.Products;
using Motivai.Catalogs.Domain.IRepository.Products;
using Motivai.Catalogs.Domain.IApp.Accounts;
using Motivai.Catalogs.App.Accounts;
using Motivai.Catalogs.App.Marketing.CampaignPagesCustomScript;
using Motivai.Catalogs.Domain.IRepository.Marketing;
using Motivai.Catalogs.Repository.Marketing;
using Motivai.Catalogs.Domain.Services.Refunds;
using Motivai.Catalogs.Domain.IRepository.GeneralSettings;
using Motivai.Catalogs.Repository.PlatformGeneralSettings;
using Motivai.Catalogs.App.Products;
using Motivai.Catalogs.Domain.Services.PaymentMethods;

namespace Motivai.Catalogs.Api
{
	public class Startup : ApiBaseStartup
	{
		public Startup(IHostingEnvironment env) : base(env) { }

		public override void ConfigureIoC(IServiceCollection services)
		{
			services.Configure<PaymentGateways>(options => Configuration.GetSection("PaymentGateways").Bind(options));

			// Apps
			services.AddSingleton<ICampaignCatalogApp, CampaignCatalogApp>();
			services.AddSingleton<IOrderCatalogApp, OrderCatalogApp>();
			services.AddSingleton<IProductCatalogApp, ProductCatalogApp>();
			services.AddSingleton<IMarketingApp, MarketingApp>();
			services.AddSingleton<IDepartmentCatalogApp, DepartmentCatalogApp>();
			services.AddSingleton<ICampaignContactApp, CampaignContactApp>();
			services.AddSingleton<IPointsPurchaseApp, PointsPurchaseApp>();
			services.AddSingleton<IRegionApp, RegionApp>();
			services.AddSingleton<IMobileRechargeApp, MobileRechargeApp>();
			services.AddSingleton<IBillPaymentApp, BillPaymentApp>();
			services.AddSingleton<IAccountWalletDebiter, AccountWalletDebiter>();
			services.AddSingleton<IMarketingMediaboxApp, MarketingMediaboxApp>();
			services.AddSingleton<IMarketingSpecialshopApp, MarketingSpecialshopApp>();
			services.AddSingleton<ICampaignPagesCustomScriptApp, CampaignPagesCustomScriptApp>();

			// Services
			services.AddSingleton<DiscountCalculator>();
			services.AddSingleton<ProductFactorySelector>();
			services.AddSingleton<SecurityNotificator>();
			services.AddSingleton<ProductPointsPurchaseCalculator>();
			services.AddSingleton<CampaignStockService>();
			services.AddSingleton<PartnerOnlineStockService>();
			services.AddSingleton<ShoppingCartManager>();
			services.AddSingleton<CatalogProductService>();
			services.AddSingleton<CatalogShippingService>();
			services.AddSingleton<ICardsService, CardsService>();
			services.AddSingleton<IBankTransferService, BankTransferService>();
			services.AddSingleton<ProductDetailsService>();

			services.AddSingleton<ShoppingCartCalculator>();
            services.AddSingleton<ProductShippingCostCalculator>();
			services.AddSingleton<OnlineShoppingCartCalculator>();
			services.AddSingleton<OfflineShoppingCartCalculator>();
			services.AddSingleton<RefundProcessorService>();
            services.AddSingleton<CatalogPaymentMethodsService>();

			// Repositories
			services.AddSingleton<ICampaignRepository, CampaignRepository>();
			services.AddSingleton<ICategoryRepository, CategoryRepository>();
			services.AddSingleton<ICompanyRepository, CompanyRepository>();
			services.AddSingleton<ICorreiosRepository, CorreiosRepository>();
			services.AddSingleton<IEmailRepository, EmailRepository>();
			services.AddSingleton<IOrderRepository, OrderRepository>();
			services.AddSingleton<IUserParticipantRepository, UserParticipantRepository>();
			services.AddSingleton<IProductCatalogRepository, ProductCatalogRepository>();
			services.AddSingleton<IProductIntegrationRepository, ProductIntegrationRepository>();
			services.AddSingleton<IOrderIntegrationRepository, OrderIntegrationRepository>();
			services.AddSingleton<IPaymentGatewayRepository, PaymentGatewayRepository>();
			services.AddSingleton<ITransactionRepository, TransactionRepository>();
			services.AddSingleton<IFactoriesRepository, FactoriesRepository>();
			services.AddSingleton<IRegionsRepository, RegionsRepository>();
			services.AddSingleton<ICatalogExtraServiceRepository, CatalogExtraServiceRepository>();
			services.AddSingleton<IPricesRepository, PricesRepository>();
			services.AddSingleton<ICardsRepository, CardsRepository>();
			services.AddSingleton<IBankTransferRepository, BankTransferRepository>();
			services.AddSingleton<INotificationRepository, NotificationRepository>();
			services.AddSingleton<IShoppingCartRepository, ShoppingCartRepository>();
			services.AddSingleton<IProductRepository, ProductRepository>();
			services.AddSingleton<IPlatformGeneralMediaBoxRepository, PlatformGeneralMediaBoxRepository>();
			services.AddSingleton<IPlatformGeneralSpecialShopRepository, PlatformGeneralSpecialShopRepository>();
            services.AddSingleton<IPlatformGeneralSettingsRepository, PlatformGeneralSettingsRepository>();
			services.AddSingleton<ICampaignsPagesCustomScriptsRepository, CampaignsPagesCustomScriptsRepository>();

			//CRYPT
			services.AddSingleton<ICryptography, Cryptography>();
		}
	}
}