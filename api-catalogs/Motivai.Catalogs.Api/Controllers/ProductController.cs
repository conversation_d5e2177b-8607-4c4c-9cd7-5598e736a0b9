using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogo.Domain.Models;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Services.PaymentMethods;
using Motivai.SharedKernel.Domain.Model;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;

namespace Motivai.Catalogo.Api.Controllers
{
    [Produces("application/json")]
    [Route("catalogs/{campaignId}/products")]
    public class ProductController : Controller
    {
        private readonly IProductCatalogApp _productCatalogApp;
        private readonly CatalogPaymentMethodsService _catalogPaymentMethodsService;
        private ILogger<ProductController> logger;

        public ProductController(IProductCatalogApp productCatalogApp,
            CatalogPaymentMethodsService catalogPaymentMethodsService,
            ILogger<ProductController> logger)
        {
            this._productCatalogApp = productCatalogApp;
            this._catalogPaymentMethodsService = catalogPaymentMethodsService;
            this.logger = logger;
        }

        private string[] SplitQueryFilter(string filter = null)
        {
            return filter?.Split('|');
        }

        [HttpGet]
        public async Task<ApiReturn<List<ProductShowcaseModel>>> SearchProductsByTermOrSku(Guid campaignId,
            [FromQuery] Guid? fid,
            [FromQuery] Guid uid, [FromQuery] Guid? pid, [FromQuery] string term, [FromQuery] string skuCode,
            [FromQuery] decimal? balance = null,
            [FromQuery] int fr = 0, [FromQuery] int sz = 8)
        {
            return await ApiReturn<List<ProductShowcaseModel>>.Execute(
                _productCatalogApp.SearchProductsByTermOrSku(campaignId, fid, uid, pid, skuCode, term, balance, fr,
                    sz));
        }

        // Refactoring
        [HttpGet("search")]
        public async Task<ApiReturn<SearchModel>> SearchProducts(Guid campaignId, [FromQuery] Guid uid,
            [FromQuery] Guid pid, [FromQuery] Guid? dpi,
            [FromQuery] string q = null, [FromQuery] string dp = null, [FromQuery] string ct = null,
            [FromQuery] string sb = null,
            [FromQuery] string pr = null, [FromQuery] string mn = null, [FromQuery] string cr = null,
            [FromQuery] string vl = null,
            [FromQuery] string srt = null, [FromQuery] bool? asc = null, [FromQuery] string nob = null,
            [FromQuery] decimal? fp = null, [FromQuery] decimal? tp = null,
            [FromQuery] decimal? balance = null, [FromQuery] int fr = 0, [FromQuery] int sz = 8,
            [FromQuery] string prt = null)
        {
            // this.logger.LogInformation("SEARCH REQUEST LOGGING: " +
            //                            $"campaignId={campaignId}, uid={uid}, pid={pid}, dpi={dpi}, q={q}, dp={dp}, ct={ct}, " +
            //                            $"sb={sb}, pr={pr}, mn={mn}, cr={cr}, vl={vl}, srt={srt}, asc={asc}, nob={nob}, " +
            //                            $"fp={fp}, tp={tp}, balance={balance}, fr={fr}, sz={sz}, prt={prt}");

            var categories = SplitQueryFilter(ct);
            var manufacturers = string.IsNullOrEmpty(mn) ? null : mn.Split(',');
            return await ApiReturn<SearchModel>.Execute(_productCatalogApp.SearchProducts(campaignId, uid, pid, dpi, q,
                SplitQueryFilter(pr), SplitQueryFilter(dp), SplitQueryFilter(ct), SplitQueryFilter(sb), SplitQueryFilter(mn),
                SplitQueryFilter(cr), SplitQueryFilter(vl), srt, asc, nob, fp, tp, balance, fr, sz, SplitQueryFilter(prt)));
        }

        [HttpGet("{elasticId}")]
        public async Task<ApiReturn<ProductDetailsModel>> GetProductByElasticId(Guid campaignId, string elasticId,
            [FromQuery] string skuCode,
            [FromQuery] Guid uid, [FromQuery] Guid pid, [FromQuery] bool? all = false,
            [FromQuery] bool? searchSimiliarSkuAndEan = false,
            [FromQuery] decimal? balance = null)
        {
            return await ApiReturn<ProductDetailsModel>.Execute(_productCatalogApp.GetProductByElasticId(campaignId,
                uid, pid, elasticId, skuCode, all, searchSimiliarSkuAndEan, balance));
        }

        [HttpGet("{elasticId}/skus/{skuCode}")]
        public async Task<ApiReturn<ProductDetailsModel>> GetSkuForCartByElasticIdAndSku(Guid campaignId,
            string elasticId, string skuCode,
            [FromQuery] Guid uid, [FromQuery] Guid pid, [FromQuery] int quantity = 1,
            [FromQuery] decimal? priceDefinedByParticipant = null)
        {
            return await ApiReturn<ProductDetailsModel>.Execute(_productCatalogApp.AddSkuToParticipantCart(campaignId,
                uid, pid, elasticId, skuCode, quantity, priceDefinedByParticipant));
        }

        [HttpGet("{elasticId}/skus/{skuCode}/availability")]
        public async Task<ApiReturn<AvailabilityModel>> CheckAvailability(Guid campaignId, string elasticId,
            string skuCode,
            [FromQuery] Guid uid, [FromQuery] Guid pid, [FromQuery] int quantity = 1,
            [FromQuery] decimal? priceDefinedByParticipant = null)
        {
            return await ApiReturn<AvailabilityModel>.Execute(_productCatalogApp.CheckAvailability(campaignId, uid, pid,
                elasticId, skuCode, quantity, priceDefinedByParticipant));
        }

        [HttpGet("skucode/{skuCode}")]
        public async Task<ApiReturn<List<ProductDetailsModel>>> FindProductsBySkuCode(Guid campaignId, string skuCode,
            [FromQuery] Guid uid, [FromQuery] Guid pid)
        {
            return await ApiReturn<List<ProductDetailsModel>>.Execute(
                _productCatalogApp.FindProductsBySkuCode(campaignId, uid, pid, skuCode));
        }

        [HttpGet("similars/{categoryId}")]
        public async Task<ApiReturn<List<ProductShowcaseModel>>> GetSimilarsProductByCategory(Guid campaignId,
            Guid categoryId, [FromQuery] Guid uid, [FromQuery] Guid pid,
            [FromQuery] decimal? balance = null)
        {
            return await ApiReturn<List<ProductShowcaseModel>>.Execute(
                _productCatalogApp.GetSimilarProducts(campaignId, uid, pid, categoryId, balance));
        }

        [HttpGet("{ean}/marketplace")]
        public async Task<ApiReturn<List<MarketplaceItem>>> GetMarketplacesByEan(Guid campaignId, [FromQuery] Guid uid,
            [FromQuery] Guid pid, string ean,
            [FromQuery] decimal? balance = null)
        {
            return await ApiReturn.Execute(_productCatalogApp.GetMarketplacesByEan(campaignId, uid, pid, ean, balance));
        }

        [HttpGet("{elasticId}/attributes")]
        public async Task<ApiReturn<ProductAttributes>> GetSkusAttributes(Guid campaignId, [FromQuery] Guid uid,
            [FromQuery] Guid pid, string elasticId,
            [FromQuery] string model = null, [FromQuery] string voltage = null, [FromQuery] string color = null,
            [FromQuery] string size = null)
        {
            return await ApiReturn<ProductAttributes>.Execute(
                _productCatalogApp.GetSkusAttributes(campaignId, uid, pid, elasticId, model, voltage, color, size));
        }

        [HttpPost("{productId}/notifications/{participantId}")]
        public async Task<ApiReturn<bool>> RegisterNotification(Guid campaignId, Guid productId, Guid participantId)
        {
            return await ApiReturn<bool>.Execute(
                _productCatalogApp.RegisterAvailableNotification(campaignId, productId, participantId));
        }

        [HttpPost("paymentmethods/eligible")]
        public async Task<ApiReturn<IEnumerable<CatalogPaymentMethod>>> GetPaymentMethodsForProduct(Guid campaignId,
            [FromBody] ResumedProduct resumedProduct)
        {
            return await ApiReturn.Execute(_catalogPaymentMethodsService.GetPaymentMethodsForProduct(campaignId, resumedProduct));
        }
    }
}