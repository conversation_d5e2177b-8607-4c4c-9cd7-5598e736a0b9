using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Model;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Catalogs.Api.Controllers
{
	[Route("catalogs/{campaignId}/departments")]
	public class DepartmentController : Controller
	{
		private readonly IDepartmentCatalogApp departmentApp;

		public DepartmentController(IDepartmentCatalogApp departmentApp)
		{
			this.departmentApp = departmentApp;
		}

		[HttpGet("{departmentId}")]
		public async Task<ApiReturn<DepartmentModel>> GetDepartmentById(Guid campaignId, Guid departmentId, [FromQuery] Guid pid)
		{
			return await ApiReturn.Execute(departmentApp.GetDepartmentById(campaignId, departmentId, pid));
		}

		[HttpGet("categories/{categoryId}")]
		public async Task<ApiReturn<CategoryModel>> GetCategoryById(Guid campaignId, Guid categoryId, [FromQuery] Guid pid)
		{
			return await ApiReturn.Execute(departmentApp.GetCategoryById(campaignId, categoryId, pid));
		}

		[HttpGet("{departmentId}/products/offers")]
		public async Task<ApiReturn<List<ProductShowcaseModel>>> GetProductsOffersByDepartment(Guid campaignId, Guid departmentId, [FromQuery] Guid uid,
				[FromQuery] Guid pid, [FromQuery] decimal? balance = null)
		{
			return await ApiReturn.Execute(departmentApp.GetProductsOffersByDepartment(campaignId, departmentId, uid, pid, balance));
		}

		[HttpGet("{departmentId}/products")]
		public async Task<ApiReturn<List<ProductShowcaseModel>>> GetProductsByDepartment(Guid campaignId, Guid departmentId, [FromQuery] Guid uid,
				[FromQuery] Guid pid, [FromQuery] decimal? balance = null)
		{
			return await ApiReturn.Execute(departmentApp.GetDepartmentFeaturedProductsByCampaign(campaignId, departmentId, uid, pid, balance));
		}
	}
}