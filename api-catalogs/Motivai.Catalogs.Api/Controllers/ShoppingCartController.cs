﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogs.Domain.Entities.Shippings;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.Catalogs.Domain.Models.Shippings;
using Motivai.Catalogs.Domain.Services.Notifications;
using Motivai.Catalogs.Domain.Services.PaymentMethods;
using Motivai.Catalogs.Domain.Services.ShoppingCarts;
using Motivai.SharedKernel.Domain.Model;

using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.PaymentMethods;

namespace Motivai.Catalogo.Api.Controllers
{
    [Route("catalogs/{campaignId}/users/{userId}/shoppingcart")]
    public class ShoppingCartController : Controller
    {
        private readonly ShoppingCartCalculator _shoppingCartCalculator;
        private readonly ProductShippingCostCalculator _productShippingCostCalculator;
        private readonly CatalogPaymentMethodsService _catalogPaymentMethodsService;
        private readonly SecurityNotificator _securityNotificator;

        public ShoppingCartController(ShoppingCartCalculator shoppingCartCalculator,
            ProductShippingCostCalculator productShippingCostCalculator,
            CatalogPaymentMethodsService catalogPaymentMethodsService,
            SecurityNotificator securityNotificator)
        {
            _shoppingCartCalculator = shoppingCartCalculator;
            _productShippingCostCalculator = productShippingCostCalculator;
            _catalogPaymentMethodsService = catalogPaymentMethodsService;
            _securityNotificator = securityNotificator;
        }

        [HttpPost("securitytoken")]
        public async Task<ApiReturn<string>> IssueSecurityToken(Guid campaignId, Guid userId)
        {
            return await ApiReturn.Execute(_securityNotificator.SendSecurityToken(campaignId, userId));
        }

        [HttpPost]
        public async Task<ApiReturn<Cart>> Calculate(Guid campaignId, Guid userId, [FromQuery] Guid pid, [FromBody] Cart cart)
        {
            return await ApiReturn<Cart>.Execute(_shoppingCartCalculator.CalculateParticipantCart(campaignId, userId, cart));
        }

        [HttpPost("item/factories")]
        public async Task<ApiReturn<List<ProductFactory>>> GetAvailableFactories(Guid campaignId, Guid userId, [FromQuery] Guid pid, [FromBody] ItemShippingModel model)
        {
            return await ApiReturn<List<ProductFactory>>.Execute(_productShippingCostCalculator.GetAvailableFactories(campaignId, userId, pid, model));
        }

        [HttpPost("item")]
        public async Task<ApiReturn<ShippingCostResult>> CalculateItemShippingCost(Guid campaignId, Guid userId, [FromQuery] Guid pid, [FromBody] ItemShippingModel model)
        {
            return await ApiReturn<ShippingCostResult>.Execute(_productShippingCostCalculator.CalculateItemShippingCost(campaignId, userId, pid, model));
        }

        [HttpPost("paymentmethods/eligible")]
        public async Task<ApiReturn<IEnumerable<CatalogPaymentMethod>>> GetEligiblePaymentMethods(Guid campaignId, [FromBody] Cart cart)
        {
            return await ApiReturn.Execute(_catalogPaymentMethodsService.GetPaymentMethodsForCart(campaignId, cart));
        }
    }
}