using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Domain.Model;

namespace Motivai.Catalogs.Api.Controllers.cards
{

    [Route("catalogs/{campaignId}/extraservices/prepaidcards")]
    public class CampaignPrePaidCardsController
    {
        private readonly ICardsService cardsService;

        public CampaignPrePaidCardsController(ICardsService cardsService) {
            this.cardsService = cardsService;
        }

        [HttpGet("participants/{userId}/active")]
		public async Task<ApiReturn<List<dynamic>>> GetParticipantActiveCards(Guid campaignId, Guid userId, [FromQuery] PrepaidCardType? cardType)
		{
			return await ApiReturn<List<dynamic>>.Execute(this.cardsService.GetParticipantActiveCards(campaignId, userId, cardType));
		}

		[HttpGet("configuration")]
		public async Task<ApiReturn<dynamic>> GetCardsConfiguration(Guid campaignId, [FromQuery] string cardType)
		{
			return await ApiReturn<dynamic>.Execute(this.cardsService.GetCardsConfiguration(campaignId, cardType));
		}

		[HttpPost("orders/fees")]
		public async Task<ApiReturn<dynamic>> CalculateCardOrderFees(Guid campaignId, [FromBody] dynamic order)
		{
			return await ApiReturn<dynamic>.Execute(this.cardsService.CalculateCardOrderFees(campaignId, order));
		}

		[HttpPost("orders")]
		public async Task<ApiReturn<dynamic>> CreateCardOrder(Guid campaignId, [FromBody] dynamic order)
		{
			return await ApiReturn<dynamic>.Execute(this.cardsService.CreateCardOrder(campaignId, order));
		}

		[HttpGet("orders/{orderId}")]
		public async Task<ApiReturn<CardOrder>> GetCardOrderById(Guid campaignId, Guid orderId)
		{
			return await ApiReturn<CardOrder>.Execute(this.cardsService.GetCardOrderById(campaignId, orderId));
		}

        [HttpGet("parametrizations")]
        public async Task<ApiReturn<dynamic>> GetCardParametrizations(Guid campaignId)
        {
            return await ApiReturn<dynamic>.Execute(this.cardsService.GetCardParametrizations(campaignId));
        }
    }
}