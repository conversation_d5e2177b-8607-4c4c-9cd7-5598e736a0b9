using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.ExtraServices;
using Motivai.Catalogs.Domain.IApp.ExtraServices;
using Motivai.Catalogs.Domain.Models.ExtraServices;
using Motivai.SharedKernel.Domain.Model;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Motivai.Catalogs.Api.Controllers
{
	[Route("catalogs/{campaignId}/extraservices")]
	public class CatalogExtraServicesController
	{
		private readonly IMobileRechargeApp mobileRechargeApp;
		private readonly IBillPaymentApp billPaymentApp;
		private readonly ICardsService cardsService;
		private readonly IBankTransferService bankTransferService;

		public CatalogExtraServicesController(IMobileRechargeApp mobileRechargeApp, IBillPaymentApp billPaymentApp,
				ICardsService cardsService, IBankTransferService bankTransferService)
		{
			this.mobileRechargeApp = mobileRechargeApp;
			this.billPaymentApp = billPaymentApp;
			this.cardsService = cardsService;
			this.bankTransferService = bankTransferService;
		}

		#region Recarga de Celular

		[HttpGet("mobiles/ddds/{ddd}/operators")]
		public async Task<ApiReturn<List<PartnerOperator>>> GetAvailableProviders(Guid campaignId, string ddd)
		{
			//
			return await ApiReturn<List<PartnerOperator>>.Execute(mobileRechargeApp.GetAvailableProvidersForDdd(campaignId, ddd));
		}

		[HttpGet("mobiles/ddds/{ddd}/operators/{operatorId}/options")]
		public async Task<ApiReturn<List<PartnerOperationOption>>> GetOperatorOptionsForDdd(Guid campaignId, string ddd, string operatorId)
		{
			return await ApiReturn<List<PartnerOperationOption>>.Execute(mobileRechargeApp.GetOperatorOptionsForDdd(campaignId, ddd, operatorId));
		}

		[HttpPost("mobiles/recharge/issue")]
		public async Task<ApiReturn<ExtraServiceOperationResult>> IssueRechargeMobile(Guid campaignId, [FromBody] MobileRechargeIssue rechargeIssue)
		{
			return await ApiReturn<ExtraServiceOperationResult>.Execute(mobileRechargeApp.IssueRechargeMobileTicket(campaignId, rechargeIssue));
		}

		[HttpPut("mobiles/recharge/confirm")]
		public async Task<ApiReturn<ExtraServiceConfirmationResult>> ConfirmRechargeMobile(Guid campaignId, [FromBody] MobileRechargeTicket rechargeTicket)
		{
			return await ApiReturn<ExtraServiceConfirmationResult>.Execute(mobileRechargeApp.ConfirmRechargeMobile(campaignId, rechargeTicket));
		}

		[HttpGet("mobiles/recharge/orders/{orderId}")]
		public async Task<ApiReturn<dynamic>> GetRechargeOrderById(Guid campaignId, Guid orderId)
		{
			return await ApiReturn.Execute(mobileRechargeApp.GetRechargeOrderById(campaignId, orderId));
		}

		#endregion

		#region Pagamento de Contas

		[HttpGet("bills/{barCode}/details")]
		public async Task<ApiReturn<BillDetails>> GetBillDetailsByBarCode(Guid campaignId, string barCode,
			[FromQuery] Guid uid, [FromQuery] Guid accountOperatorLoginId, [FromQuery] Guid accountOperatorId,
			[FromQuery] string userDocument = null, [FromQuery] string userAccountOperatorDocument = null,
			[FromQuery] bool skipBalanceValidation = false)
		{
			return await ApiReturn.Execute(billPaymentApp.GetBillDetails(campaignId, barCode, uid, userDocument,
				userAccountOperatorDocument, accountOperatorId, accountOperatorLoginId, skipBalanceValidation)
			);
		}

		[HttpPost("bills/issue")]
		public async Task<ApiReturn<ExtraServiceOperationResult>> IssueBillPaymentTicket(Guid campaignId, [FromBody] BillDetails bill)
		{
			return await ApiReturn.Execute(billPaymentApp.IssueBillPaymentTicket(campaignId, bill));
		}

		[HttpPut("bills/confirm")]
		public async Task<ApiReturn<ExtraServiceConfirmationResult>> ConfirmBillPaymentTicket(Guid campaignId, [FromBody] BillPaymentTicket ticket)
		{
			return await ApiReturn.Execute(billPaymentApp.ConfirmBillPaymentTicket(campaignId, ticket));
		}

		[HttpPost("billpayments/schedule")]
		public async Task<ApiReturn<dynamic>> ScheduleBillPayment(Guid campaignId, [FromBody] BillDetails billDetails)
		{
			return await ApiReturn.Execute(billPaymentApp.ScheduleBillPayment(campaignId, billDetails));
		}

		[HttpGet("billpayments/orders/{orderId}")]
		public async Task<ApiReturn<dynamic>> GetBillPaymentsOrderById(Guid campaignId, Guid orderId)
		{
			return await ApiReturn.Execute(billPaymentApp.GetBillPaymentsOrderById(campaignId, orderId));
		}

		#endregion

		#region Transferência Bancária

		[HttpGet("cashback/configurations")]
		[HttpGet("banktransfer/configurations")]
		public async Task<ApiReturn<dynamic>> GetBankTransferConfiguration(Guid campaignId)
		{
			return await ApiReturn.Execute(bankTransferService.GetBankTransferConfiguration(campaignId));
		}

		[HttpPost("cashback/orders/fees")]
		[HttpPost("banktransfer/orders/fees")]
		public async Task<ApiReturn<dynamic>> CalculateBankTransferOrderFees(Guid campaignId, [FromBody] dynamic order)
		{
			return await ApiReturn.Execute(bankTransferService.CalculateBankTransferOrderFees(campaignId, order));
		}

		[HttpPost("cashback/orders")]
		[HttpPost("banktransfer/orders")]
		public async Task<ApiReturn<dynamic>> CreateBankTransferOrder(Guid campaignId, [FromBody] dynamic order)
		{
			return await ApiReturn.Execute(bankTransferService.CreateBankTransferOrder(campaignId, order));
		}

		[HttpGet("cashback/orders/{orderId}")]
		[HttpGet("banktransfer/orders/{orderId}")]
		public async Task<ApiReturn<dynamic>> GetBankTransferOrderById(Guid campaignId, Guid orderId)
		{
			return await ApiReturn.Execute(bankTransferService.GetBankTransferOrderById(campaignId, orderId));
		}

		#endregion
	}
}
