﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Entities.Carts;
using Motivai.Catalogo.Domain.IApp;
using Motivai.Catalogs.Domain.Models.Orders;
using Motivai.SharedKernel.Domain.Model;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Catalogo.Api.Controllers
{
	[Produces("application/json")]
	[Route("catalogs/{campaignId}/orders")]
	public class OrderController : Controller
	{
		private readonly IOrderCatalogApp _orderCatalogApp;

		public OrderController(IOrderCatalogApp orderCatalogApp)
		{
			_orderCatalogApp = orderCatalogApp;
		}

		[HttpPost]
		public async Task<ApiReturn<Cart>> CreateOrder([FromBody] Cart cart)
		{
			return await ApiReturn<Cart>.Execute(_orderCatalogApp.CreateOrder(cart));
		}

		[HttpPost("sync")]
		public async Task<ApiReturn<Cart>> CreateOrderSync([FromBody] Cart cart)
		{
			return await ApiReturn.Execute(_orderCatalogApp.CreateOrderSync(cart));
		}

		[HttpGet("{orderId}")]
		public async Task<ApiReturn<Cart>> GetOrderById(Guid campaignId, Guid orderId, [FromQuery] Guid pid)
		{
			return await ApiReturn<Cart>.Execute(_orderCatalogApp.GetOrderById(orderId, campaignId, pid));
		}

		[HttpGet("{orderId}/resumed")]
		public async Task<ApiReturn<MasterOrderResumed>> FetchOrderResumed(Guid orderId)
		{
			return await ApiReturn<MasterOrderResumed>.Execute(_orderCatalogApp.FetchOrderResumed(orderId));
		}

		[HttpGet("{orderId}/childrenorders/{itemGrouperId}/vouchers")]
		public async Task<ApiReturn<List<dynamic>>> ConsultVoucherLink(Guid campaignId, Guid orderId, Guid itemGrouperId)
		{
			return await ApiReturn.Execute(_orderCatalogApp.ConsultLinkVouchers(campaignId, orderId, itemGrouperId));
		}

		[HttpPut("{orderId}/items/price")]
		public async Task<ApiReturn<bool?>> PriceFactoryItems([FromBody] FactoryOrderPrice order)
		{
			return await ApiReturn<bool?>.Execute(_orderCatalogApp.PriceFactoryItems(order));
		}

		[HttpPut("{orderId}/approve")]
		public async Task<ApiReturn<dynamic>> ApproveOrder(Guid campaignId, Guid orderId)
		{
			return await ApiReturn<dynamic>.Execute(_orderCatalogApp.ApproveOrder(campaignId, orderId));
		}

		[HttpPut("{orderId}/refuse")]
		public async Task<ApiReturn<dynamic>> RefuseOrder(Guid campaignId, Guid orderId)
		{
			return await ApiReturn<dynamic>.Execute(_orderCatalogApp.RefuseOrder(campaignId, orderId));
		}
	}
}
