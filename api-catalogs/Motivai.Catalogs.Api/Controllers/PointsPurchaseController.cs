using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.Catalogs.Domain.Entities.PointsPurchase;
using Motivai.Catalogs.Domain.IApp.PointsPurchase;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Model;
using Motivai.SharedKernel.Domain.Model.Structures;

namespace Motivai.Catalogs.Api.Controllers
{
    [Route("catalogs/{campaignId}/points-purchase")]
    public class PointsPurchaseController
    {
        private readonly IPointsPurchaseApp _pointsPurchaseApp;

        public PointsPurchaseController(IPointsPurchaseApp pointsPurchaseApp)
        {
            _pointsPurchaseApp = pointsPurchaseApp;
        }

        [HttpGet("options")]
        public async Task<ApiReturn<List<Entry<decimal, decimal>>>> GetPointsOptionsToBuy(Guid campaignId)
        {
            return await ApiReturn.Execute(_pointsPurchaseApp.GetPointsOptionsToBuy(campaignId));
        }

        [HttpGet("payments")]
        public async Task<ApiReturn<PaymentOptions>> CalculatePaymentInstallments(Guid campaignId, [FromQuery] decimal pointsNeeded = 0)
        {
            return await ApiReturn.Execute(_pointsPurchaseApp.GetPaymentOptionsForPoints(campaignId, pointsNeeded));
        }

        [HttpPost("payments")]
        public async Task<ApiReturn<bool>> IssuePointsPurchasePayment(Guid campaignId, [FromBody] PointsPurchaseOrder order)
        {
            return await ApiReturn.Execute(_pointsPurchaseApp.IssuePointsPurchasePayment(campaignId, order));
        }
    }
}