using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.Models.Marketing;
using Motivai.SharedKernel.Domain.Model;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Catalogs.Api.Controllers.Marketing
{
	[Route("campaigns/{campaignId}/pages/customscripts")]
    public class CampaignsPagesCustomScriptsController
    {
        private readonly ICampaignPagesCustomScriptApp campaignPagesCustomScriptApp;
        public CampaignsPagesCustomScriptsController(ICampaignPagesCustomScriptApp _campaignPagesCustomScriptApp)
        {
            this.campaignPagesCustomScriptApp = _campaignPagesCustomScriptApp;
        }

		[HttpGet]
        public async Task<ApiReturn<List<CampaignPagesCustomScriptModel>>> GetCampaignsScript(string campaignId)
		{
			return await ApiReturn<List<CampaignPagesCustomScriptModel>>.Execute(this.campaignPagesCustomScriptApp.GetCampaignsScript(campaignId));
		}
    }
}