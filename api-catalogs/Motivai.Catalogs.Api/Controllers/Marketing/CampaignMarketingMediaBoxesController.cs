using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.SharedKernel.Domain.Model;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Catalogs.Api.Controllers.Marketing
{
	[Route("catalogs/{campaignId}/mediaboxes")]
	public class CampaignMarketingMediaboxesController
	{
		private readonly IMarketingMediaboxApp marketingMediaboxApp;

		public CampaignMarketingMediaboxesController(IMarketingMediaboxApp marketingMediaboxApp)
		{
			this.marketingMediaboxApp = marketingMediaboxApp;
		}

		[HttpGet]
		public async Task<ApiReturn<List<MediaBox>>> GetMediaBoxes(Guid campaignId,
			[FromQuery] Guid uid, [FromQuery] Guid pid,
			[FromQuery] bool catalog, [FromQuery] bool site,
			[FromQuery] string departmentId, [FromQuery] string sitePages)
		{
			return await ApiReturn<List<MediaBox>>.Execute(marketingMediaboxApp.GetActiveMediaBoxes(campaignId, uid, pid, catalog, site, departmentId, sitePages));
		}

		#region Catálogo

		[HttpGet("home")]
		public async Task<ApiReturn<List<MediaBox>>> GetCatalogMediaboxesForHome(Guid campaignId, [FromQuery] Guid uid, [FromQuery] Guid pid)
		{
			return await ApiReturn<List<MediaBox>>.Execute(marketingMediaboxApp.GetCatalogMediaboxesForHome(campaignId, uid, pid));
		}

		[HttpGet("{departamentId}")]
		public async Task<ApiReturn<List<MediaBox>>> GetCatalogMediaboxesForDepartment(Guid campaignId, string departamentId, [FromQuery] Guid uid, [FromQuery] Guid pid)
		{
			return await ApiReturn<List<MediaBox>>.Execute(marketingMediaboxApp.GetCatalogMediaboxesForDepartment(campaignId, uid, pid, departamentId));
		}

		#endregion
	}
}