using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Domain.Model;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.Catalogs.Api.Controllers.Marketing
{
	[Route("catalogs/{campaignId}/specialshops")]
	public class CampaignMarketingSpecialShopsController
	{
		private readonly IMarketingSpecialshopApp marketingSpecialshopApp;

		public CampaignMarketingSpecialShopsController(IMarketingSpecialshopApp marketingSpecialshopApp)
		{
			this.marketingSpecialshopApp = marketingSpecialshopApp;
		}

		[HttpGet]
		public async Task<ApiReturn<List<SpecialShopModel>>> GetActiveSpecialShopsByCampaign(Guid campaignId,
			[FromQuery] Guid pid, [FromQuery] CampaignParametrizationOrigin? origin)
		{
			return await ApiReturn<List<SpecialShopModel>>.Execute(marketingSpecialshopApp.GetActiveSpecialShopsByCampaign(campaignId, pid, origin));
		}

		[HttpGet("{specialShopId}")]
		public async Task<ApiReturn<SpecialShopModel>> GetActiveSpecialShopById(Guid campaignId, Guid specialShopId,
			[FromQuery] Guid uid, [FromQuery] Guid pid, [FromQuery] CampaignParametrizationOrigin origin, [FromQuery] Boolean shouldConsultSkuAvailaibilityAtPartner)
		{
			return await ApiReturn<SpecialShopModel>.Execute(marketingSpecialshopApp.GetActiveSpecialShopById(campaignId, specialShopId, uid, pid, origin, shouldConsultSkuAvailaibilityAtPartner));
		}

		[HttpGet("random")]
		public async Task<ApiReturn<SpecialShopModel>> GetRandomActiveSpecialShopByCampaign(Guid campaignId, [FromQuery] Guid pid)
		{
			return await ApiReturn<SpecialShopModel>.Execute(marketingSpecialshopApp.GetRandomActiveSpecialShopByCampaign(campaignId, pid));
		}
	}
}