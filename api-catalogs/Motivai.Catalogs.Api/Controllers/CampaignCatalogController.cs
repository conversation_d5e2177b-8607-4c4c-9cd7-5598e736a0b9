using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.Catalogo.Domain.IApp.CampaignCatalog;
using Motivai.Catalogs.Domain.IApp.ContactUs;
using Motivai.Catalogs.Domain.IApp.Marketing;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.Catalogs.Domain.Models.Catalog;
using Motivai.Catalogs.Domain.Models.Product;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Model;

using Newtonsoft.Json.Linq;

namespace Motivai.Catalogs.Api.Controllers
{
    [Route("catalogs")]
    public class CampaignCatalogController
    {
        private readonly ICampaignCatalogApp _catalogApp;
        private readonly IMarketingApp _marketingApp;
        private readonly ICampaignContactApp _contactApp;

        public CampaignCatalogController(ICampaignCatalogApp catalogApp, IMarketingApp marketingApp,
            ICampaignContactApp contactApp)
        {
            this._catalogApp = catalogApp;
            this._marketingApp = marketingApp;
            this._contactApp = contactApp;
        }

        [HttpGet("campaigns/id")]
        public async Task<ApiReturn<Guid>> GetCampaignIdByDomain([FromQuery] string domain)
        {
            return await ApiReturn<Guid>.Execute(_catalogApp.GetCampaignIdByDomain(domain));
        }

        [HttpGet("{campaignId}/theme")]
        public async Task<ApiReturn<Guid>> GetThemeFolderByCampaign(Guid campaignId)
        {
            return await ApiReturn<Guid>.Execute(_marketingApp.GetThemeFolderByCampaign(campaignId));
        }

        [HttpGet("{campaignId}/coinname")]
        public async Task<ApiReturn<CoinName>> GetCoinNameByCampaign(Guid campaignId)
        {
            return await ApiReturn<CoinName>.Execute(_catalogApp.GetCoinNameByCampaign(campaignId));
        }

        [HttpGet("{campaignId}/menu")]
        public async Task<ApiReturn<JArray>> GetMenuByCampaign(Guid campaignId)
        {
            return await ApiReturn<JArray>.Execute(_marketingApp.GetCampaignMenu(campaignId));
        }

        [HttpGet("{campaignId}/customization")]
        public async Task<ApiReturn<CampaignCustomization>> GetCampaignCustomization(Guid campaignId)
        {
            return await ApiReturn<CampaignCustomization>.Execute(_marketingApp.GetCampaignCustomization(campaignId));
        }

        [HttpGet("{campaignId}/settings")]
        public async Task<ApiReturn<CampaignSettingsModel>> GetSettingsByCampaign(Guid campaignId)
        {
            return await ApiReturn<CampaignSettingsModel>.Execute(_catalogApp.GetSettingsByCampaign(campaignId));
        }

        [HttpGet("{campaignId}/settings/pages")]
        public async Task<ApiReturn<CampaignCatalogSettings>> GetPagesSettingsByCampaign(Guid campaignId)
        {
            return await ApiReturn<CampaignCatalogSettings>.Execute(_catalogApp.GetPagesSettingsByCampaign(campaignId));
        }

        [HttpGet("{campaignId}/settings/footer")]
        public async Task<ApiReturn<CampaignCatalogSettingsFooter>> GetFooterSettingsByCampaign(Guid campaignId)
        {
            return await ApiReturn<CampaignCatalogSettingsFooter>.Execute(_catalogApp.GetFooterSettingsByCampaign(campaignId));
        }


        [HttpGet("{campaignId}/institutional/regulation")]
        public async Task<ApiReturn<dynamic>> GetRegulationByCampaign(Guid campaignId, [FromQuery] Guid? uid)
        {
            return await ApiReturn<dynamic>.Execute(_catalogApp.GetRegulationByCampaign(campaignId, uid));
        }

        [HttpGet("{campaignId}/institutional/privacypolicy")]
        public async Task<ApiReturn<dynamic>> GetPrivacyPolicyByCampaign(Guid campaignId, [FromQuery] Guid? uid)
        {
            return await ApiReturn<dynamic>.Execute(_catalogApp.GetPrivacyPolicyByCampaign(campaignId, uid));
        }

        [HttpGet("{campaignId}/institutional/shippingPolicy")]
        public async Task<ApiReturn<dynamic>> GetShippingPolicyByCampaign(Guid campaignId, [FromQuery] Guid? uid)
        {
            return await ApiReturn<dynamic>.Execute(_catalogApp.GetShippingPolicyByCampaign(campaignId, uid));
        }

        [HttpGet("{campaignId}/institutional/faqs")]
        public async Task<ApiReturn<List<dynamic>>> GetFaqsByCampaign(Guid campaignId, [FromQuery] Guid? uid, [FromQuery] string term = null)
        {
            return await ApiReturn<List<dynamic>>.Execute(_catalogApp.GetFaqsByCampaign(campaignId, uid, term));
        }

        [HttpGet("{campaignId}/communications/{communicationId}")]
        public async Task<ApiReturn<MediaBox>> GetCampaignCommunicationById(Guid campaignId, Guid communicationId, [FromQuery] Guid uid, [FromQuery] Guid pid)
        {
            return await ApiReturn<MediaBox>.Execute(_marketingApp.GetCampaignCommunicationById(campaignId, communicationId, pid));
        }

        [HttpGet("{campaignId}/home/<USER>")]
        public async Task<ApiReturn<List<ProductShowcaseModel>>> GetHomeProductsByCampaign(Guid campaignId, [FromQuery] Guid uid,
                [FromQuery] Guid pid, [FromQuery] decimal? balance = null, [FromQuery] int? size = 0, [FromQuery] bool? random = false)
        {
            return await ApiReturn<List<ProductShowcaseModel>>.Execute(_catalogApp.GetHomeProductsByCampaign(campaignId, uid, pid, balance, size, random));
        }

        [HttpGet("{campaignId}/contact/subjects")]
        [HttpGet("{campaignId}/contactus/subjects")]
        public async Task<ApiReturn<List<dynamic>>> GetContactSubjectsByCampaign(Guid campaignId)
        {
            return await ApiReturn<List<dynamic>>.Execute(_contactApp.GetContactUsSubjects(campaignId));
        }

        [HttpPost("{campaignId}/contact")]
        [HttpPost("{campaignId}/contactus/form")]
        public async Task<ApiReturn<bool>> SendContactUsFormMessage(Guid campaignId, [FromBody] ContactUsModel model)
        {
            return await ApiReturn.Execute(_contactApp.SendContactUsFormMessage(campaignId, model));
        }
    }
}