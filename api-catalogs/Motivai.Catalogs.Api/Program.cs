using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using NLog.Web;

namespace Motivai.Catalogs.Api {
    public class Program {
        public static void Main(string[] args) {
            try {
                BuildWebHost(args).Build().Run();
            } catch (Exception) {
                throw;
            } finally {
                NLog.LogManager.Shutdown();
            }
        }

        private static IWebHostBuilder BuildWebHost(string[] args) {
            return new WebHostBuilder()
                .UseKestrel()
                .UseContentRoot(Directory.GetCurrentDirectory())
                .UseIISIntegration()
                .UseStartup<Startup>()
                // .UseApplicationInsights()
                .UseNLog();
        }
    }
}