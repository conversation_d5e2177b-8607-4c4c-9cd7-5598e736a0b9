﻿{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Information",
      "System": "Warning",
      "Microsoft": "Warning"
    }
  },
  "AWS_X_API_KEY": "",
  "ApiGeneralSettings": "",
  "ApiUsuario": "",
  "ApiCampanha": "",
  "ApiCorreios": "",
  "ApiTransacao" : "",
  "ApiProduto": "",
  "ApiCategories": "",
  "ApiIntegracao": "",
  "ApiElasticSearch": "",
  "ApiEmail": "",
  "ApiEmpresa": "",
  "ApiOrders": "",
  "ApiPagamento": "",
  "ApiRegions": "",
  "ApiFactories": "",
  "ApiCatalogServicesIntegrations": "",
  "CampaignsExtraServicesApi": "",
  "PricesApi": "",
  "CardsApi": "",
  "CashbackApi": "",
  "ApiSms": "",
  "MarketplaceOrdersIntegrationsApi": "",
  "REDIS_CONNECTION_STRING": "",
  "MONGODB_URI": "",
  "MONGODB_DATABASE": ""
}
